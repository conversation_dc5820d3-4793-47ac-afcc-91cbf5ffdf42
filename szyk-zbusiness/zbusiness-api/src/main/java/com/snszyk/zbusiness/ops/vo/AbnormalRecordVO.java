/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.snszyk.zbusiness.ops.entity.AbnormalRecord;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 设备异常明细表视图实体类
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AbnormalRecordVO对象", description = "设备异常明细表")
public class AbnormalRecordVO extends AbnormalRecord {
	private static final long serialVersionUID = 1L;

	public AbnormalRecordVO() {
		super();
	}

	public AbnormalRecordVO(Long waveId) {
		super();
		this.setWaveId(waveId);
	}

	public AbnormalRecordVO(Integer strategyType, Integer abnormalType, Date abnormalTime) {
		super();
		this.setStrategyType(strategyType);
		this.setAbnormalType(abnormalType);
		this.setAbnormalTime(abnormalTime);
	}

}
