package com.snszyk.zbusiness.basic.config;

import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.zbusiness.basic.dto.HealthIndex;
import com.snszyk.zbusiness.ops.enums.AlarmLevelEnum;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Objects;


@Slf4j
public class HealthIndexConfig {
	private static final Integer FAULT_MOUNT_ONE = 1;
	private static final Integer FAULT_MOUNT_THREE = 3;
	private static final Integer FAULT_MOUNT_FIVE = 5;
	/**
	 * 获取设备的健康指数
	 * @return
	 */
	public static HealthIndex getHealthIndex(Long id, Integer lifeExpectancy, Date startDateOfUse, Integer alarmLevel) {

		//1、报警等级得分
		int alarmIndex = 40;
		switch (Objects.requireNonNull(AlarmLevelEnum.getByCode(alarmLevel))) {
			case LEVEL_ONE:
				alarmIndex = 30;
				break;
			case LEVEL_TWO:
				alarmIndex = 20;
				break;
			case LEVEL_THREE:
				alarmIndex = 10;
				break;
			case LEVEL_FOUR:
				alarmIndex = 5;
				break;
			default:
				log.debug("设备无报警。");
				break;
		}

		//2、设备使用年限得分
		int lifeIndex = 30;
		BigDecimal usePercentage = BigDecimal.ZERO;
		if (lifeExpectancy > 0 && startDateOfUse != null) {
			//已使用天数
			long useDays = DateUtil.between(startDateOfUse, DateUtil.now()).toDays();
			usePercentage = BigDecimal.valueOf(useDays)
				.divide(BigDecimal.valueOf(lifeExpectancy), 2, RoundingMode.HALF_UP);
			if (usePercentage.compareTo(BigDecimal.valueOf(0.75)) >= 0) {
				lifeIndex = 5;
			} else if (usePercentage.compareTo(BigDecimal.valueOf(0.50)) >= 0) {
				lifeIndex = 10;
			} else if (usePercentage.compareTo(BigDecimal.valueOf(0.25)) >= 0) {
				lifeIndex = 20;
			}
		} else {
			log.warn("getHealthIndex() - 设备暂无使用年限数据。");
		}

		//3、故障记录得分
		int faultIndex = 30;
		Integer faultAmount = 0;
		//健康指数 & 详情
		HealthIndex healthIndex = new HealthIndex();
		healthIndex.setHealthIndex(alarmIndex + lifeIndex + faultIndex)
			.setAlarmIndex(alarmIndex)
			.setAlarmLevel(alarmLevel)
			.setLifeIndex(lifeIndex)
			.setUsePercentage(usePercentage.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP))
			.setFaultIndex(faultIndex)
			.setFaultAmount(faultAmount);
		return healthIndex;
	}
}
