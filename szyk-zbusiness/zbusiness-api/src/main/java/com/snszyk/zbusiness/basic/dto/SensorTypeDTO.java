/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.SensorType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 传感器类型表数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorTypeDTO对象", description = "传感器类型表")
public class SensorTypeDTO extends SensorType {

	/**
	 * 是否已生成传感器实例（0：未生成；1：已生成，不允许编辑。）
	 */
	@ApiModelProperty(value = "是否已生成传感器实例（0：未生成；1：已生成，不允许编辑。）")
	private Integer hasSensorInstance;

	/**
	 * 生产厂家名称
	 */
	@ApiModelProperty(value = "生产厂家名称")
	private String supplierName;

	/**
	 * 传感器类型名称
	 */
	@ApiModelProperty(value = "传感器类型名称")
	private String categoryName;

	/**
	 * 传感器类型参数列表
	 */
	@ApiModelProperty(value = "传感器类型参数列表")
	private List<SensorTypeParamDTO> sensorTypeParamList;

}
