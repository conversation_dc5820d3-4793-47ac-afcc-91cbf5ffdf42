package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class EquipmentSensorDTO implements Serializable {

	public static SzykRedis szykRedis;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;
	private String name;
	private String code;
	private Integer online;

	private Integer spower;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long paramId;

	public String getOnline() {
		Integer online = (Integer) szykRedis.get(code + StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode());
		if (online == null || online <= 0) {
			return null;
		}
		return online.toString();
	}

	public String getSpower() {
		String data = szykRedis.get(code + StringPool.COLON + paramId + StringPool.COLON + SampledDataTypeEnum.SENSOR_POWER.getCode());
		if (data == null) {
			return null;
		}
		BigDecimal power = new BigDecimal(data);
		if (power.intValue() <= 0) {
			return null;
		}
		return power.toPlainString();
	}
}
