/*
 *      Copyright (c) 2018-2028
 */

package com.snszyk.system.config;

import lombok.AllArgsConstructor;
import com.snszyk.system.event.ApiLogListener;
import com.snszyk.system.event.ErrorLogListener;
import com.snszyk.system.event.UsualLogListener;
import com.snszyk.core.launch.props.SzykProperties;
import com.snszyk.core.launch.server.ServerInfo;
import com.snszyk.system.service.ILogService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 日志工具自动配置
 *
 * <AUTHOR>
 */
@Configuration
@AllArgsConstructor
public class SzykLogConfiguration {

	private final ILogService logService;
	private final ServerInfo serverInfo;
	private final SzykProperties szykProperties;

	@Bean(name = "apiLogListener")
	public ApiLogListener apiLogListener() {
		return new ApiLogListener(logService, serverInfo, szykProperties);
	}

	@Bean(name = "errorEventListener")
	public ErrorLogListener errorEventListener() {
		return new ErrorLogListener(logService, serverInfo, szykProperties);
	}

	@Bean(name = "usualEventListener")
	public UsualLogListener usualEventListener() {
		return new UsualLogListener(logService, serverInfo, szykProperties);
	}

}
