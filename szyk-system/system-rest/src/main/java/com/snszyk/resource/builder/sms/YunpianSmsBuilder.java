/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.builder.sms;

import com.yunpian.sdk.YunpianClient;
import lombok.SneakyThrows;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.sms.SmsTemplate;
import com.snszyk.core.sms.props.SmsProperties;
import com.snszyk.core.sms.YunpianSmsTemplate;
import com.snszyk.resource.entity.Sms;

/**
 * 云片短信构建类
 *
 * <AUTHOR>
 */
public class YunpianSmsBuilder {

	@SneakyThrows
	public static SmsTemplate template(Sms sms, SzykRedis szykRedis) {
		SmsProperties smsProperties = new SmsProperties();
		smsProperties.setTemplateId(sms.getTemplateId());
		smsProperties.setAccessKey(sms.getAccessKey());
		smsProperties.setSignName(sms.getSignName());
		YunpianClient client = new YunpianClient(smsProperties.getAccessKey()).init();
		return new YunpianSmsTemplate(smsProperties, client, szykRedis);
	}

}
