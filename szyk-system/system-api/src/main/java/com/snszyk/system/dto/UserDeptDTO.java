/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
public class UserDeptDTO {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "员工名称")
	private String employeeName;

	@ApiModelProperty(value = "组织名称")
	private String orgName;

	@ApiModelProperty(value = "部门名称")
	private String deptName;

	@ApiModelProperty(value = "人员头像")
	private String avatar;

}
