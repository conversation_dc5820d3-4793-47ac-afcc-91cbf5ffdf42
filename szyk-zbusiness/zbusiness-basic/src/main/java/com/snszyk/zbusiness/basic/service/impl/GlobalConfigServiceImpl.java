/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.entity.GlobalConfig;
import com.snszyk.zbusiness.basic.enums.GlobalConfigCategoryEnum;
import com.snszyk.zbusiness.basic.mapper.GlobalConfigMapper;
import com.snszyk.zbusiness.basic.service.IGlobalConfigService;
import com.snszyk.zbusiness.basic.vo.AbnormalStrategyConfigVO;
import com.snszyk.zbusiness.basic.vo.AlarmPeriodConfigVO;
import com.snszyk.zbusiness.basic.vo.GlobalConfigVO;
import com.snszyk.zbusiness.basic.vo.LubricateStrategyConfigVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 全局配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-12
 */
@AllArgsConstructor
@Service
public class GlobalConfigServiceImpl extends BaseServiceImpl<GlobalConfigMapper, GlobalConfig> implements IGlobalConfigService {

	private final SzykRedis szykRedis;
	private static final String KEY_ALARM_PERIOD = "config:alarm:period";
	private static final String KEY_LUBRICATE_RECOMMENDED_RULE = "config:lubricate:rule";
	private static final String KEY_LUBRICATE_RECOMMENDED_FREQ = "config:lubricate:freq";
	private static final String KEY_ABNORMAL_CONTINUOUS_STRATEGY = "config:abnormal:continuous:strategy";
	private static final String KEY_ABNORMAL_DISCONTINUOUS_STRATEGY = "config:abnormal:discontinuous:strategy";
	private static final String KEY_ABNORMAL_TREND_STRATEGY = "config:abnormal:trend:strategy";

	@Override
	public GlobalConfigVO detail(String tenantId) {
		List<GlobalConfig> list = this.list(Wrappers.<GlobalConfig>query().lambda()
			.eq(GlobalConfig::getTenantId, tenantId));
		GlobalConfigVO configVO = new GlobalConfigVO(tenantId);
		if(Func.isNotEmpty(list)){
			list.forEach(globalConfig -> {
				if(GlobalConfigCategoryEnum.ALARM_PERIOD
					== GlobalConfigCategoryEnum.getByCode(globalConfig.getCategory())){
					configVO.setAlarmPeriodConfig(JSONUtil.toBean(globalConfig.getSettings(), AlarmPeriodConfigVO.class));
				}
				if(GlobalConfigCategoryEnum.LUBRICATE_STRATEGY
					== GlobalConfigCategoryEnum.getByCode(globalConfig.getCategory())){
					configVO.setLubricateStrategyConfig(JSONUtil.toBean(globalConfig.getSettings(), LubricateStrategyConfigVO.class));
				}
				if(GlobalConfigCategoryEnum.ABNORMAL_STRATEGY
					== GlobalConfigCategoryEnum.getByCode(globalConfig.getCategory())){
					configVO.setAbnormalStrategyConfig(JSONUtil.toBean(globalConfig.getSettings(), AbnormalStrategyConfigVO.class));
				}
			});
		}
		return configVO;
	}

	@Override
	public boolean submit(GlobalConfigVO vo) {
		String tenantId = vo.getTenantId();

		List<GlobalConfig> list = new ArrayList<>();
		GlobalConfig globalConfig;
		if(Func.isNotEmpty(vo.getAlarmPeriodConfig())){
			globalConfig = this.getOne(Wrappers.<GlobalConfig>query().lambda()
				.eq(GlobalConfig::getCategory, GlobalConfigCategoryEnum.ALARM_PERIOD.getCode()).eq(GlobalConfig::getTenantId, tenantId));
			if(Func.isNotEmpty(globalConfig)){
				globalConfig.setSettings(JSONUtil.toJsonStr(vo.getAlarmPeriodConfig()));
			} else {
				globalConfig = new GlobalConfig(tenantId, GlobalConfigCategoryEnum.ALARM_PERIOD.getCode(),
					JSONUtil.toJsonStr(vo.getAlarmPeriodConfig()));
				globalConfig.setCategory(GlobalConfigCategoryEnum.ALARM_PERIOD.getCode());
			}
			list.add(globalConfig);
			szykRedis.set(AuthUtil.getTenantId().concat(":").concat(KEY_ALARM_PERIOD), vo.getAlarmPeriodConfig().getPeriod());
		} else {
			szykRedis.del(AuthUtil.getTenantId().concat(":").concat(KEY_ALARM_PERIOD));
		}
		if(Func.isNotEmpty(vo.getLubricateStrategyConfig())){
			globalConfig = this.getOne(Wrappers.<GlobalConfig>query().lambda()
				.eq(GlobalConfig::getCategory, GlobalConfigCategoryEnum.LUBRICATE_STRATEGY.getCode()).eq(GlobalConfig::getTenantId, tenantId));
			if(Func.isNotEmpty(globalConfig)){
				globalConfig.setSettings(JSONUtil.toJsonStr(vo.getLubricateStrategyConfig()));
			} else {
				globalConfig = new GlobalConfig(tenantId, GlobalConfigCategoryEnum.LUBRICATE_STRATEGY.getCode(),
					JSONUtil.toJsonStr(vo.getLubricateStrategyConfig()));
				globalConfig.setCategory(GlobalConfigCategoryEnum.LUBRICATE_STRATEGY.getCode());
			}
			list.add(globalConfig);
			if(Func.isNotEmpty(vo.getLubricateStrategyConfig().getRecommendedRule())){
				szykRedis.set(AuthUtil.getTenantId().concat(":").concat(KEY_LUBRICATE_RECOMMENDED_RULE),
					vo.getLubricateStrategyConfig().getRecommendedRule());
			} else {
				szykRedis.del(AuthUtil.getTenantId().concat(":").concat(KEY_LUBRICATE_RECOMMENDED_RULE));
			}
			if(Func.isNotEmpty(vo.getLubricateStrategyConfig().getRecommendedFreq())){
				szykRedis.set(AuthUtil.getTenantId().concat(":").concat(KEY_LUBRICATE_RECOMMENDED_FREQ),
					vo.getLubricateStrategyConfig().getRecommendedFreq());
			} else {
				szykRedis.del(AuthUtil.getTenantId().concat(":").concat(KEY_LUBRICATE_RECOMMENDED_FREQ));
			}
		}
		if(Func.isNotEmpty(vo.getAbnormalStrategyConfig())){
			globalConfig = this.getOne(Wrappers.<GlobalConfig>query().lambda()
				.eq(GlobalConfig::getCategory, GlobalConfigCategoryEnum.ABNORMAL_STRATEGY.getCode()).eq(GlobalConfig::getTenantId, tenantId));
			if(Func.isNotEmpty(globalConfig)){
				globalConfig.setSettings(JSONUtil.toJsonStr(vo.getAbnormalStrategyConfig()));
			} else {
				globalConfig = new GlobalConfig(tenantId, GlobalConfigCategoryEnum.ABNORMAL_STRATEGY.getCode(),
					JSONUtil.toJsonStr(vo.getAbnormalStrategyConfig()));
				globalConfig.setCategory(GlobalConfigCategoryEnum.ABNORMAL_STRATEGY.getCode());
			}
			list.add(globalConfig);
			if(Func.isNotEmpty(vo.getAbnormalStrategyConfig().getContinuousStrategy())){
				szykRedis.set(AuthUtil.getTenantId().concat(":").concat(KEY_ABNORMAL_CONTINUOUS_STRATEGY),
					JSONUtil.toJsonStr(vo.getAbnormalStrategyConfig().getContinuousStrategy()));
			} else {
				szykRedis.del(AuthUtil.getTenantId().concat(":").concat(KEY_ABNORMAL_CONTINUOUS_STRATEGY));
			}
			if(Func.isNotEmpty(vo.getAbnormalStrategyConfig().getDiscontinuousStrategy())){
				szykRedis.set(AuthUtil.getTenantId().concat(":").concat(KEY_ABNORMAL_DISCONTINUOUS_STRATEGY),
					JSONUtil.toJsonStr(vo.getAbnormalStrategyConfig().getDiscontinuousStrategy()));
			} else {
				szykRedis.del(AuthUtil.getTenantId().concat(":").concat(KEY_ABNORMAL_DISCONTINUOUS_STRATEGY));
			}
			if(Func.isNotEmpty(vo.getAbnormalStrategyConfig().getTrendStrategy())){
				szykRedis.set(AuthUtil.getTenantId().concat(":").concat(KEY_ABNORMAL_TREND_STRATEGY),
					JSONUtil.toJsonStr(vo.getAbnormalStrategyConfig().getTrendStrategy()));
			} else {
				szykRedis.del(AuthUtil.getTenantId().concat(":").concat(KEY_ABNORMAL_TREND_STRATEGY));
			}
		}
		return this.saveOrUpdateBatch(list);
	}

	@Override
	public GlobalConfigVO getSetting(String tenantId, String category) {
		GlobalConfig globalConfig = this.getOne(Wrappers.<GlobalConfig>query().lambda().eq(GlobalConfig::getCategory, category)
			.eq(GlobalConfig::getTenantId, tenantId));
		GlobalConfigVO vo = Objects.requireNonNull(BeanUtil.copy(globalConfig, GlobalConfigVO.class));
		switch (GlobalConfigCategoryEnum.getByCode(category)) {
			case ALARM_PERIOD:
				AlarmPeriodConfigVO alarmPeriodConfig = JSONUtil.toBean(globalConfig.getSettings(), AlarmPeriodConfigVO.class);
				vo.setAlarmPeriodConfig(alarmPeriodConfig);
				break;
			case LUBRICATE_STRATEGY:
				LubricateStrategyConfigVO lubricateStrategyConfig = JSONUtil.toBean(globalConfig.getSettings(), LubricateStrategyConfigVO.class);
				vo.setLubricateStrategyConfig(lubricateStrategyConfig);
				break;
			case ABNORMAL_STRATEGY:
				AbnormalStrategyConfigVO abnormalStrategyConfig = JSONUtil.toBean(globalConfig.getSettings(), AbnormalStrategyConfigVO.class);
				vo.setAbnormalStrategyConfig(abnormalStrategyConfig);
				break;
			default:
		}
		return vo;
	}

}
