package com.snszyk.system.dto;

import com.snszyk.resource.entity.Attach;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台logo dto
 *
 * <AUTHOR>
 * @since 2023/5/10 15:07
 **/
@Data
public class LogoDTO {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "主键")
	private Long id;

	@ApiModelProperty(value = "平台名称")
	private String platformName;

	@ApiModelProperty(value = "平台简称")
	private String shortName;

	@ApiModelProperty(value = "公司名称")
	private String companyName;

	@ApiModelProperty(value = "logo")
	private Attach attach;
}
