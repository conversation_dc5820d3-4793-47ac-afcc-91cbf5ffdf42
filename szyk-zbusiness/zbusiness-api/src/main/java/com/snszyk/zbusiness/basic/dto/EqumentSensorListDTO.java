package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 传感器实例对应的传感器
 */
@Data
public class EqumentSensorListDTO implements Serializable {

	@ApiModelProperty(value = "设备名称")
	private String name;
	@ApiModelProperty(value = "传感器列表")
	private List<String> sensorList = new ArrayList<>();
	private List<String> onlineSensorList = new ArrayList<>();
	@ApiModelProperty(value = "数量")
	private int num;
	@ApiModelProperty(value = "在线数量")
	private int onlineCount;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	@ToString.Exclude
	private String sensorCode;

	@JsonInclude(JsonInclude.Include.NON_NULL)
	@ToString.Exclude
	private String sensorId;

	public void configOnline(SzykRedis szykRedis) {
		String[] list = sensorCode.split(",");
		for (String key : list) {
			Object dt = szykRedis.get(key + StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode());
			if (dt == null) {
				continue;
			}
			String obj = String
				.valueOf(dt);
			// if (obj == null) {
			// continue;
			// }
			Integer online = Integer.parseInt(obj);
			sensorList.add(key);
			if (online == 0) {
				continue;
			}
			onlineCount++;
			onlineSensorList.add(key);
		}
	}

}
