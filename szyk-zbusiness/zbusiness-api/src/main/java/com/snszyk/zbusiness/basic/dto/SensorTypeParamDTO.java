/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.SensorTypeParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 传感器类型参数表数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorTypeParamDTO对象", description = "传感器类型参数表")
public class SensorTypeParamDTO extends SensorTypeParam {

	/**
	 * 振动类型名称
	 */
	@ApiModelProperty(value = "振动类型名称")
	private String vibrationTypeName;

	/**
	 * 采样数据类型名称
	 */
	@ApiModelProperty(value = "采样数据类型名称")
	private String sampleDataTypeName;

	/**
	 * 轴方向名称
	 */
	@ApiModelProperty(value = "轴方向名称")
	private String axialDirectionName;

	/**
	 * 默认特征值名称（多个用英文逗号分隔）
	 */
	@ApiModelProperty(value = "默认特征值名称（多个用英文逗号分隔）")
	private String defaultFeaturesName;

	/**
	 * 是否可以编辑
	 */
	@ApiModelProperty(value = "是否可以编辑")
	private Boolean canEdit;

}
