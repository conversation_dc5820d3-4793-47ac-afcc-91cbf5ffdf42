package com.snszyk.zbusiness.basic.rabbit.bussiness;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.DingTalkMessageVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.entity.User;
import com.snszyk.system.service.IUserSearchService;
import com.snszyk.zbusiness.basic.config.SidasBizConfig;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceParamDTO;
import com.snszyk.zbusiness.basic.dto.SensorOfflineDTO;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.CommandBusiness;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.IMonitorService;
import com.snszyk.zbusiness.basic.service.ISensorInstanceParamService;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.IWaveService;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 采样数据异常报警
 *
 * <AUTHOR>
 * @date 2024/05/07 13:36
 **/
@Slf4j
@Component
@AllArgsConstructor
public class SampleDataAlarmBusiness implements CommandBusiness {

	private final ISensorInstanceService sensorInstanceService;
	private final ISensorInstanceParamService sensorInstanceParamService;
	private final IMonitorService monitorService;
	private final IWaveService waveService;
	private final MessageLogicService messageLogicService;
	private final IUserSearchService userSearchService;
	private final RabbitTemplate rabbitTemplate;
	private final SzykRedis szykRedis;
	private final InfluxdbTools influxdbTools;
	private final SidasBizConfig sidasBizConfig;
	private static final String[] SAMPLE_DATA_TYPE = {"ACCELERATION", "VELOCITY", "DISPLACEMENT"};

	@Override
	public String getCommand() {
		//return Command.VIBRATE_COMMAND;
		return null;
	}

	@Override
	public void business(MessageBean message) {
		// 仅加速度、速度、位移类型判断采样数据异常
		if (!ArrayUtils.contains(SAMPLE_DATA_TYPE, SampledDataTypeEnum.getByCode(message.getType()).getCode())) {
			return;
		}
		log.info("传感器采样数据异常—输入数据：=============={}", message);
		Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
			.eq(Wave::getMonitorId, message.getMonitorId()).eq(Wave::getSensorCode, message.getId())
			.eq(Wave::getSampleDataType, message.getType()).eq(Wave::getSensorInstanceParamId, message.getSensorInstanceParamId())
			.eq(Wave::getUnbind, 0));
		if (wave == null) {
			log.info("当前传感器没有波形，无法判断采样数据是否异常：=============={}", message.getId() + StringPool.COLON + message.getType());
		}
		String sensorOnline = SampledDataTypeEnum.SENSOR_ONLINE.getCode();
		String sensorCode = message.getId();
		log.info("开始判断当前传感器是否采样数据异常==============波形：{}，传感器编码：{}，采样时间：{}", wave.getId(), sensorCode,
			DateUtil.formatDateTime(new Date(message.getOriginTime())));
		// 查询传感器参数id
		SensorInstanceParamDTO stateParam = sensorInstanceParamService.getStateParam(sensorCode, sensorOnline);
		if (stateParam == null) {
			log.warn("StateSaveRedisBusiness.business() - 没找到对应的传感器实例参数！sensorCode = {}", sensorCode);
			return;
		}
		String onlineStateKey = sensorCode + StringPool.COLON + sensorOnline;
		Monitor monitor = monitorService.getById(wave.getMonitorId());
		// 排除传感器离线的情况
		if (Func.isNotEmpty((Object) szykRedis.get(onlineStateKey))) {
			log.info("当前传感器在线状态：=============={}", onlineStateKey);
			SensorDataVO sensorData = new SensorDataVO(null, wave.getId(), null);
			JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
			List<JSONObject> list = influxdbTools.queryData(message.getOriginTime()
					- Func.toInt(sidasBizConfig.sidasBizProperties().getSampleDataExceptionTime()) * 60 * 60 * 1000,
				message.getOriginTime(), jsonObject);
			if (Func.isEmpty(list)) {
				this.sendAlarmMsg(monitor.getTenantId(), sensorCode, MessageBizTypeEnum.SAMPLE_DATA_EXCEPTION);
			}
		}
	}

	/**
	 * 发送sidas门户端待办消息
	 *
	 * @param tenantId
	 * @param sensorCode
	 * @param messageBizType
	 * @return void
	 * <AUTHOR>
	 * @date 2024/4/30 15:08
	 */
	private void sendAlarmMsg(String tenantId, String sensorCode, MessageBizTypeEnum messageBizType) {
		log.info("租户id：=============={}", tenantId);
		List<User> users = userSearchService.listAllUser(tenantId);
		if (Func.isNotEmpty(users)) {
			//传感器实例（测点路径）
			SensorInstanceDTO sensorInstance = sensorInstanceService.detailByCode(sensorCode);
			//发送待办消息
			MessageVo messageVo = new MessageVo();
			messageVo.setAppKey("SiDAs");
			messageVo.setTitle(messageBizType.getMessage() + "通知");
			SensorOfflineDTO messageContent = new SensorOfflineDTO()
				.setPathName(sensorInstance.getPathName().replace(StringPool.COMMA, StringPool.SLASH))
				.setSensorCode(sensorInstance.getCode());
			messageVo.setContent(JSONObject.toJSONString(messageContent));
			messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			messageVo.setBizType(messageBizType.getCode());
			messageVo.setBizId(sensorInstance.getId().toString());
			messageVo.setSender("SiDAs");
			messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			List<ReceiverInfoVo.UserVo> userVoList = users.stream().map(user -> {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(user.getId());
				userVo.setRealName(user.getRealName());
				return userVo;
			}).collect(Collectors.toList());
			receiverInfoVo.setUserList(userVoList);
			messageVo.setReceiverInfoVo(receiverInfoVo);
			log.info("发送" + messageBizType.getMessage() + "通知 - 接收人：{}", JSONObject.toJSONString(userVoList));
			R messageResult = messageLogicService.commitMessage(messageVo);
			;
			log.info("发送" + messageBizType.getMessage() + "通知 - 结果：{}", JSONObject.toJSONString(messageResult));
			// 发送钉钉消息
			log.info(messageBizType.getMessage() + "-发送钉钉消息：==================={}", sensorCode);
			Tenant tenant = SysCache.getTenant(tenantId);
			DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(tenant.getTenantId(),
				tenant.getTenantName(), sensorCode, messageBizType.getCode(), JSONUtil.toJsonStr(sensorInstance));
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
				EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE, dingTalkMessage);
		} else {
			log.warn("获取用户列表失败！code = {}, msg = {}.");
		}
	}

}
