/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.message.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.message.dto.MessageDto;
import com.snszyk.message.entity.Message;
import com.snszyk.message.mapper.MessageMapper;
import com.snszyk.message.service.IMessageService;
import com.snszyk.message.vo.MessageVo;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消息推送service
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@AllArgsConstructor
@Service
public class MessageServiceImpl extends BaseServiceImpl<MessageMapper, Message> implements IMessageService {


	/**
	 * 消息发送分页列表
	 * @param vo vo
	 * @return
	 */
	@Override
	public IPage<MessageDto> pageSend(IPage<MessageDto> page, MessageVo vo) {
		List<MessageDto> records = baseMapper.pageSend(page, vo);
		return page.setRecords(records);
	}

}
