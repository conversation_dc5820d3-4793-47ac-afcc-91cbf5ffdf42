/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.AiModelDTO;
import com.snszyk.zbusiness.basic.entity.AiModel;
import com.snszyk.zbusiness.basic.vo.AiModelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI模型表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface AiModelMapper extends BaseMapper<AiModel> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param aiModel
	 * @return
	 */
	List<AiModelDTO> page(IPage page, @Param("aiModel") AiModelVO aiModel);

	/**
	 * 设备关联AI模型
	 *
	 * @param sensorCode
	 * @return
	 */
	List<AiModelDTO> queryAiModelParams(@Param("sensorCode") String sensorCode);

}
