package com.snszyk.zbusiness.ops.job;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.message.entity.MessageSetting;
import com.snszyk.message.enums.*;
import com.snszyk.message.service.IMessageSettingService;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.ops.entity.Abnormal;
import com.snszyk.zbusiness.ops.entity.AbnormalRecord;
import com.snszyk.zbusiness.ops.enums.AbnormalStatusEnum;
import com.snszyk.zbusiness.ops.service.IAbnormalRecordService;
import com.snszyk.zbusiness.ops.service.IAbnormalService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 每日异常定时任务
 * 支持多租户配置，每个租户可以设置自己的消息发送时间
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Slf4j
public class DailyAbnormalXxlJob {

	private static final String MESSAGE_TITLE = "每日异常通知";
	/**
	 * Redis缓存键前缀
	 */
	private static final String CACHE_KEY_PREFIX = "daily_abnormal:executed:";

	/**
	 * 缓存过期时间：25小时（秒）
	 */
	private static final long CACHE_EXPIRE_SECONDS = 25 * 60 * 60;

	private final IAbnormalRecordService abnormalRecordService;
	private final IAbnormalService abnormalService;
	private final IEquipmentService equipmentService;
	private final IMessageSettingService messageSettingService;
	private final MessageLogicService messageLogicService;
	private final SzykRedis szykRedis;

	/**
	 * 每日异常定时任务
	 * 每30分钟执行一次（整点和半点），检查是否有需要发送的消息
	 * xxl-job配置：0 0,30 * * * ?
	 *
	 * @param param 参数，可以为空
	 * @return 执行结果
	 */
	@XxlJob("dailyAbnormalJobHandler")
	public ReturnT<String> dailyAbnormalJobHandler(String param) {
		XxlJobLogger.log("每日异常定时任务开始执行");
		log.info("每日异常定时任务开始执行");

		try {
			// 1. 获取所有租户的消息设置
			List<MessageSetting> messageSettings = messageSettingService.list(Wrappers.<MessageSetting>query().lambda()
				.eq(MessageSetting::getBizType, MessageBizTypeEnum.DAILY_ABNORMAL.getCode())
				.eq(MessageSetting::getEnabled, SzykConstant.DB_STATUS_NORMAL)); // 只获取已启用的配置

			if (messageSettings.isEmpty()) {
				XxlJobLogger.log("未找到已启用的每日异常消息设置，任务终止");
				log.warn("未找到已启用的每日异常消息设置，任务终止");
				return ReturnT.SUCCESS;
			}

			// 2. 查询当天的异常记录信息
			Date today = DateUtil.parse(DateUtil.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
			Date tomorrow = DateUtil.plusDays(today, 1);

			List<AbnormalRecord> allAbnormalRecordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
				.between(AbnormalRecord::getAbnormalTime, today, tomorrow));

			if (allAbnormalRecordList.isEmpty()) {
				XxlJobLogger.log("今日没有异常记录，无需发送通知");
				log.info("今日没有异常记录，无需发送通知");
				return ReturnT.SUCCESS;
			}

			// 3. 获取所有相关的异常信息（用于获取租户ID）
			Set<Long> abnormalIds = allAbnormalRecordList.stream()
				.map(AbnormalRecord::getAbnormalId)
				.collect(Collectors.toSet());

			List<Abnormal> abnormalList = abnormalService.listByIds(abnormalIds);
			Map<Long, Abnormal> abnormalMap = abnormalList.stream()
				.collect(Collectors.toMap(Abnormal::getId, abnormal -> abnormal));

			// 4. 按租户统计异常信息
			Map<String, AbnormalStatistics> tenantAbnormalStatMap = new HashMap<>();
			for (AbnormalRecord record : allAbnormalRecordList) {
				Abnormal abnormal = abnormalMap.get(record.getAbnormalId());
				if (abnormal == null) {
					continue;
				}

				String tenantId = abnormal.getTenantId();
				AbnormalStatistics statistics = tenantAbnormalStatMap.computeIfAbsent(tenantId, k -> new AbnormalStatistics());
				statistics.totalCount++;

				// 状态：0：未处理，1：已成故障，2：已关闭
				if (AbnormalStatusEnum.CLOSED.getCode().equals(record.getStatus())) {
					statistics.handledCount++;
				}
			}

			if (tenantAbnormalStatMap.isEmpty()) {
				XxlJobLogger.log("今日没有有效的异常统计信息，无需发送通知");
				log.info("今日没有有效的异常统计信息，无需发送通知");
				return ReturnT.SUCCESS;
			}

			// 5. 获取当前时间
			LocalDateTime now = LocalDateTime.now();

			// 6. 对每个租户的消息设置进行处理
			int totalSentCount = 0;
			for (MessageSetting messageSetting : messageSettings) {
				String tenantId = messageSetting.getTenantId();

				// 检查该租户是否有异常统计信息
				AbnormalStatistics statistics = tenantAbnormalStatMap.get(tenantId);
				if (statistics == null || statistics.getTotalCount() == 0) {
					log.info("租户[{}]今日没有异常信息，跳过发送", tenantId);
					continue;
				}

				// 检查是否应该发送消息
				if (shouldSendMessage(tenantId, messageSetting, now)) {
					// 获取接收人信息
					String receiverType = messageSetting.getReceiverType();
					String receiverInfo = messageSetting.getReceiverInfo();

					if (StringUtil.isBlank(receiverInfo)) {
						log.warn("租户[{}]接收人信息为空，跳过发送", tenantId);
						continue;
					}

					// 发送消息
					boolean sendResult = sendAbnormalMessage(tenantId, statistics, receiverType, receiverInfo);
					if (sendResult) {
						// 标记今天已执行，防止重复发送
						markAsExecuted(tenantId);
						// 这里无法准确计算接收人数量，因为解析逻辑在messageLogicService内部
						// 可以设置为1，表示成功发送了一条消息配置
						totalSentCount += 1;
					}


				}
			}

			if (totalSentCount > 0) {
				XxlJobLogger.log("每日异常定时任务执行完成，已向" + totalSentCount + "个用户发送通知");
				log.info("每日异常定时任务执行完成，已向{}个用户发送通知", totalSentCount);
			} else {
				XxlJobLogger.log("每日异常定时任务执行完成，没有需要发送的通知");
				log.info("每日异常定时任务执行完成，没有需要发送的通知");
			}

			return ReturnT.SUCCESS;
		} catch (Exception e) {
			XxlJobLogger.log("每日异常定时任务执行异常: " + e.getMessage());
			log.error("每日异常定时任务执行异常", e);
			return new ReturnT<>(ReturnT.FAIL_CODE, "执行异常: " + e.getMessage());
		}
	}

	/**
	 * 检查是否应该发送消息
	 * 支持整点和半点的精确匹配，防止重复发送
	 *
	 * @param tenantId       租户ID
	 * @param messageSetting 消息设置
	 * @param now            当前时间
	 * @return 是否应该发送消息
	 */
	private boolean shouldSendMessage(String tenantId, MessageSetting messageSetting, LocalDateTime now) {
		String sendStrategy = messageSetting.getSendStrategy();

		// 检查今天是否已经发送过
		if (hasExecutedToday(tenantId)) {
			return false;
		}

		// 如果是固定时间发送，精确匹配整点或半点
		if (SendStrategyEnum.FIXED_TIME.getCode().equals(sendStrategy)) {
			LocalTime fixedSendTime = messageSetting.getFixedSendTime();
			if (fixedSendTime == null) {
				log.warn("租户[{}]固定发送时间为空，跳过发送", tenantId);
				return false;
			}

			// 获取当前时间和目标时间
			int currentHour = now.getHour();
			int currentMinute = now.getMinute();
			int targetHour = fixedSendTime.getHour();
			int targetMinute = fixedSendTime.getMinute();

			// 只在整点(0分)或半点(30分)执行
			if (currentMinute != 0 && currentMinute != 30) {
				return false;
			}

			// 精确匹配用户设置的时间
			return currentHour == targetHour && currentMinute == targetMinute;
		}

		return false;
	}


	/**
	 * 发送异常消息
	 *
	 * @param tenantId     租户ID
	 * @param statistics   异常统计信息
	 * @param receiverType 接收人类型
	 * @param receiverInfo 接收人信息JSON字符串
	 * @return 是否发送成功
	 */
	private boolean sendAbnormalMessage(String tenantId, AbnormalStatistics statistics, String receiverType, String receiverInfo) {
		// 构建按位置分组的统计信息
		Date today = new Date();
		String startTime = DateUtil.format(DateUtil.parse(DateUtil.format(today, "yyyy-MM-dd"), "yyyy-MM-dd"), "yyyy-MM-dd HH:mm:ss");
		String endTime = DateUtil.format(DateUtil.plusDays(DateUtil.parse(DateUtil.format(today, "yyyy-MM-dd"), "yyyy-MM-dd"), 1), "yyyy-MM-dd HH:mm:ss");

		List<LocationStatistics> locationStats = buildLocationStatistics(tenantId, startTime, endTime);
		statistics.setLocationStatistics(locationStats);

		// 计算异常所属位置下的设备总数
		int totalDeviceCount = calculateDeviceCountInAbnormalLocations(tenantId, locationStats);
		statistics.setDeviceCount(totalDeviceCount);

		// 直接将AbnormalStatistics对象转换为JSON字符串
		String content = JSON.toJSONString(statistics);

		// 构建消息对象
		MessageVo messageVo = new MessageVo();
		messageVo.setAppKey("SiDAs");
		messageVo.setTitle(MESSAGE_TITLE);
		messageVo.setContent(content);
		messageVo.setType(MessageTypeEnum.SYSTEM.getCode()); // 系统消息
		messageVo.setSender(MessageTypeEnum.SYSTEM.getCode()); // 系统发送
		messageVo.setIsImmediate(YesNoEnum.YES.getCode()); // 立即发送
		messageVo.setBizType(MessageBizTypeEnum.DAILY_ABNORMAL.getCode());
		messageVo.setScheduleTime(DateUtil.now());
		messageVo.setChannel(MessageChannelEnum.MINI_PROGRAM.getCode());

		// 设置接收人信息 - 让messageLogicService自己解析
		messageVo.setReceiverType(receiverType);
		messageVo.setReceiverInfo(receiverInfo);
		messageVo.setReceiverInfoVo(JSON.parseObject(receiverInfo, ReceiverInfoVo.class));

		// 使用messageLogicService发送消息
		R result = messageLogicService.commitMessage(messageVo);
		if (result.isSuccess()) {
			log.info("租户[{}]的每日异常消息已发送，异常总数: {}, 已处理: {}, 未处理: {}, 消息内容: {}",
				tenantId, statistics.getTotalCount(), statistics.getHandledCount(), statistics.getUnhandledCount(), content);
			return true;
		} else {
			log.error("租户[{}]的每日异常消息发送失败: {}", tenantId, result.getMsg());
			return false;
		}
	}

	/**
	 * 构建按位置分组的统计信息
	 *
	 * @param tenantId  租户ID
	 * @param startTime 开始时间
	 * @param endTime   结束时间
	 * @return 按位置分组的统计信息列表
	 */
	private List<LocationStatistics> buildLocationStatistics(String tenantId, String startTime, String endTime) {
		try {
			Date start = DateUtil.parse(startTime, "yyyy-MM-dd HH:mm:ss");
			Date end = DateUtil.parse(endTime, "yyyy-MM-dd HH:mm:ss");

			// 查询时间范围内的异常记录
			List<AbnormalRecord> abnormalRecords = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
				.between(AbnormalRecord::getAbnormalTime, start, end));

			if (abnormalRecords.isEmpty()) {
				return new ArrayList<>();
			}

			// 获取相关的异常信息（用于获取租户ID和设备ID）
			Set<Long> abnormalIds = abnormalRecords.stream()
				.map(AbnormalRecord::getAbnormalId)
				.collect(Collectors.toSet());

			List<Abnormal> abnormalList = abnormalService.listByIds(abnormalIds);
			Map<Long, Abnormal> abnormalMap = abnormalList.stream()
				.filter(abnormal -> tenantId.equals(abnormal.getTenantId()))
				.collect(Collectors.toMap(Abnormal::getId, abnormal -> abnormal));

			// 获取所有相关的设备信息（用于获取device_id）
			Set<Long> equipmentIds = abnormalMap.values().stream()
				.map(Abnormal::getEquipmentId)
				.collect(Collectors.toSet());

			// 通过设备服务获取异常相关的设备信息
			List<Equipment> abnormalEquipmentList = equipmentService.listByIds(equipmentIds);

			// 构建equipmentId到deviceId的映射
			Map<Long, String> equipmentDeviceMap = new HashMap<>();
			Map<Long, String> equipmentNameMap = new HashMap<>();
			for (Equipment equipment : abnormalEquipmentList) {
				String deviceId = equipment.getDeviceId() != null ? equipment.getDeviceId().toString() : "unknown_" + equipment.getId();
				equipmentDeviceMap.put(equipment.getId(), deviceId);
				equipmentNameMap.put(equipment.getId(), equipment.getName() != null ? equipment.getName() : "设备-" + deviceId);
			}

			// 按设备ID分组统计
			Map<String, LocationStatistics> locationStatsMap = new HashMap<>();

			for (AbnormalRecord record : abnormalRecords) {
				Abnormal abnormal = abnormalMap.get(record.getAbnormalId());
				if (abnormal == null) {
					continue;
				}

				Long equipmentId = abnormal.getEquipmentId();
				String deviceId = equipmentDeviceMap.get(equipmentId);
				if (deviceId == null) {
					continue;
				}

				LocationStatistics locationStats = locationStatsMap.computeIfAbsent(deviceId, k -> {
					LocationStatistics stats = new LocationStatistics();
					stats.setDeviceId(deviceId);
					// 使用设备名称作为位置名称
					String locationName = equipmentNameMap.get(equipmentId);
					if (locationName == null) {
						locationName = "位置-" + deviceId;
					}
					stats.setDeviceName(locationName);
					return stats;
				});

				locationStats.setTotalCount(locationStats.getTotalCount() + 1);

				// 状态：0：未处理，1：已成故障，2：已关闭
				if (AbnormalStatusEnum.CLOSED.getCode().equals(record.getStatus())) {
					locationStats.setHandledCount(locationStats.getHandledCount() + 1);
				}
			}

			return new ArrayList<>(locationStatsMap.values());
		} catch (Exception e) {
			log.error("构建位置统计信息异常", e);
			return new ArrayList<>();
		}
	}

	/**
	 * 计算异常所属位置下的设备总数
	 *
	 * @param tenantId      租户ID
	 * @param locationStats 位置统计信息列表
	 * @return 设备总数
	 */
	private int calculateDeviceCountInAbnormalLocations(String tenantId, List<LocationStatistics> locationStats) {
		try {
			// 获取所有有异常的位置ID
			Set<String> abnormalLocationIds = locationStats.stream()
				.map(LocationStatistics::getDeviceId)
				.collect(Collectors.toSet());

			if (abnormalLocationIds.isEmpty()) {
				return 0;
			}

			// 查询该租户下所有设备
			List<Equipment> allEquipmentList = equipmentService.list(Wrappers.<Equipment>query().lambda()
				.eq(Equipment::getTenantId, tenantId));

			// 统计异常位置下的设备总数
			int deviceCount = 0;
			for (Equipment equipment : allEquipmentList) {
				String deviceId = equipment.getDeviceId() != null ? equipment.getDeviceId().toString() : "unknown_" + equipment.getId();
				if (abnormalLocationIds.contains(deviceId)) {
					deviceCount++;
				}
			}

			return deviceCount;
		} catch (Exception e) {
			log.error("计算异常位置设备总数异常", e);
			return 0;
		}
	}

	/**
	 * 检查今天是否已经执行过
	 *
	 * @param tenantId 租户ID
	 * @return 是否已执行
	 */
	private boolean hasExecutedToday(String tenantId) {
		try {
			String today = DateUtil.format(new Date(), "yyyy-MM-dd");
			String cacheKey = CACHE_KEY_PREFIX + tenantId + ":" + today;
			return StringUtil.isNotBlank(szykRedis.get(cacheKey));
		} catch (Exception e) {
			log.error("检查执行状态异常，租户ID: {}", tenantId, e);
			return false;
		}
	}

	/**
	 * 标记今天已执行
	 *
	 * @param tenantId 租户ID
	 */
	private void markAsExecuted(String tenantId) {
		try {
			String today = DateUtil.format(new Date(), "yyyy-MM-dd");
			String cacheKey = CACHE_KEY_PREFIX + tenantId + ":" + today;
			// 缓存25小时过期
			szykRedis.setEx(cacheKey, "executed", CACHE_EXPIRE_SECONDS);
			log.info("租户[{}]今日异常通知已标记为已执行", tenantId);
		} catch (Exception e) {
			log.error("标记执行状态异常，租户ID: {}", tenantId, e);
		}
	}


	/**
	 * 异常统计信息内部类
	 */
	@Data
	private static class AbnormalStatistics {
		/**
		 * 异常总数量
		 */
		private Integer totalCount = 0;

		/**
		 * 已处理的异常数量
		 */
		private Integer handledCount = 0;

		/**
		 * 异常所属位置下的设备总数量
		 */
		private Integer deviceCount = 0;

		/**
		 * 按异常位置分组的统计信息
		 */
		private List<LocationStatistics> locationStatistics;

		/**
		 * 未处理的异常数量（计算属性）
		 */
		public Integer getUnhandledCount() {
			return totalCount - handledCount;
		}
	}

	/**
	 * 按位置统计的异常信息
	 */
	@Data
	private static class LocationStatistics {
		/**
		 * 设备ID（异常位置）
		 */
		private String deviceId;

		/**
		 * 设备名称
		 */
		private String deviceName;

		/**
		 * 该位置异常总数量
		 */
		private Integer totalCount = 0;

		/**
		 * 该位置已处理的异常数量
		 */
		private Integer handledCount = 0;

		/**
		 * 该位置未处理的异常数量（计算属性）
		 */
		public Integer getUnhandledCount() {
			return totalCount - handledCount;
		}
	}
}
