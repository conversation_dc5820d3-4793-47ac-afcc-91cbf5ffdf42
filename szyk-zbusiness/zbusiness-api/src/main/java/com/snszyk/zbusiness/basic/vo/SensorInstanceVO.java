/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 传感器实例表 视图实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "SensorInstanceVO对象", description = "传感器实例表")
public class SensorInstanceVO extends SensorInstance {

	private static final long serialVersionUID = 1L;

	/**
	 * 传感器参数列表
	 */
	@ApiModelProperty(value = "传感器参数列表")
	private List<SensorInstanceParamVO> sensorInstanceParamList;

	/**
	 * 传感器编码列表 - 批量新增时需要
	 */
	@ApiModelProperty(value = "传感器编码列表 - 批量新增时需要")
	private List<String> codeList;

	/**
	 * 批量增加时的个数 - 批量新增时需要
	 */
	@ApiModelProperty(value = "批量增加时的个数 - 批量新增时需要")
	private Integer batchAddCount;

	/**
	 * 名称或型号 - 分页查询时需要
	 */
	@ApiModelProperty(value = "名称或型号或编码 - 分页查询时需要")
	private String nameOrModelOrCode;

	/**
	 * 生产厂家（字典：sensor_supplier） - 分页查询时需要
	 */
	@ApiModelProperty(value = "生产厂家（字典：sensor_supplier）")
	private String supplier;

	/**
	 * 传感器类型（字典：sensor_category） - 分页查询时需要
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器类型（字典：sensor_category）")
	private Integer category;

	/**
	 * 路径id - 分页查询时需要
	 */
	@ApiModelProperty(value = "路径id - 分页查询时需要")
	private String pathId;

	/**
	 * 是否已分配（0：未分配；1：已分配） - 分页查询时需要
	 */
	@ApiModelProperty(value = "是否已分配（0：未分配；1：已分配） - 分页查询时需要")
	private Integer isBind;

	/**
	 * 是否已绑定采集站（0：未绑定；1：已绑定） - 分页查询时需要
	 */
	@ApiModelProperty(value = "是否已绑定采集站（0：未绑定；1：已绑定） - 分页查询时需要")
	private Integer isBindStation;

	/**
	 * 有线无线（0：有线；1：无线）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "有线无线（0：有线；1：无线）")
	private Integer isWireless;

	/**
	 * 轴数（0-非振动；1-单轴；3-三轴）
	 */
	@ApiModelProperty(value = "轴数（0-非振动；1-单轴；3-三轴）")
	private Integer axisCount;

}
