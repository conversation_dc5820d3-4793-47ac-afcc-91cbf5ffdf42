/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础树表实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@Accessors(chain = true)
@TableName("sidas_basic_tree")
@ApiModel(value = "BasicTree对象", description = "基础树表")
public class BasicTree implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 租户ID
	 */
	@ApiModelProperty(value = "租户ID")
	private String tenantId;
	/**
	 * 父主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父主键")
	private Long parentId;
	/**
	 * 祖级列表
	 */
	@ApiModelProperty(value = "祖级列表")
	private String ancestors;
	/**
	 * 路径（用","分隔）
	 */
	@ApiModelProperty(value = "路径（用','分隔）")
	private String path;
	/**
	 * 路径名称（用","分隔）
	 */
	@ApiModelProperty(value = "路径名称（用','分隔）")
	private String pathName;
	/**
	 * 节点分类（0：位置，1：设备，2：部位）
	 */
	//@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "节点分类（0：位置，1：设备，2：部位）")
	private Integer nodeCategory;
	/**
	 * 节点类型（二进制算法，1：sidas，2：lubricate，3：sidas+lubricate）
	 */
	//@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "节点类型（二进制算法，1：sidas，2：lubricate，3：sidas+lubricate）")
	private Integer nodeType;
	/**
	 * 节点编码
	 */
	@ApiModelProperty(value = "节点编码")
	private String nodeCode;
	/**
	 * 节点名称
	 */
	@ApiModelProperty(value = "节点名称")
	private String nodeName;
	/**
	 * 节点层级
	 */
	@ApiModelProperty(value = "节点层级（从0开始）")
	private Integer nodeLevel;
	/**
	 * 报警等级（0绿色1蓝色2黄色3紫色4红色）
	 */
	@ApiModelProperty(value = "报警等级（0绿色1蓝色2黄色3紫色4红色）")
	private Integer alarmLevel;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("创建时间")
	private Date createTime;

}
