/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.auth.endpoint;

import com.github.xiaoymin.knife4j.annotations.ApiSort;
import com.snszyk.auth.provider.ITokenGranter;
import com.snszyk.auth.provider.TokenGranterBuilder;
import com.snszyk.auth.provider.TokenParameter;
import com.snszyk.captcha.application.ImageCaptchaApplication;
import com.snszyk.captcha.vo.CaptchaResponse;
import com.snszyk.captcha.vo.ImageCaptchaVO;
import com.snszyk.core.captcha.common.constant.CaptchaTypeConstant;
import com.snszyk.core.captcha.validator.common.model.dto.ImageCaptchaTrack;
import com.snszyk.system.entity.UserInfo;
import com.wf.captcha.SpecCaptcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import com.snszyk.common.cache.CacheNames;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.jwt.JwtUtil;
import com.snszyk.core.jwt.props.JwtProperties;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.launch.constant.TokenConstant;
import com.snszyk.core.log.annotation.ApiLog;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.WebUtil;
import com.snszyk.auth.utils.TokenUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.util.UUID;

import static com.snszyk.core.cache.constant.CacheConstant.*;

/**
 * 令牌端点
 *
 * <AUTHOR>
 */
@NonDS
@ApiSort(1)
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_AUTH_NAME)
@Api(value = "用户授权认证", tags = "授权接口")
public class SzykTokenEndPoint {

	private final SzykRedis szykRedis;
	private final JwtProperties jwtProperties;
	private final ImageCaptchaApplication imageCaptchaApplication;

	@ApiLog("登录用户验证")
	@PostMapping("/oauth/token")
	@ApiOperation(value = "获取认证令牌", notes = "传入租户ID:tenantId,账号:account,密码:password")
	public Kv token(@ApiParam(value = "租户ID", required = true) @RequestParam String tenantId,
					@ApiParam(value = "账号", required = true) @RequestParam(required = false) String username,
					@ApiParam(value = "密码", required = true) @RequestParam(required = false) String password,
					@ApiIgnore @RequestHeader(name = TokenUtil.DEPT_HEADER_KEY, required = false) String deptId,
					@ApiIgnore @RequestHeader(name = TokenUtil.ROLE_HEADER_KEY, required = false) String roleId) {

		Kv authInfo = Kv.create();

		String grantType = WebUtil.getRequest().getParameter("grant_type");
		String refreshToken = WebUtil.getRequest().getParameter("refresh_token");

		String userType = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);

		TokenParameter tokenParameter = new TokenParameter();
		tokenParameter.getArgs().set("tenantId", tenantId)
			.set("username", username)
			.set("password", password)
			.set("grantType", grantType)
			.set("refreshToken", refreshToken)
			.set("userType", userType)
			.set("deptId", deptId).set("roleId", roleId);

		ITokenGranter granter = TokenGranterBuilder.getGranter(grantType);
		UserInfo userInfo = granter.grant(tokenParameter);

		if (userInfo == null || userInfo.getUser() == null) {
			return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "用户名或密码不正确");
		}

		if (Func.isEmpty(userInfo.getRoles())) {
			return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "未获得用户的角色信息");
		}

		return TokenUtil.createAuthInfo(userInfo);
	}

	@ApiLog("登录用户验证")
	@PostMapping("/oauth/code")
	@ApiOperation(value = "获取认证令牌", notes = "传入租户ID:tenantId,账号:account,密码:password")
	public Kv slider(@ApiParam(value = "租户ID", required = true) @RequestParam String tenantId,
					 @ApiParam(value = "账号", required = true) @RequestParam(required = false) String username,
					 @ApiParam(value = "密码", required = true) @RequestParam(required = false) String password,
					 @ApiIgnore @RequestHeader(name = TokenUtil.DEPT_HEADER_KEY, required = false) String deptId,
					 @ApiIgnore @RequestHeader(name = TokenUtil.ROLE_HEADER_KEY, required = false) String roleId,
					 @RequestBody ImageCaptchaTrack imageCaptchaTrack) {

		Kv authInfo = Kv.create();

		String grantType = WebUtil.getRequest().getParameter("grant_type");
		String refreshToken = WebUtil.getRequest().getParameter("refresh_token");

		String userType = Func.toStr(WebUtil.getRequest().getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);

		TokenParameter tokenParameter = new TokenParameter();
		tokenParameter.getArgs().set("tenantId", tenantId)
			.set("username", username)
			.set("password", password)
			.set("grantType", grantType)
			.set("refreshToken", refreshToken)
			.set("userType", userType)
			.set("deptId", deptId)
			.set("roleId", roleId)
			.set("imageCaptchaTrack", imageCaptchaTrack);

		ITokenGranter granter = TokenGranterBuilder.getGranter(grantType);
		UserInfo userInfo = granter.grant(tokenParameter);

		if (userInfo == null || userInfo.getUser() == null) {
			return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "用户名或密码不正确");
		}

		if (Func.isEmpty(userInfo.getRoles())) {
			return authInfo.set("error_code", HttpServletResponse.SC_BAD_REQUEST).set("error_description", "未获得用户的角色信息");
		}

		return TokenUtil.createAuthInfo(userInfo);
	}

	@GetMapping("/oauth/gen")
	@ApiOperation(value = "获取验证码")
	@ResponseBody
	public CaptchaResponse<ImageCaptchaVO> genCaptcha(HttpServletRequest request, @RequestParam(value = "type", required = false)String type) {
		if (StringUtils.isBlank(type)) {
			type = CaptchaTypeConstant.SLIDER;
		}
		CaptchaResponse<ImageCaptchaVO> response = imageCaptchaApplication.generateCaptcha(type);
		return response;
	}

	@PostMapping("/oauth/check")
	@ResponseBody
	public boolean checkCaptcha(@RequestParam("id") String id,
								@RequestBody ImageCaptchaTrack imageCaptchaTrack,
								HttpServletRequest request) {
		return imageCaptchaApplication.matching(id, imageCaptchaTrack);
	}


	@GetMapping("/oauth/logout")
	@ApiOperation(value = "退出登录")
	public Kv logout() {
		SzykUser user = AuthUtil.getUser();
		if (user != null && jwtProperties.getState()) {
			String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
			JwtUtil.removeAccessToken(user.getTenantId(), String.valueOf(user.getUserId()), token);
		}
		return Kv.create().set("success", "true").set("msg", "success");
	}

	@GetMapping("/oauth/captcha")
	@ApiOperation(value = "获取验证码")
	public Kv captcha() {
		SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
		String verCode = specCaptcha.text().toLowerCase();
		String key = UUID.randomUUID().toString();
		// 存入redis并设置过期时间为30分钟
		szykRedis.setEx(CacheNames.CAPTCHA_KEY + key, verCode, Duration.ofMinutes(30));
		// 将key和base64返回给前端
		return Kv.create().set("key", key).set("image", specCaptcha.toBase64());
	}

	@GetMapping("/oauth/clear-cache")
	@ApiOperation(value = "清除缓存")
	public Kv clearCache() {
		CacheUtil.clear(BIZ_CACHE);
		CacheUtil.clear(USER_CACHE);
		CacheUtil.clear(DICT_CACHE);
		CacheUtil.clear(FLOW_CACHE);
		CacheUtil.clear(SYS_CACHE);
		CacheUtil.clear(PARAM_CACHE);
		CacheUtil.clear(RESOURCE_CACHE);
		CacheUtil.clear(MENU_CACHE);
		CacheUtil.clear(DICT_CACHE, Boolean.FALSE);
		CacheUtil.clear(MENU_CACHE, Boolean.FALSE);
		CacheUtil.clear(SYS_CACHE, Boolean.FALSE);
		CacheUtil.clear(PARAM_CACHE, Boolean.FALSE);
		return Kv.create().set("success", "true").set("msg", "success");
	}
}
