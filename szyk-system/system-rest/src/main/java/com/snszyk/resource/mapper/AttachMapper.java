/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.mapper;

import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.vo.AttachVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 附件表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface AttachMapper extends BaseMapper<Attach> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param attach
	 * @return
	 */
	List<AttachVO> selectAttachPage(IPage page, AttachVO attach);

}
