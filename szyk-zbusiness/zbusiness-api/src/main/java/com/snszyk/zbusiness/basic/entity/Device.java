/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备位置表实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@Accessors(chain = true)
@TableName("eolm_device")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Device对象", description = "设备位置表")
public class Device extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 父主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父主键")
	@TableField(updateStrategy= FieldStrategy.NOT_NULL)
	private Long parentId;
	/**
	 * 编码
	 */
	@ApiModelProperty(value = "编码")
	private String code;
	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 场景图片
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "场景图片")
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	private Long image;
	/**
	 * 坐标信息
	 */
	@ApiModelProperty(value = "坐标信息")
	private String coordinate;
	/**
	 * 经纬度（用','分隔）
	 */
	@ApiModelProperty(value = "经纬度（用','分隔）")
	private String longitudeLatitude;
	/**
	 * 全路径
	 */
	@ApiModelProperty(value = "全路径")
	private String path;
	/**
	 * 全路径名称
	 */
	@ApiModelProperty(value = "全路径名称")
	private String pathName;
	/**
	 * 类型（二进制算法，1：sidas，2：lubricate，3：sidas+lubricate）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "类型（二进制算法，1：sidas，2：lubricate，3：sidas+lubricate）")
	private Integer type;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
