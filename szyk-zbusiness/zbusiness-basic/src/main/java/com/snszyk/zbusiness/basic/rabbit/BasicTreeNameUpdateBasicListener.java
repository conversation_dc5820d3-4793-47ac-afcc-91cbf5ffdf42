package com.snszyk.zbusiness.basic.rabbit;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.dto.BasicTreeNameUpdateDTO;
import com.snszyk.zbusiness.basic.entity.BasicTree;
import com.snszyk.zbusiness.basic.entity.Device;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.service.IBasicTreeService;
import com.snszyk.zbusiness.basic.service.IDeviceService;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.service.IMonitorService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 生产结构树节点名称（地点、设备、测点）更新监听 - basic服务
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class BasicTreeNameUpdateBasicListener {

	private final IDeviceService deviceService;
	private final IEquipmentService equipmentService;
	private final IMonitorService monitorService;
	private final IBasicTreeService basicTreeService;

	@RabbitHandler
	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_BASIC_TREE_NAME_UPDATE_BASIC)
	public void basicTreeNameUpdate(BasicTreeNameUpdateDTO dto) {
		log.info("生产结构树节点名称更新 - {}", dto);

		if (BasicTreeNameUpdateDTO.TYPE_DEVICE.equals(dto.getType())) {
			// 更新子Device、子Equipment、子Monitor
			updateChildDevice(dto);
			updateChildEquipment(dto);
			updateChildMonitor(dto);
			// 更新sidas_basic_tree中的节点
			updateBasicTreeChildNode(dto);
		} else if (BasicTreeNameUpdateDTO.TYPE_EQUIPMENT.equals(dto.getType())) {
			// 更新子Monitor
			updateChildMonitor(dto);
			// 更新sidas_basic_tree中的节点
			updateBasicTreeChildNode(dto);
		} else if (BasicTreeNameUpdateDTO.TYPE_MONITOR.equals(dto.getType())) {
			// 更新sidas_basic_tree中的节点
			updateBasicTreeChildNode(dto);
		} else {
			log.warn("不支持的type = {}", dto.getType());
		}
	}

	/**
	 * device或equipment更新时，更新sidas_basic_tree中的子节点
	 * @param dto device、equipment最新数据
	 */
	private void updateBasicTreeChildNode(BasicTreeNameUpdateDTO dto) {
		List<BasicTree> childNodeList = basicTreeService.list(Wrappers.<BasicTree>lambdaQuery()
			.like(BasicTree::getPath, dto.getId()));
		if (Func.isNotEmpty(childNodeList)) {
			childNodeList.forEach(node -> {
				// 找到路径中的索引
				List<String> pathIdList = Arrays.asList(node.getPath().split(StringPool.COMMA));
				int pathIdIndex = pathIdList.indexOf(dto.getId().toString());

				// 如果是更新了节点路径名的最后一级名称，即更新了当前节点的名称
				if (pathIdIndex == (pathIdList.size() - 1)) {
					node.setNodeName(dto.getNewName());
				}

				// 更新路径对应索引的Name
				List<String> pathNameList = Arrays.asList(node.getPathName().split(StringPool.COMMA));
				pathNameList.set(pathIdIndex, dto.getNewName());

				// 更新子节点的pathName
				node.setPathName(String.join(StringPool.COMMA, pathNameList));
				boolean update = basicTreeService.updateById(node);
				log.info("updateBasicTreeChildNode() - 更新子节点的pathName - {}", update);
			});
		}
	}

	/**
	 * 更新子地点路径名称
	 * @param monitorDto
	 */
	private void updateChildDevice(BasicTreeNameUpdateDTO monitorDto) {
		List<Device> childDeviceList = deviceService.list(Wrappers.<Device>lambdaQuery()
			.like(Device::getPath, monitorDto.getId()));
		if (Func.isNotEmpty(childDeviceList)) {
			childDeviceList.forEach(device -> {
				// 找到路径中的索引
				List<String> pathIdList = Arrays.asList(device.getPath().split(StringPool.COMMA));
				int pathIdIndex = pathIdList.indexOf(monitorDto.getId().toString());

				// 更新路径对应索引的Name
				List<String> pathNameList = Arrays.asList(device.getPathName().split(StringPool.COMMA));
				pathNameList.set(pathIdIndex, monitorDto.getNewName());

				// 更新子节点的pathName
				device.setPathName(String.join(StringPool.COMMA, pathNameList));

				// 更新当前节点的Name
				if (device.getId().equals(monitorDto.getId())) {
					device.setName(monitorDto.getNewName());
				}

				boolean update = deviceService.updateById(device);
				log.info("updateChildDevice() - 更新子节点的pathName - {}", update);
			});
		}
	}

	/**
	 * 更新子设备路径名称
	 * @param equipmentDto
	 */
	private void updateChildEquipment(BasicTreeNameUpdateDTO equipmentDto) {
		List<Equipment> childEquipmentList = equipmentService.list(Wrappers.<Equipment>lambdaQuery()
			.like(Equipment::getPath, equipmentDto.getId()));
		if (Func.isNotEmpty(childEquipmentList)) {
			childEquipmentList.forEach(equipment -> {
				// 找到路径中的索引
				List<String> pathIdList = Arrays.asList(equipment.getPath().split(StringPool.COMMA));
				int pathIdIndex = pathIdList.indexOf(equipmentDto.getId().toString());

				// 更新路径对应索引的Name
				List<String> pathNameList = Arrays.asList(equipment.getPathName().split(StringPool.COMMA));
				pathNameList.set(pathIdIndex, equipmentDto.getNewName());

				// 更新子节点的pathName
				equipment.setPathName(String.join(StringPool.COMMA, pathNameList));
				boolean update = equipmentService.updateById(equipment);
				log.info("updateChildEquipment() - 更新子节点的pathName - {}", update);
			});
		}
	}

	/**
	 * 更新子测点路径名称
	 * @param dto
	 */
	private void updateChildMonitor(BasicTreeNameUpdateDTO dto) {
		List<Monitor> childMonitorList = monitorService.list(Wrappers.<Monitor>lambdaQuery()
			.like(Monitor::getPath, dto.getId()));
		if (Func.isNotEmpty(childMonitorList)) {
			childMonitorList.forEach(monitor -> {
				// 找到路径中的索引
				List<String> pathIdList = Arrays.asList(monitor.getPath().split(StringPool.COMMA));
				int pathIdIndex = pathIdList.indexOf(dto.getId().toString());

				// 更新路径对应索引的Name
				List<String> pathNameList = Arrays.asList(monitor.getPathName().split(StringPool.COMMA));
				pathNameList.set(pathIdIndex, dto.getNewName());

				// 更新子节点的pathName
				monitor.setPathName(String.join(StringPool.COMMA, pathNameList));
				boolean update = monitorService.updateById(monitor);
				log.info("updateChildMonitor() - 更新子节点的pathName - {}", update);
			});
		}
	}

}
