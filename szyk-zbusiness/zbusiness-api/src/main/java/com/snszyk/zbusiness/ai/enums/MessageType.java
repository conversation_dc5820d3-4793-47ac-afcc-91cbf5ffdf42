package com.snszyk.zbusiness.ai.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MessageType {

	/**
	 * 工作流
	 */
	WORKFLOW_REPORT,
	/**
	 * 聊天工作流
	 */
	CHATFLOW_QA,
	;

	public static MessageType getByCode(String code) {
		for (MessageType value : MessageType.values()) {
			if (code.equals(value.name())) {
				return value;
			}
		}
		return null;
	}
}
