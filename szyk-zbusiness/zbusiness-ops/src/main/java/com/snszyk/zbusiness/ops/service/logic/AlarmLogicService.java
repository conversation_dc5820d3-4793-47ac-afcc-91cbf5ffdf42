/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.logic;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.config.SzykAttachConfig;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.common.utils.ChartUtil;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.DingTalkMessageVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.entity.User;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.system.service.IRoleService;
import com.snszyk.system.service.IUserSearchService;
import com.snszyk.system.vo.RoleVO;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.enums.AlarmIndexEnum;
import com.snszyk.zbusiness.basic.enums.GlobalConfigCategoryEnum;
import com.snszyk.zbusiness.basic.enums.RealNonVibrationDataEnum;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.feign.PythonServerFeign;
import com.snszyk.zbusiness.basic.service.*;
import com.snszyk.zbusiness.basic.vo.*;
import com.snszyk.zbusiness.ops.dto.*;
import com.snszyk.zbusiness.ops.entity.*;
import com.snszyk.zbusiness.ops.enums.*;
import com.snszyk.zbusiness.ops.service.*;
import com.snszyk.zbusiness.ops.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 报警管理信息表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Slf4j
@Service
public class AlarmLogicService {

	private static final String[] KEY_ALARM_HANDLER = {"threshold", "mechanism", "ai"};
	private static final String[] KEY_DIAGRAM_CHART = {"trendChart", "timeDomainDiagram", "freqDomainDiagram", "envelopDiagram"};
	@Resource
	private IAlarmService alarmService;
	@Resource
	private AbnormalLogicService abnormalLogicService;
	@Resource
	private IAlarmThresholdService alarmThresholdService;
	@Resource
	private IAlarmDetailService alarmDetailService;
	@Resource
	private IAlarmRecordService alarmRecordService;
	@Resource
	private IAlarmChartService alarmChartService;
	@Resource
	private IDiagnosisRecordService diagnosisRecordService;
	@Resource
	private IDictBizService dictBizService;
	@Resource
	private IUserSearchService userSearchService;

	@Resource
	private MessageLogicService messageLogicService;
	@Resource
	private PythonServerFeign pythonServerFeign;
	@Resource
	private SzykRedis szykRedis;
	@Resource
	private InfluxdbTools influxdbTools;
	@Resource
	private RabbitTemplate rabbitTemplate;
	@Resource
	private SzykAttachConfig szykAttachConfig;
	@Resource
	private IEquipmentService equipmentService;
	@Resource
	private IMonitorService monitorService;
	@Resource
	private IWaveService waveService;
	@Resource
	private ISensorInstanceService sensorInstanceService;
	@Resource
	private IDeviceService deviceService;
	@Resource
	private IGlobalConfigService globalConfigService;
	@Resource
	private IRoleService roleService;
	@Resource
	private IAiModelService aiModelService;

	/**
	 * 文件存储路径
	 */
	private String rootPath;

	@PostConstruct
	public void initField() {
		this.rootPath = szykAttachConfig.szykAttachProperties().getPath();
	}

	/**
	 * 分页列表
	 *
	 * @param page
	 * @param alarm
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.zbusiness.alarm.dto.AlarmDTO>
	 * <AUTHOR>
	 * @date 2022/12/13 11:58
	 */
	public IPage<AlarmDTO> page(IPage<AlarmDTO> page, AlarmVO alarm) {
		if (Func.isNotEmpty(alarm.getStartDate())) {
			alarm.setStartDate(alarm.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(alarm.getEndDate())) {
			alarm.setEndDate(alarm.getEndDate() + " 23:59:59");
		}
		IPage<AlarmDTO> iPage = alarmService.page(page, alarm);
		if (iPage != null && Func.isNotEmpty(iPage.getRecords())) {
			iPage.getRecords().forEach(dto -> {
				dto.setPathName(Func.isNotEmpty(dto.getPathName()) ? dto.getPathName().replace(StringPool.COMMA, StringPool.SLASH) : "");
				if (Func.isNotEmpty(dto.getFirstAlarmTime())) {
					dto.setDuration(this.getDuration(dto.getFirstAlarmTime()));
				}
				// 设备编号和型号
				Equipment equipment = equipmentService.getById(dto.getEquipmentId());
				if (Func.isNotEmpty(equipment)) {
					dto.setDeviceNo(equipment.getCode()).setDeviceModel(equipment.getModel());
				}
			});
		}
		return iPage;
	}

	/**
	 * 获取持续时长
	 *
	 * @param firstAlarmTime
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2022/12/13 10:16
	 */
	public String getDuration(Date firstAlarmTime) {
		Long time = DateUtil.betweenMs(firstAlarmTime, com.snszyk.core.tool.utils.DateUtil.now());
		Double days = Math.floor(time / (1000 * 60 * 60 * 24));
		Double hours = Math.floor((time / (1000 * 60 * 60)) % 24);
		Double minutes = Math.floor((time / (1000 * 60)) % 60);
		//Double seconds = Math.floor((time/1000)%60);
		return days.intValue() + "天" + hours.intValue() + "时" + minutes.intValue() + "分";
	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return com.snszyk.zbusiness.alarm.dto.AlarmDTO
	 * <AUTHOR>
	 * @date 2022/12/13 13:56
	 */
	public AlarmDTO detail(Long id) {
		Alarm alarm = alarmService.getById(id);
		if (alarm == null) {
			throw new ServiceException("当前报警不存在，id = " + id);
		}
		AlarmDTO detail = Objects.requireNonNull(BeanUtil.copy(alarm, AlarmDTO.class));

		Equipment equipment = equipmentService.getById(alarm.getEquipmentId());
		if (Func.isNotEmpty(equipment)) {
			detail.setDeviceNo(equipment.getCode()).setDeviceModel(equipment.getModel());
		}
		detail.setAlarmLevelName(DictBizCache.getValue(DictBizEnum.ALARM_LEVEL, detail.getAlarmLevel()))
			.setStatusName(DictBizCache.getValue(DictBizEnum.ALARM_BIZ_STATUS, detail.getStatus()))
			.setVibrateSubareaName(DictBizCache.getValue(DictBizEnum.VIBRATE_SUBAREA, detail.getVibrateSubarea()))
			.setDuration(this.getDuration(detail.getFirstAlarmTime())).
			setPathName(alarm.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
		// 诊断结论
		DiagnosisRecordVO diagnosisRecord = new DiagnosisRecordVO(id, null);
		List<DiagnosisRecordVO> diagnosisRecordList = diagnosisRecordService.recordsByAlarmId(diagnosisRecord);
		if (Func.isNotEmpty(diagnosisRecordList)) {
			detail.setDiagnosisConclusions(diagnosisRecordList);
		}
		// 关闭原因
		if (AlarmStatusEnum.CLOSED == AlarmStatusEnum.getByCode(alarm.getStatus())) {
			detail.setCloseVO(JSONUtil.toBean(alarm.getCloseReason(), AlarmCloseVO.class));
		}
		// 报警位置列表
		List<AlarmDetail> list = alarmDetailService.list(Wrappers.<AlarmDetail>query().lambda().eq(AlarmDetail::getAlarmId, alarm.getId())
			.orderByDesc(AlarmDetail::getLastAlarmTime));
		if (Func.isNotEmpty(list)) {
			detail.setAlarmDetails(list.stream().map(alarmDetail -> {
				AlarmDetailDTO alarmDetailDTO = Objects.requireNonNull(BeanUtil.copy(alarmDetail, AlarmDetailDTO.class));
				alarmDetailDTO.setAlarmTypeName(AlarmBizTypeEnum.getByCode(alarmDetail.getAlarmType()).getName())
					.setAlarmLevelName(AlarmLevelEnum.getByCode(alarmDetail.getAlarmLevel()).getName());
				if (Func.isNotEmpty(alarmDetail.getAlarmIndex())) {
					alarmDetailDTO.setAlarmIndexName(AlarmIndexEnum.getByCode(alarmDetail.getAlarmIndex()).getName());
				}
				// alarmId+测点+指标唯一确定
				alarmDetailDTO.setTimes(alarmRecordService.count(Wrappers.<AlarmRecord>query().lambda().eq(AlarmRecord::getAlarmId, id)
					.eq(AlarmRecord::getMonitorId, alarmDetail.getMonitorId()).eq(AlarmRecord::getAlarmType, alarmDetail.getAlarmType())
					.eq(Func.isNotEmpty(alarmDetail.getAlarmIndex()), AlarmRecord::getAlarmIndex, alarmDetail.getAlarmIndex())));
				if (Func.isNotEmpty(alarmDetail.getVal())) {
					alarmDetailDTO.setAlarmValue(String.format("%.3f", alarmDetail.getVal()));
				}
				return alarmDetailDTO;
			}).collect(Collectors.toList()));
		}
		return detail;
	}

	/**
	 * 关闭报警
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2022/12/15 10:25
	 */
	public boolean closeAlarm(AlarmCloseVO vo) {
		Alarm alarm = alarmService.getById(vo.getId());
		if (alarm == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		vo.setCloseTime(com.snszyk.core.tool.utils.DateUtil.now()).setCloseUser(AuthUtil.getUserId())
			.setCloseUserName(UserCache.getUser(AuthUtil.getUserId()).getRealName());
		alarm.setCloseReason(JSONUtil.toJsonStr(vo)).setStatus(AlarmStatusEnum.CLOSED.getCode());
		//更新设备报警等级
		log.info("发送MQ消息给设备更新报警等级：{}", alarm.getEquipmentId());
		rabbitTemplate.convertAndSend(EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
			EolmConstant.Rabbit.ROUTING_ALARM_LEVEL_CLOSE, alarm.getEquipmentId());
		return alarmService.updateById(alarm);
	}

	/**
	 * 报警
	 *
	 * @param alarmDetailVO
	 * @return boolean
	 * <AUTHOR>
	 * @date 2022/12/15 09:16
	 */
	public boolean addAlarm(AlarmDetailVO alarmDetailVO) {
		log.info("=====》报警数据：================{}", alarmDetailVO);
		boolean ret;
		Map<String, Object> dataMap = this.getAlarmData(alarmDetailVO);
		Alarm alarm = (Alarm) dataMap.get("alarm");
		AlarmDetail alarmDetail = (AlarmDetail) dataMap.get("alarmDetail");
		Boolean monitorAlarm = (Boolean) dataMap.get("monitorAlarm");
		Boolean equipmentAlarm = (Boolean) dataMap.get("equipmentAlarm");
		Boolean isThresholdChange = (Boolean) dataMap.get("isThresholdChange");
		// 更新设备报警信息
		ret = alarmService.saveOrUpdate(alarm);
		// 更新报警位置
		alarmDetail.setAlarmId(alarm.getId()).setLastAlarmTime(alarmDetailVO.getLastAlarmTime());
		alarmDetailService.saveOrUpdate(alarmDetail);
		// 更新报警记录
		AlarmRecord alarmRecord = Objects.requireNonNull(BeanUtil.copy(alarmDetail, AlarmRecord.class));
		alarmRecord.setId(null).setAlarmDetailId(alarmDetail.getId()).setAlarmTime(alarmDetailVO.getLastAlarmTime())
			.setSampleDataType(alarmDetailVO.getSampleDataType()).setUnit(alarmDetailVO.getUnit())
			.setSensorDataId(alarmDetailVO.getSensorDataId()).setWaveId(alarmDetailVO.getWaveId()).setVal(alarmDetailVO.getVal());
		if (isThresholdChange) {
			alarmRecord.setAlarmLevel(alarmDetailVO.getAlarmLevel());
		}
		alarmRecordService.save(alarmRecord);
		// 门限报警生成异常
		if (AlarmIndexEnum.EFFECTIVE_VALUE == AlarmIndexEnum.getByCode(alarmRecord.getAlarmIndex())) {
			this.setRedisData(KEY_ALARM_HANDLER[0], alarmRecord.getWaveId(), alarmRecord.getAlarmTime(), 2);
			this.sensorDataHandler(alarmDetailVO.getTenantId(), alarmRecord.getWaveId() + StringPool.COLON +
				alarmRecord.getAlarmTime().getTime() + StringPool.COLON + KEY_ALARM_HANDLER[0]);
		}
		// 更新设备和测点报警等级
		AlarmLevelVO alarmLevelVO = new AlarmLevelVO();
		if (monitorAlarm) {
			MonitorVO monitorVO = new MonitorVO(alarmDetailVO.getMonitorId(), alarmDetailVO.getAlarmLevel());
			alarmLevelVO.setMonitor(monitorVO);
			log.info("=====》测点报警：================{}", monitorVO.getId() + ":" + monitorVO.getAlarmLevel());
		}
		if (equipmentAlarm) {
			EquipmentVO equipmentVO = new EquipmentVO(alarmDetailVO.getEquipmentId(), alarm.getAlarmLevel());
			log.info("=====》设备报警：================{}", equipmentVO.getId() + ":" + equipmentVO.getAlarmLevel());
		}
		//更新测点报警等级
		if (Func.isNotEmpty(alarmLevelVO)) {
			log.info("发送MQ消息给设备更新报警等级：{}", alarmLevelVO);
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
				EolmConstant.Rabbit.ROUTING_ALARM_LEVEL_UPDATE, alarmLevelVO);
		}
		// 保存诊断图谱
		AlarmRecordVO alarmRecordVO = Objects.requireNonNull(BeanUtil.copy(alarmRecord, AlarmRecordVO.class));
		alarmRecordVO.setOriginTime(alarmDetailVO.getAlarmDataTime());
		log.info("=====》保存门限报警记录图谱：================{}", alarmRecordVO);
		if (AlarmIndexEnum.EQUIPMENT_TEMPERATURE != AlarmIndexEnum.getByCode(alarmRecordVO.getAlarmIndex())) {
			this.saveWaveForm(alarmRecordVO);
		}
		// 发送报警消息
		alarmDetailVO.setAlarmId(alarm.getId());
		Equipment equipment = equipmentService.getById(alarm.getEquipmentId());

		this.sendMessage(equipment.getCode(), alarmDetailVO);
		return ret;
	}

	/**
	 * 获取报警、报警明细数据
	 *
	 * @param alarmDetailVO
	 * @return java.util.Map<java.lang.String, java.lang.Object>
	 * <AUTHOR>
	 * @date 2023/06/13 16:31
	 */
	public Map<String, Object> getAlarmData(AlarmDetailVO alarmDetailVO) {
		Map<String, Object> dataMap = new HashMap<>(16);
		boolean monitorAlarm = false;
		boolean equipmentAlarm = false;
		boolean isThresholdChange = false;
		AlarmDetail alarmDetail;
		// 待处理的报警
		Alarm alarm = alarmService.getOne(Wrappers.<Alarm>query().lambda().eq(Alarm::getEquipmentId, alarmDetailVO.getEquipmentId())
			.ne(Alarm::getStatus, AlarmStatusEnum.CLOSED.getCode()));
		// 当前设备还未报警
		if (Func.isEmpty(alarm)) {
			// 新增alarmDetail
			alarmDetail = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetail.class));
			alarmDetail.setFirstAlarmTime(alarmDetailVO.getLastAlarmTime());
			monitorAlarm = true;
			// 新增alarm
			alarm = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, Alarm.class));
			alarm.setStatus(0);
			alarm.setAlarmType(AlarmBizTypeEnum.THRESHOLD.getCode()).setFirstAlarmTime(alarmDetailVO.getLastAlarmTime());
			equipmentAlarm = true;
		} else {
			// 当前设备已有报警
			List<AlarmDetail> alarmDetails = alarmDetailService.list(Wrappers.<AlarmDetail>query().lambda()
				.eq(AlarmDetail::getAlarmId, alarm.getId()).eq(AlarmDetail::getMonitorId, alarmDetailVO.getMonitorId()));
			// 当前测点还未报警
			if (Func.isEmpty(alarmDetails)) {
				alarmDetail = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetail.class));
				alarmDetail.setFirstAlarmTime(alarmDetailVO.getLastAlarmTime());
				monitorAlarm = true;
			} else {
				// 当前测点已有报警
				alarmDetail = alarmDetailService.queryAlarmDetail(alarmDetailVO.getMonitorId(), null, alarmDetailVO.getAlarmIndex());
				// 当前指标还未报警
				if (Func.isEmpty(alarmDetail)) {
					alarmDetail = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetail.class));
					alarmDetail.setFirstAlarmTime(alarmDetailVO.getLastAlarmTime());
					monitorAlarm = true;
				} else {
					Integer compareLevel = 0;
					// 满足条件更新当前测点当前指标的报警情况
					List<AlarmDetail> currentAllDetails = alarmDetailService.list(Wrappers.<AlarmDetail>query().lambda()
						.eq(AlarmDetail::getMonitorId, alarmDetailVO.getMonitorId()).eq(AlarmDetail::getAlarmIndex, alarmDetailVO.getAlarmIndex()));
					if (Func.isNotEmpty(currentAllDetails)) {
						List<Integer> alarmLevels = currentAllDetails.stream().map(d -> d.getAlarmLevel()).collect(Collectors.toList());
						compareLevel = Collections.max(alarmLevels);
					}
					if (alarmDetailVO.getAlarmLevel() >= compareLevel) {
						alarmDetail.setAlarmLevel(alarmDetailVO.getAlarmLevel()).setVal(alarmDetailVO.getVal())
							.setWaveId(alarmDetailVO.getWaveId()).setLastAlarmTime(alarmDetailVO.getLastAlarmTime());
						monitorAlarm = true;
					} else {
						// 门限出现变动的情况，以最新的数据保存报警记录
						isThresholdChange = true;
					}
				}
			}
			// 设备报警的前提是测点报警
			if (monitorAlarm) {
				// 测点报警并不意味着设备一定报警
				if (alarmDetailVO.getAlarmLevel() > alarm.getAlarmLevel()) {
					alarm.setAlarmLevel(alarmDetail.getAlarmLevel());
					equipmentAlarm = true;
				}
			}
		}
		alarm.setLastAlarmTime(alarmDetailVO.getLastAlarmTime());
		dataMap.put("alarm", alarm);
		dataMap.put("alarmDetail", alarmDetail);
		dataMap.put("monitorAlarm", monitorAlarm);
		dataMap.put("equipmentAlarm", equipmentAlarm);
		dataMap.put("isThresholdChange", isThresholdChange);
		return dataMap;
	}

	/**
	 * 处理门限报警的mq数据
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2023/03/15 11:18
	 */
	public void handleSensorData(SensorDataVO sensorData) {
		// 非振动数据值
		BigDecimal value = null;
		// 振动数据值
		BigDecimal rmsValue = null;
		BigDecimal peakValue = null;
		BigDecimal peakPeakValue = null;
		BigDecimal clearanceValue = null;
		BigDecimal skewnessValue = null;
		BigDecimal kurtosisValue = null;
		boolean flag = Arrays.stream(RealNonVibrationDataEnum.values()).anyMatch(t ->
			Func.equals(t.getCode(), sensorData.getSampleDataType()));
		if (!flag) {
			if (Func.isNotEmpty(sensorData.getValue())) {
				rmsValue = sensorData.getValue();
			} else {
				rmsValue = sensorData.getRmsValue();
			}
			// 峰值
			peakValue = sensorData.getPeakValue();
			// 峰峰值
			peakPeakValue = sensorData.getPeakPeakValue();
			// 裕度
			clearanceValue = sensorData.getClearanceValue();
			// 峭度
			skewnessValue = sensorData.getSkewnessValue();
			// 歪度
			kurtosisValue = sensorData.getKurtosisValue();
		} else {
			value = sensorData.getValue();
		}
		log.info("报警数据采样时间：======================={}", com.snszyk.core.tool.utils.DateUtil.formatDateTime(sensorData.getOriginTime()));

		MonitorDTO monitor = monitorService.getByIdIncludeEquipment(sensorData.getMonitorId());
		AlarmDetailVO alarmDetailVO = new AlarmDetailVO().toAlarmDetailVO(monitor, AlarmBizTypeEnum.THRESHOLD, null, sensorData.getOriginTime());
		alarmDetailVO.setSensorDataId(sensorData.getId())
			.setWaveId(sensorData.getWaveId())
			.setVal(sensorData.getValue());
		alarmDetailVO.setSampleDataType(sensorData.getSampleDataType())
			.setMeasureDirection(sensorData.getMeasureDirection())
			.setStressNumber(sensorData.getNumber());
		List<AlarmDetailVO> list = new ArrayList<>();
		if (flag && Func.isNotEmpty(value)) {
			AlarmDetailVO vo = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetailVO.class));
			vo.setAlarmIndex(sensorData.getSampleDataType());
			list.add(vo);
		}
		if (Func.isNotEmpty(rmsValue)) {
			AlarmDetailVO vo = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetailVO.class));
			vo.setAlarmIndex(AlarmIndexEnum.EFFECTIVE_VALUE.getCode()).setVal(rmsValue);
			list.add(vo);
		}
		if (Func.isNotEmpty(peakValue) && peakValue.compareTo(BigDecimal.ZERO) != 0) {
			AlarmDetailVO vo = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetailVO.class));
			vo.setAlarmIndex(AlarmIndexEnum.PEAK_VALUE.getCode()).setVal(peakValue);
			list.add(vo);
		}
		if (Func.isNotEmpty(peakPeakValue) && peakPeakValue.compareTo(BigDecimal.ZERO) != 0) {
			AlarmDetailVO vo = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetailVO.class));
			vo.setAlarmIndex(AlarmIndexEnum.PEAK_PEAK_VALUE.getCode()).setVal(peakPeakValue);
			list.add(vo);
		}
		if (Func.isNotEmpty(clearanceValue) && clearanceValue.compareTo(BigDecimal.ZERO) != 0) {
			AlarmDetailVO vo = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetailVO.class));
			vo.setAlarmIndex(AlarmIndexEnum.CLEARANCE_FACTOR.getCode()).setVal(clearanceValue);
			list.add(vo);
		}
		if (Func.isNotEmpty(skewnessValue) && skewnessValue.compareTo(BigDecimal.ZERO) != 0) {
			AlarmDetailVO vo = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetailVO.class));
			vo.setAlarmIndex(AlarmIndexEnum.SKEWNESS_VALUE.getCode()).setVal(skewnessValue);
			list.add(vo);
		}
		if (Func.isNotEmpty(kurtosisValue) && kurtosisValue.compareTo(BigDecimal.ZERO) != 0) {
			AlarmDetailVO vo = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetailVO.class));
			vo.setAlarmIndex(AlarmIndexEnum.KURTOSIS_VALUE.getCode()).setVal(kurtosisValue);
			list.add(vo);
		}
		if (Func.isNotEmpty(list)) {
			list.forEach(vo -> {
				this.thresholdAlarm(vo);
			});
		}
	}

	/**
	 * 门限报警
	 *
	 * @param alarmDetailVO
	 * @return void
	 * <AUTHOR>
	 * @date 2024/2/2 11:31
	 */
	private void thresholdAlarm(AlarmDetailVO alarmDetailVO) {
		try {
			// 测量值
			BigDecimal value = alarmDetailVO.getVal();
			// 比对门限
			AlarmThreshold alarmThreshold;
			boolean flag = Arrays.stream(RealNonVibrationDataEnum.values()).anyMatch(t ->
				Func.equals(t.getCode(), alarmDetailVO.getSampleDataType()));
			if (flag) {
				alarmThreshold = alarmThresholdService.getOne(Wrappers.<AlarmThreshold>query().lambda()
					.eq(AlarmThreshold::getMonitorId, alarmDetailVO.getMonitorId())
					.eq(AlarmThreshold::getSampleDataType, alarmDetailVO.getSampleDataType()));
			} else {
				LambdaQueryWrapper<AlarmThreshold> queryWrapper = Wrappers.<AlarmThreshold>query().lambda()
					.eq(AlarmThreshold::getMonitorId, alarmDetailVO.getMonitorId())
					.eq(AlarmThreshold::getSampleDataType, alarmDetailVO.getSampleDataType())
					.eq(AlarmThreshold::getQuotaType, alarmDetailVO.getAlarmIndex())
					.eq(Func.isNotEmpty(alarmDetailVO.getStressNumber()), AlarmThreshold::getStressNumber, alarmDetailVO.getStressNumber());
				if (Func.isEmpty(alarmDetailVO.getStressNumber()) && SampledDataTypeEnum.STRESS != SampledDataTypeEnum.getByCode(alarmDetailVO.getSampleDataType())) {
					queryWrapper.eq(AlarmThreshold::getMeasureDirection, alarmDetailVO.getMeasureDirection()).isNull(AlarmThreshold::getStressNumber);
				}
				alarmThreshold = alarmThresholdService.getOne(queryWrapper);
			}
			if (Func.isEmpty(alarmThreshold)) {
				log.info("未设置门限不进行比对：{}", alarmDetailVO);
				if (AlarmIndexEnum.EFFECTIVE_VALUE == AlarmIndexEnum.getByCode(alarmDetailVO.getAlarmIndex())) {
					this.setRedisData(KEY_ALARM_HANDLER[0], alarmDetailVO.getWaveId(), alarmDetailVO.getAlarmDataTime(), 1);
				}
				return;
			}
			if (Func.isEmpty(alarmThreshold.getFirstThresholdLower()) && Func.isEmpty(alarmThreshold.getFirstThresholdUpper())
				&& Func.isEmpty(alarmThreshold.getSecondThresholdLower()) && Func.isEmpty(alarmThreshold.getSecondThresholdUpper())
				&& Func.isEmpty(alarmThreshold.getThirdThresholdLower()) && Func.isEmpty(alarmThreshold.getThirdThresholdUpper())
				&& Func.isEmpty(alarmThreshold.getFourthThresholdLower()) && Func.isEmpty(alarmThreshold.getFourthThresholdUpper())) {
				log.info("未设置门限不进行比对：{}", alarmDetailVO);
				if (AlarmIndexEnum.EFFECTIVE_VALUE == AlarmIndexEnum.getByCode(alarmDetailVO.getAlarmIndex())) {
					this.setRedisData(KEY_ALARM_HANDLER[0], alarmDetailVO.getWaveId(), alarmDetailVO.getAlarmDataTime(), 1);
				}
				return;
			}
			CommonThresholdDTO compareThreshold = Objects.requireNonNull(BeanUtil.copy(alarmThreshold, CommonThresholdDTO.class));
			AlarmLevelEnum alarmLevel;
			switch (ThresholdAlarmTypeEnum.getByCode(compareThreshold.getAlarmType())) {
				case HORIZONTAL_LOWER_LIMIT:
					alarmLevel = this.horizontalLowerLimitCompare(compareThreshold, value);
					break;
				case INSIDE_WINDOW:
					alarmLevel = this.insideWindowCompare(compareThreshold, value);
					break;
				case OUTSIDE_WINDOW:
					alarmLevel = this.outsideWindowCompare(compareThreshold, value);
					break;
				default:
					alarmLevel = this.horizontalOverrunCompare(compareThreshold, value);
			}
			if (alarmLevel != null) {
				alarmDetailVO.setAlarmLevel(alarmLevel.getCode()).setUnit(alarmThreshold.getQuotaUnit());
			}
			if (Func.isNotEmpty(alarmDetailVO.getAlarmLevel())) {
				// 振动数据更新波形报警等级(比较大小更新)
				if (Func.isNotEmpty(alarmDetailVO.getWaveId())) {
					updateWaveAlarmLevel(alarmDetailVO.getWaveId(), alarmDetailVO.getAlarmLevel());
				}
				// 更新原始采样数据表报警等级(比较大小更新)
				updateSensorDataAlarm(alarmDetailVO.getWaveId(), alarmDetailVO.getAlarmLevel(),
					DateUtil.formatDateTime(alarmDetailVO.getAlarmDataTime()));
				// 设备报警
				this.addAlarm(alarmDetailVO);
			} else {
				if (AlarmIndexEnum.EFFECTIVE_VALUE == AlarmIndexEnum.getByCode(alarmDetailVO.getAlarmIndex())) {
					this.setRedisData(KEY_ALARM_HANDLER[0], alarmDetailVO.getWaveId(), alarmDetailVO.getAlarmDataTime(), 1);
				}
			}
		} catch (Exception e) {
			log.error("处理数据报警异常!", e);
			e.printStackTrace();
		}
	}

	public MonitorDTO basicInfoBySensorCode(String sensorCode) {
		SensorInstance sensorInstance = sensorInstanceService.getOne(Wrappers.<SensorInstance>query().lambda()
			.eq(SensorInstance::getCode, sensorCode));
		if (sensorInstance == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		Monitor monitor = monitorService.getById(sensorInstance.getMonitorId());
		if (monitor == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		MonitorDTO dto = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorDTO.class));
		Equipment equipment = equipmentService.getById(monitor.getEquipmentId());
		if (equipment == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		EquipmentDTO equipmentDTO = Objects.requireNonNull(BeanUtil.copy(equipment, EquipmentDTO.class));
		dto.setEquipmentInfo(equipmentDTO);
		Device device = deviceService.getById(equipment.getDeviceId());
		if (device == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		DeviceDTO deviceDTO = Objects.requireNonNull(BeanUtil.copy(device, DeviceDTO.class));
		dto.setDeviceInfo(deviceDTO);
		return dto;
	}

	/**
	 * 处理AI诊断数据
	 *
	 * @param vo
	 * @return void
	 * <AUTHOR>
	 * @date 2023/07/14 11:35
	 */
	public void handleAiAlarmData(AiAlarmResultVO vo) {
		log.info("=====》处理AI诊断数据：{}", JSON.toJSONString(vo));
		// AI诊断正常
		if (Func.equals(Func.toInt(StringPool.ZERO), vo.getResultCode())) {
			this.setRedisData(KEY_ALARM_HANDLER[2], vo.getWaveId(), vo.getOriginTime(), 1);
			return;
		}
		String sensorCode = vo.getSensorCode();
		// 根据传感器编码查询设备和部位信息
		MonitorDTO monitor = basicInfoBySensorCode(sensorCode);
		if (Func.isNotEmpty(monitor)) {
			EquipmentDTO equipment = monitor.getEquipmentInfo();
			// AI报警默认报警等级1级
			AlarmDetailVO alarmDetailVO = new AlarmDetailVO(AlarmBizTypeEnum.INTELLIGENCE, vo.getWaveId(), AlarmLevelEnum.LEVEL_ONE);
			alarmDetailVO.setTenantId(equipment.getTenantId()).setDevicePath(equipment.getPath())
				.setEquipmentName(equipment.getName()).setPathName(equipment.getPathName())
				.setCreateUser(equipment.getCreateUser()).setCreateDept(equipment.getCreateDept())
				.setUpdateUser(equipment.getUpdateUser());
			alarmDetailVO.setEquipmentId(equipment.getId()).setMonitorId(monitor.getId()).setMonitorName(monitor.getName())
				.setMonitorPath(monitor.getPath()).setFirstAlarmTime(com.snszyk.core.tool.utils.DateUtil.now())
				.setLastAlarmTime(vo.getOriginTime()).setAlarmDataTime(vo.getOriginTime());
			AlarmDetail alarmDetail;
			Alarm alarm = alarmService.getOne(Wrappers.<Alarm>query().lambda().eq(Alarm::getEquipmentId, equipment.getId())
				.eq(Alarm::getTenantId, equipment.getTenantId()).ne(Alarm::getStatus, AlarmStatusEnum.CLOSED.getCode()));
			boolean flag = true;
			if (alarm != null) {
				alarmDetail = alarmDetailService.getOne(Wrappers.<AlarmDetail>query().lambda().eq(AlarmDetail::getAlarmId, alarm.getId())
					.eq(AlarmDetail::getMonitorId, monitor.getId()).eq(AlarmDetail::getAlarmType, AlarmBizTypeEnum.INTELLIGENCE.getCode()));
				if (Func.isEmpty(alarmDetail)) {
					alarmDetail = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetail.class));
					alarmDetail.setAlarmId(alarm.getId());
				} else {
					alarmDetail.setLastAlarmTime(vo.getOriginTime()).setAlarmDataTime(vo.getOriginTime());
				}
				alarm.setAlarmType(AlarmBizTypeEnum.INTELLIGENCE.getCode());
			} else {
				alarm = Objects.requireNonNull(BeanUtil.copy(monitor, Alarm.class));
				alarm.setEquipmentName(equipment.getName()).setDevicePath(equipment.getPath()).setPathName(equipment.getPathName())
					.setAlarmLevel(AlarmLevelEnum.LEVEL_ONE.getCode()).setAlarmType(AlarmBizTypeEnum.INTELLIGENCE.getCode())
					.setFirstAlarmTime(vo.getOriginTime()).setId(null);
				alarm.setStatus(AlarmStatusEnum.WAIT_HANDLE.getCode());
				alarmDetail = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetail.class));
			}
			if (flag) {
				// 报警
				alarm.setLastAlarmTime(vo.getOriginTime());
				alarmService.saveOrUpdate(alarm);
				// 报警明细
				alarmDetail.setAlarmId(alarm.getId());
				alarmDetailService.saveOrUpdate(alarmDetail);
				// 报警记录
				AlarmRecord alarmRecord = Objects.requireNonNull(BeanUtil.copy(alarmDetail, AlarmRecord.class));
				alarmRecord.setAlarmDetailId(alarmDetail.getId()).setAlarmTime(vo.getOriginTime()).setWaveId(vo.getWaveId()).setId(null);
				alarmRecordService.save(alarmRecord);
				// AI报警生成异常
				this.setRedisData(KEY_ALARM_HANDLER[2], alarmRecord.getWaveId(), alarmRecord.getAlarmTime(), 2);
				this.sensorDataHandler(equipment.getTenantId(), alarmRecord.getWaveId() + StringPool.COLON +
					alarmRecord.getAlarmTime().getTime() + StringPool.COLON + KEY_ALARM_HANDLER[2]);
				// 2024.6.6
				DiagnosisRecordVO diagnosisRecordVO = new DiagnosisRecordVO(alarm.getId(), equipment.getId());
				diagnosisRecordVO.setDiagnosisType(AlarmBizTypeEnum.INTELLIGENCE.getCode()).setConclusion(vo.getResult());
				List<DiagnosisRecordVO> diagnosisRecordList = diagnosisRecordService.recordsByAlarmId(diagnosisRecordVO);
				if (Func.isEmpty(diagnosisRecordList)) {
					// 保存诊断结果
					if (Func.isNotEmpty(vo.getResult())) {
						DiagnosisRecordVO diagnosisRecord = new DiagnosisRecordVO().toDiagnosisRecord(alarm);
						diagnosisRecord.setDiagnosisType(DiagnosisTypeEnum.INTELLIGENCE.getCode()).setConclusion(vo.getResult())
							.setDiagnoseTime(vo.getOriginTime()).setDiagnoseUser(Func.toLong(IntelligentDiagnosisTypeEnum.AI_MODEL.getCode()));
						diagnosisRecordService.submit(diagnosisRecord);
					}
				}
				// 更新设备和测点报警等级
				MonitorVO monitorVO = new MonitorVO(alarmDetailVO.getMonitorId(), alarmDetailVO.getAlarmLevel());
				log.info("=====》AI模型测点报警：================{}", monitorVO.getId() + ":" + monitorVO.getAlarmLevel());
				EquipmentVO equipmentVO = new EquipmentVO(alarmDetailVO.getEquipmentId(), alarmDetailVO.getAlarmLevel());
				AlarmLevelVO alarmLevelVO = new AlarmLevelVO(equipmentVO, monitorVO, null);
				log.info("=====》AI模型设备报警：================{}", equipmentVO.getId() + ":" + equipmentVO.getAlarmLevel());
				if (Func.isNotEmpty(alarmLevelVO)) {
					log.info("AI模型报警发送MQ消息给设备更新报警等级：{}", alarmLevelVO);
					rabbitTemplate.convertAndSend(EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
						EolmConstant.Rabbit.ROUTING_ALARM_LEVEL_UPDATE, alarmLevelVO);
				}
				// 振动数据更新波形报警等级(比较大小更新)
				if (Func.isNotEmpty(alarmDetailVO.getWaveId())) {
					updateWaveAlarmLevel(vo.getWaveId(), alarmDetailVO.getAlarmLevel());
				}
				// 更新原始采样数据表报警等级(比较大小更新)
				updateSensorDataAlarm(vo.getWaveId(), alarmDetailVO.getAlarmLevel(),
					DateUtil.formatDateTime(vo.getOriginTime()));
				// 发送报警消息
				alarmDetailVO.setTenantId(equipment.getTenantId()).setAlarmId(alarm.getId());
				this.sendMessage(equipment.getCode(), alarmDetailVO);
			}
		}
	}

	public Boolean updateWaveAlarmLevel(Long waveId, Integer alarmLevel) {
		boolean ret = true;
		Wave wave = waveService.getById(waveId);
		if (alarmLevel > wave.getAlarmLevel()) {
			ret = waveService.update(Wrappers.<Wave>update().lambda()
				.set(Wave::getAlarmLevel, alarmLevel).eq(Wave::getId, waveId));
		}
		return ret;
	}

	public Boolean updateSensorDataAlarm(Long waveId, Integer alarmLevel, String originTime) {
		SensorDataVO sensorData = new SensorDataVO();
		sensorData.setWaveId(waveId);
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
		Date date = com.snszyk.core.tool.utils.DateUtil.parse(originTime, com.snszyk.core.tool.utils.DateUtil.PATTERN_DATETIME);
		List<JSONObject> list = influxdbTools.queryData(date.getTime(), date.getTime() + 1000L, jsonObject);
		list.forEach(action -> {
			SensorDataDTO dto = action.toJavaObject(SensorDataDTO.class);
			if (alarmLevel > dto.getAlarmLevel()) {
				dto.setAlarmLevel(alarmLevel);
				influxdbTools.update(Func.toStr(dto.getMonitorId()),
					Func.toStr(waveId),
					(JSONObject) JSONObject.toJSON(dto),
					dto.getOriginTime().getTime(),
					Func.isNotEmpty(dto.getWaveformUrl()));

			}
		});
		return Boolean.TRUE;
	}

	/**
	 * 处理机理模型诊断数据
	 *
	 * @param vo
	 * @return void
	 * <AUTHOR>
	 * @date 2024/1/10 17:26
	 */
	public void handleModelAlarmData(ModelAlarmResultVO vo) {
		log.info("=====》处理机理模型诊断数据：{}", JSON.toJSONString(vo));
		Date originTime = DateUtil.parseDateTime(vo.getOriginTime());

		MonitorDTO monitor = monitorService.getByIdIncludeEquipment(vo.getMonitorId());
		//if(!isInAlarmPeriod(monitor.getTenantId(), vo.getWaveId(), vo.getAlarmLevel())){
		//	return;
		//}
		AlarmDetailVO alarmDetailVO = new AlarmDetailVO().toAlarmDetailVO(monitor, AlarmBizTypeEnum.MECHANISM, vo.getAlarmLevel(), originTime);
		Alarm alarm = alarmService.getOne(Wrappers.<Alarm>query().lambda().eq(Alarm::getEquipmentId, monitor.getEquipmentInfo().getId())
			.eq(Alarm::getTenantId, monitor.getTenantId()).ne(Alarm::getStatus, AlarmStatusEnum.CLOSED.getCode()));
		boolean alarmFlag = Boolean.FALSE;
		AlarmDetail alarmDetail;
		if (alarm != null) {
			alarmDetail = alarmDetailService.getOne(Wrappers.<AlarmDetail>query().lambda().eq(AlarmDetail::getAlarmId, alarm.getId())
				.eq(AlarmDetail::getMonitorId, monitor.getId()).eq(AlarmDetail::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode()));
			if (Func.isEmpty(alarmDetail)) {
				alarmFlag = Boolean.TRUE;
				alarmDetail = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetail.class));
				alarmDetail.setAlarmId(alarm.getId());
			} else {
				if (vo.getAlarmLevel() >= alarmDetail.getAlarmLevel()) {
					alarmFlag = Boolean.TRUE;
					alarmDetail.setAlarmLevel(vo.getAlarmLevel()).setLastAlarmTime(originTime).setAlarmDataTime(originTime);
				}
			}
			if (vo.getAlarmLevel() >= alarm.getAlarmLevel()) {
				alarmFlag = Boolean.TRUE;
				alarm.setAlarmLevel(vo.getAlarmLevel()).setAlarmType(AlarmBizTypeEnum.MECHANISM.getCode());
			}
		} else {
			alarmFlag = Boolean.TRUE;
			alarm = Objects.requireNonNull(BeanUtil.copy(monitor, Alarm.class));
			alarm.setEquipmentName(monitor.getEquipmentInfo().getName()).setDevicePath(monitor.getEquipmentInfo().getPath())
				.setPathName(monitor.getEquipmentInfo().getPathName()).setAlarmLevel(vo.getAlarmLevel()).setAlarmType(AlarmBizTypeEnum.MECHANISM.getCode())
				.setFirstAlarmTime(originTime).setId(null);
			alarm.setStatus(AlarmStatusEnum.WAIT_HANDLE.getCode());
			alarmDetail = Objects.requireNonNull(BeanUtil.copy(alarmDetailVO, AlarmDetail.class));
		}
		if (alarmFlag) {
			// 更新报警信息
			alarm.setLastAlarmTime(originTime);
			alarmService.saveOrUpdate(alarm);
			// 更新报警明细
			alarmDetail.setAlarmId(alarm.getId()).setWaveId(vo.getWaveId());
			alarmDetailService.saveOrUpdate(alarmDetail);
			// 更新原始采样数据表报警等级(比较大小更新)
			updateSensorDataAlarm(vo.getWaveId(), vo.getAlarmLevel(), vo.getOriginTime());
			// 振动数据更新波形报警等级(比较大小更新)
			updateWaveAlarmLevel(vo.getWaveId(), vo.getAlarmLevel());
			// 更新设备和测点报警等级
			AlarmLevelVO alarmLevelVO = new AlarmLevelVO();
			MonitorVO monitorVO = new MonitorVO(alarmDetailVO.getMonitorId(), alarmDetailVO.getAlarmLevel());
			alarmLevelVO.setMonitor(monitorVO);
			log.info("=====》机理模型测点报警：================{}", monitorVO.getId() + ":" + monitorVO.getAlarmLevel());
			EquipmentVO equipmentVO = new EquipmentVO(alarmDetailVO.getEquipmentId(), alarmDetailVO.getAlarmLevel());
			alarmLevelVO.setEquipment(equipmentVO);
			log.info("=====》机理模型设备报警：================{}", equipmentVO.getId() + ":" + equipmentVO.getAlarmLevel());
			if (Func.isNotEmpty(alarmLevelVO)) {
				log.info("=====》机理模型报警发送MQ消息给设备更新报警等级：{}", alarmLevelVO);
				rabbitTemplate.convertAndSend(EolmConstant.Rabbit.DIRECT_EXCHANGE_ALARM,
					EolmConstant.Rabbit.ROUTING_ALARM_LEVEL_UPDATE, alarmLevelVO);
			}
			// 发送报警消息
			alarmDetailVO.setAlarmId(alarm.getId());
			this.sendMessage(monitor.getEquipmentInfo().getCode(), alarmDetailVO);
		}
		// 保存报警记录
		AlarmRecord alarmRecord = Objects.requireNonNull(BeanUtil.copy(alarmDetail, AlarmRecord.class));
		alarmRecord.setId(null).setAlarmDetailId(alarmDetail.getId()).setWaveId(vo.getWaveId())
			.setAlarmLevel(vo.getAlarmLevel()).setAlarmTime(originTime)
			.setFaultType(vo.getFaultType()).setFaultGrade(vo.getFaultGrade());
		alarmRecordService.save(alarmRecord);
		// 机理报警生成异常
		this.sensorDataHandler(monitor.getTenantId(), vo.getWaveId() + StringPool.COLON +
			originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[1]);
		// 保存诊断图谱
		AlarmRecordVO alarmRecordVO = Objects.requireNonNull(BeanUtil.copy(alarmRecord, AlarmRecordVO.class));
		alarmRecordVO.setOriginTime(alarmDetailVO.getAlarmDataTime());
		log.info("=====》保存机理模型报警记录图谱：================{}", alarmRecordVO);
		this.saveWaveForm(alarmRecordVO);
		// 保存诊断结果
		if (Func.isNotEmpty(vo.getResult())) {
			this.saveDiagnosisRecord(vo, alarm);
		}
		// 润滑不良报警
		if (Func.equals(StringPool.ONE, vo.getLub())) {
			this.sendToLubricateAlarm(vo);
		}
	}

	/**
	 * 发送润滑报警
	 *
	 * @param vo
	 * @return void
	 * <AUTHOR>
	 * @date 2024/4/11 17:23
	 */
	private void sendToLubricateAlarm(ModelAlarmResultVO vo) {
		WaveDTO wave = tempWaveInfo(vo.getWaveId());
		if (Func.isNotEmpty(wave)) {
			vo.setTempWaveId(wave.getId());
		}
		MonitorDTO monitor = monitorService.getByIdIncludeEquipment(vo.getMonitorId());
		if (Func.isNotEmpty(monitor)) {
			Long runningTime = monitor.getEquipmentInfo().getRunningTime();
			if (runningTime != null && runningTime > 0L) {
				BigDecimal rt = BigDecimal.valueOf(runningTime)
					.divide(BigDecimal.valueOf(1000 * 60 * 60), 2, RoundingMode.HALF_UP);
				vo.setRunTime(rt.toString());
			}
		}
		rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_LUBRICATE_STRATEGY,
			EolmConstant.Rabbit.ROUTING_LUBRICATE_STRATEGY, vo);
	}

	public WaveDTO tempWaveInfo(Long id) {
		Wave wave = waveService.getById(id);
		if (Func.isNotEmpty(wave)) {
			List<Wave> tempWaveList = waveService.list(Wrappers.<Wave>query().lambda()
				.eq(Wave::getMonitorId, wave.getMonitorId()).eq(Wave::getSensorCode, wave.getSensorCode())
				.eq(Wave::getSampleDataType, SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode()));
			if (Func.isNotEmpty(tempWaveList)) {
				WaveDTO dto = Objects.requireNonNull(BeanUtil.copy(tempWaveList.get(0), WaveDTO.class));
				return dto;
			}
		}
		return null;
	}

	/**
	 * 保存诊断结果
	 *
	 * @param vo
	 * @param alarm
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/3/13 9:39
	 */
	private boolean saveDiagnosisRecord(ModelAlarmResultVO vo, Alarm alarm) {
		DiagnosisRecordVO diagnosisRecord = new DiagnosisRecordVO().toDiagnosisRecord(alarm);
		diagnosisRecord.setDiagnosisType(DiagnosisTypeEnum.INTELLIGENCE.getCode())
			.setDiagnoseTime(com.snszyk.core.tool.utils.DateUtil.now())
			.setDiagnoseUser(Func.toLong(IntelligentDiagnosisTypeEnum.MECHANISM_MODEL.getCode()));
		// 智能诊断结论和检修建议
		//return diagnosisClient.submitDiagnosisRecord(diagnosisRecord).isSuccess();
		diagnosisRecordService.submit(diagnosisRecord);
		return true;
	}

	/**
	 * 判断是否在报警周期内
	 *
	 * @param tenantId
	 * @param waveId
	 * @param alarmLevel
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/1/19 13:56
	 */
	public boolean isInAlarmPeriod(String tenantId, Long waveId, Integer alarmLevel) {
		// 报警周期
		BigDecimal alarmPeriod = null;

		GlobalConfigVO globalConfig = globalConfigService.getSetting(tenantId, GlobalConfigCategoryEnum.ALARM_PERIOD.getCode());

		if (Func.isNotEmpty(globalConfig) && Func.isNotEmpty(globalConfig.getAlarmPeriodConfig())) {
			alarmPeriod = globalConfig.getAlarmPeriodConfig().getPeriod();
		}

		//BigDecimal alarmPeriod = szykRedis.get(AuthUtil.getTenantId().concat(":").concat(ALARM_PERIOD_REDIS_KEY));
		log.info("报警周期=================：{}", alarmPeriod);
		if (Func.isEmpty(alarmPeriod)) {
			return true;
		}
		LambdaQueryWrapper<AlarmRecord> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
			.orderByDesc(AlarmRecord::getAlarmTime).last(" limit 1");
		AlarmRecord alarmRecord = alarmRecordService.getBaseMapper().selectOne(queryWrapper);
		if (Func.isEmpty(alarmRecord)) {
			return true;
		}
		BigDecimal interval = alarmPeriod.multiply(new BigDecimal(3600000));
		log.info("报警周期（毫秒）=================：{}", interval);
		if (System.currentTimeMillis() >= (alarmRecord.getAlarmTime().getTime() + interval.longValue())) {
			log.info("系统当前时间（毫秒）=================：{}", System.currentTimeMillis());
			log.info("上次最新报警时间（毫秒）=================：{}", alarmRecord.getAlarmTime().getTime());
			return true;
		} else {
			Alarm alarm = alarmService.getById(alarmRecord.getAlarmId());
			if (alarmLevel > alarm.getAlarmLevel()) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 判断是否在报警周期内
	 *
	 * @param alarmLevel
	 * @param tenantId
	 * @param monitorId
	 * @param waveId
	 * @param alarmIndex
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/06/14 15:06
	 */
	public boolean isInAlarmPeriod(Integer alarmLevel, String tenantId, Long monitorId, Long waveId, String alarmIndex) {
		// 报警周期
		BigDecimal alarmPeriod = null;

		GlobalConfigVO globalConfig = globalConfigService.getSetting(tenantId, GlobalConfigCategoryEnum.ALARM_PERIOD.getCode());

		if (Func.isNotEmpty(globalConfig) && Func.isNotEmpty(globalConfig.getAlarmPeriodConfig())) {
			alarmPeriod = globalConfig.getAlarmPeriodConfig().getPeriod();
		}

		//BigDecimal alarmPeriod = szykRedis.get(AuthUtil.getTenantId().concat(":").concat(ALARM_PERIOD_REDIS_KEY));
		log.info("报警周期=================：{}", alarmPeriod);
		if (Func.isEmpty(alarmPeriod)) {
			return true;
		}
		AlarmDetail alarmDetail = alarmDetailService.queryAlarmDetail(monitorId, waveId, alarmIndex);
		if (Func.isEmpty(alarmDetail)) {
			return true;
		}
		BigDecimal interval = alarmPeriod.multiply(new BigDecimal(3600000));
		log.info("报警周期（毫秒）=================：{}", interval);
		if (System.currentTimeMillis() >= (alarmDetail.getLastAlarmTime().getTime() + interval.longValue())) {
			log.info("系统当前时间（毫秒）=================：{}", System.currentTimeMillis());
			log.info("上次最新报警时间（毫秒）=================：{}", alarmDetail.getLastAlarmTime().getTime());
			return true;
		} else {
			Alarm alarm = alarmService.getById(alarmDetail.getAlarmId());
			if (alarmLevel > alarm.getAlarmLevel()) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 水平超限比对
	 *
	 * @param alarmThreshold
	 * @param value
	 * @return com.snszyk.zbusiness.alarm.enums.AlarmLevelEnum
	 * <AUTHOR>
	 * @date 2023/04/20 11:38
	 */
	private AlarmLevelEnum horizontalOverrunCompare(CommonThresholdDTO alarmThreshold, BigDecimal value) {
		AlarmLevelEnum alarmLevel = null;
		// 四级门限
		if (Func.isNotEmpty(alarmThreshold.getFourthThresholdUpper()) && value.compareTo(alarmThreshold.getFourthThresholdUpper()) >= 0) {
			return AlarmLevelEnum.LEVEL_FOUR;
		} else
			// 三级门限
			if (Func.isNotEmpty(alarmThreshold.getThirdThresholdUpper()) && value.compareTo(alarmThreshold.getThirdThresholdUpper()) >= 0) {
				return AlarmLevelEnum.LEVEL_THREE;
			} else
				// 二级门限
				if (Func.isNotEmpty(alarmThreshold.getSecondThresholdUpper()) && value.compareTo(alarmThreshold.getSecondThresholdUpper()) >= 0) {
					return AlarmLevelEnum.LEVEL_TWO;
				} else
					// 一级门限
					if (Func.isNotEmpty(alarmThreshold.getFirstThresholdUpper()) && value.compareTo(alarmThreshold.getFirstThresholdUpper()) >= 0) {
						alarmLevel = AlarmLevelEnum.LEVEL_ONE;
					}
		return alarmLevel;
	}

	/**
	 * 水平低限比对
	 *
	 * @param alarmThreshold
	 * @param value
	 * @return com.snszyk.zbusiness.alarm.enums.AlarmLevelEnum
	 * <AUTHOR>
	 * @date 2023/04/20 11:38
	 */
	private AlarmLevelEnum horizontalLowerLimitCompare(CommonThresholdDTO alarmThreshold, BigDecimal value) {
		AlarmLevelEnum alarmLevel = null;
		// 四级门限
		if (Func.isNotEmpty(alarmThreshold.getFourthThresholdLower()) && value.compareTo(alarmThreshold.getFourthThresholdLower()) < 0) {
			return AlarmLevelEnum.LEVEL_FOUR;
		} else
			// 三级门限
			if (Func.isNotEmpty(alarmThreshold.getThirdThresholdLower()) && value.compareTo(alarmThreshold.getThirdThresholdLower()) < 0) {
				return AlarmLevelEnum.LEVEL_THREE;
			} else
				// 二级门限
				if (Func.isNotEmpty(alarmThreshold.getSecondThresholdLower()) && value.compareTo(alarmThreshold.getSecondThresholdLower()) < 0) {
					return AlarmLevelEnum.LEVEL_TWO;
				} else
					// 一级门限
					if (Func.isNotEmpty(alarmThreshold.getFirstThresholdLower()) && value.compareTo(alarmThreshold.getFirstThresholdLower()) < 0) {
						alarmLevel = AlarmLevelEnum.LEVEL_ONE;
					}
		return alarmLevel;
	}

	/**
	 * 窗内比对(值落在设定区间内则报警)
	 *
	 * @param alarmThreshold
	 * @param value
	 * @return com.snszyk.zbusiness.alarm.enums.AlarmLevelEnum
	 * <AUTHOR>
	 * @date 2023/04/20 11:38
	 */
	private AlarmLevelEnum insideWindowCompare(CommonThresholdDTO alarmThreshold, BigDecimal value) {
		AlarmLevelEnum alarmLevel = null;
		if (Func.isNotEmpty(alarmThreshold.getFourthThresholdLower()) && Func.isNotEmpty(alarmThreshold.getFourthThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getFourthThresholdLower()) >= 0 && value.compareTo(alarmThreshold.getFourthThresholdUpper()) <= 0) {
				return AlarmLevelEnum.LEVEL_FOUR;
			}
		}
		if (Func.isNotEmpty(alarmThreshold.getThirdThresholdLower()) && Func.isNotEmpty(alarmThreshold.getThirdThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getThirdThresholdLower()) >= 0 && value.compareTo(alarmThreshold.getThirdThresholdUpper()) <= 0) {
				return AlarmLevelEnum.LEVEL_THREE;
			}
		}
		if (Func.isNotEmpty(alarmThreshold.getSecondThresholdLower()) && Func.isNotEmpty(alarmThreshold.getSecondThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getSecondThresholdLower()) >= 0 && value.compareTo(alarmThreshold.getSecondThresholdUpper()) <= 0) {
				return AlarmLevelEnum.LEVEL_TWO;
			}
		}
		if (Func.isNotEmpty(alarmThreshold.getFirstThresholdLower()) && Func.isNotEmpty(alarmThreshold.getFirstThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getFirstThresholdLower()) >= 0 && value.compareTo(alarmThreshold.getFirstThresholdUpper()) <= 0) {
				alarmLevel = AlarmLevelEnum.LEVEL_ONE;
			}
		}
		return alarmLevel;
	}

	/**
	 * 窗外比对(值落在设定区间外则报警)
	 *
	 * @param alarmThreshold
	 * @param value
	 * @return com.snszyk.zbusiness.alarm.enums.AlarmLevelEnum
	 * <AUTHOR>
	 * @date 2023/04/20 11:38
	 */
	private AlarmLevelEnum outsideWindowCompare(CommonThresholdDTO alarmThreshold, BigDecimal value) {
		AlarmLevelEnum alarmLevel = null;
		if (Func.isNotEmpty(alarmThreshold.getFourthThresholdLower()) && Func.isNotEmpty(alarmThreshold.getFourthThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getFourthThresholdLower()) < 0 || value.compareTo(alarmThreshold.getFourthThresholdUpper()) > 0) {
				return AlarmLevelEnum.LEVEL_FOUR;
			}
		}
		if (Func.isNotEmpty(alarmThreshold.getThirdThresholdLower()) && Func.isNotEmpty(alarmThreshold.getThirdThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getThirdThresholdLower()) < 0 || value.compareTo(alarmThreshold.getThirdThresholdUpper()) > 0) {
				return AlarmLevelEnum.LEVEL_THREE;
			}
		}
		if (Func.isNotEmpty(alarmThreshold.getSecondThresholdLower()) && Func.isNotEmpty(alarmThreshold.getSecondThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getSecondThresholdLower()) < 0 || value.compareTo(alarmThreshold.getSecondThresholdUpper()) > 0) {
				return AlarmLevelEnum.LEVEL_TWO;
			}
		}
		if (Func.isNotEmpty(alarmThreshold.getFirstThresholdLower()) && Func.isNotEmpty(alarmThreshold.getFirstThresholdUpper())) {
			if (value.compareTo(alarmThreshold.getFirstThresholdLower()) < 0 || value.compareTo(alarmThreshold.getFirstThresholdUpper()) > 0) {
				alarmLevel = AlarmLevelEnum.LEVEL_ONE;
			}
		}
		return alarmLevel;
	}

	/**
	 * 发送报警消息
	 *
	 * @param code          设备编码
	 * @param alarmDetailVO
	 * @return void
	 * <AUTHOR>
	 * @date 2023/06/20 11:56
	 */
	private void sendMessage(String code, AlarmDetailVO alarmDetailVO) {
		MessageVo messageVo = new MessageVo();
		messageVo.setAppKey("SiDAs");
		messageVo.setTitle(MessageBizTypeEnum.EQUIPMENT_ALARM.getMessage() + "通知");
		alarmDetailVO.setAlarmLevelName(AlarmLevelEnum.getByCode(alarmDetailVO.getAlarmLevel()).getName());
		if (Func.isNotEmpty(alarmDetailVO.getAlarmIndex())) {
			alarmDetailVO.setAlarmIndexName(AlarmIndexEnum.getByCode(alarmDetailVO.getAlarmIndex()).getName());
		}
		messageVo.setContent(JSONUtil.toJsonStr(alarmDetailVO));
		messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
		messageVo.setBizType(MessageBizTypeEnum.EQUIPMENT_ALARM.getCode());
		messageVo.setBizId(String.valueOf(alarmDetailVO.getAlarmId()));
		messageVo.setSender("SiDAs");
		messageVo.setIsImmediate(YesNoEnum.YES.getCode());
		messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
		ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
		// 根据租户ID获取角色信息（roleAlias = diagnosis_analyst）
		RoleVO role = roleService.selectByRoleAlias(alarmDetailVO.getTenantId(), "diagnosis_analyst");
		List<String> userPhones = new ArrayList<>();
		if (Func.isNotEmpty(role)) {
			Long roleId = role.getId();
			// 根据角色获取用户列表
			List<User> users = userSearchService.listByRole(Collections.singletonList(roleId));
			if (Func.isNotEmpty(users)) {
				List<ReceiverInfoVo.UserVo> userVoList = users.stream().map(user -> {
					ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
					userVo.setId(user.getId());
					userVo.setRealName(user.getRealName());
					return userVo;
				}).collect(Collectors.toList());
				receiverInfoVo.setUserList(userVoList);
				userPhones = users.stream().map(user -> user.getPhone()).collect(Collectors.toList());
			} else {
				log.warn("发送设备报警消息失败：获取用户列表失败！code = {}, msg = {}");
			}
		} else {
			log.warn("发送设备报警消息失败：获取角色信息失败！code = {}, msg = {}");
		}
		messageVo.setReceiverInfoVo(receiverInfoVo);
		messageLogicService.commitMessage(messageVo);
		// 发送钉钉消息
		Tenant tenant = SysCache.getTenant(alarmDetailVO.getTenantId());
		log.info(MessageBizTypeEnum.EQUIPMENT_ALARM.getMessage() + "-发送钉钉消息：==================={}", code);
		DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(tenant.getTenantId(),
			tenant.getTenantName(), code, MessageBizTypeEnum.EQUIPMENT_ALARM.getCode(), messageVo.getContent());
		dingTalkMessage.setUserPhones(userPhones);
		rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
			EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE, dingTalkMessage);
	}

	/**
	 * 导出excel
	 *
	 * @param alarm
	 * @param response
	 * @return void
	 * <AUTHOR>
	 * @date 2023/04/18 09:06
	 */
	public void exportExcel(AlarmVO alarm, HttpServletResponse response) {
		List<AlarmExcelDTO> exportList = new ArrayList<>();
		List<AlarmDTO> results = alarmService.listExport(alarm);
		if (Func.isNotEmpty(results)) {
			results.forEach(dto -> {
				dto.setPathName(dto.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
				DictBiz dictBiz = dictBizService.getDictValue(DictBizEnum.ALARM_BIZ_STATUS.getName(), Func.toStr(dto.getStatus()));
				if (Func.isNotEmpty(dictBiz)) {
					dto.setStatusName(dictBiz.getDictValue());
				}
				dictBiz = dictBizService.getDictValue(DictBizEnum.ALARM_LEVEL.getName(), Func.toStr(dto.getAlarmLevel()));
				if (Func.isNotEmpty(dictBiz)) {
					dto.setAlarmLevelName(dictBiz.getDictValue());
				}
				dictBiz = dictBizService.getDictValue(DictBizEnum.ALARM_BIZ_TYPE.getName(), Func.toStr(dto.getAlarmType()));
				if (Func.isNotEmpty(dictBiz)) {
					dto.setAlarmTypeName(dictBiz.getDictValue());
				}
				if (Func.isNotEmpty(dto.getFirstAlarmTime())) {
					dto.setDuration(this.getDuration(dto.getFirstAlarmTime()));
				}
			});
			exportList = Func.copy(results, AlarmExcelDTO.class);
		}
		try {
			response.addHeader("Content-Type", "application/octet-stream");
			response.setCharacterEncoding("UTF-8");
			response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("报警管理.xlsx", "UTF-8"));
			EasyExcel.write(response.getOutputStream(), AlarmExcelDTO.class)
				.autoCloseStream(Boolean.FALSE)
				.sheet("报警管理")
				.doWrite(exportList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 生成诊断报告图片并保存
	 *
	 * @param alarmRecord
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/05/23 16:17
	 */
	public boolean saveWaveForm(AlarmRecordVO alarmRecord) {
		log.info("======>生成诊断图并保存：{}", alarmRecord);
		Equipment equipment = equipmentService.getById(alarmRecord.getEquipmentId());
		if (ObjectUtil.isEmpty(equipment)) {
			throw new ServiceException("查询设备信息失败");
		}
		String fileName = equipment.getName() + StringPool.DOLLAR
			+ alarmRecord.getMonitorName();
		if (Func.isNotEmpty(alarmRecord.getAlarmIndex())) {
			fileName += StringPool.DOLLAR + AlarmIndexEnum.getByCode(alarmRecord.getAlarmIndex()).getName();
		}
		Date startDate = null;
		// 查询生成图片的数据
		List<AlarmChart> alarmChartList;
		AlarmChart alarmChart = Objects.requireNonNull(BeanUtil.copy(alarmRecord, AlarmChart.class));
		alarmChart.setId(null).setAlarmRecordId(alarmRecord.getId());
		if (AlarmBizTypeEnum.THRESHOLD == AlarmBizTypeEnum.getByCode(alarmRecord.getAlarmType())) {
			alarmChartList = alarmChartService.list(Wrappers.<AlarmChart>query().lambda()
				.eq(AlarmChart::getAlarmId, alarmRecord.getAlarmId()).eq(AlarmChart::getMonitorId, alarmRecord.getMonitorId())
				.eq(AlarmChart::getAlarmIndex, alarmRecord.getAlarmIndex()).orderByDesc(AlarmChart::getAlarmTime));
		} else {
			alarmChartList = alarmChartService.list(Wrappers.<AlarmChart>query().lambda()
				.eq(AlarmChart::getAlarmId, alarmRecord.getAlarmId()).eq(AlarmChart::getMonitorId, alarmRecord.getMonitorId())
				.eq(AlarmChart::getAlarmDetailId, alarmRecord.getAlarmDetailId()).orderByDesc(AlarmChart::getAlarmTime));
		}
		if (Func.isNotEmpty(alarmChartList) && alarmChartList.size() > 0) {
			startDate = alarmChartList.get(0).getOriginTime();
		}
		try {
			SensorDataVO sensorData = new SensorDataVO(null, alarmRecord.getWaveId(), null);
			sensorData.setHasWaveData(1);
			JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
			List<JSONObject> list;
			if (Func.isNotEmpty(startDate)) {
				list = influxdbTools.queryData(startDate.getTime(), null, jsonObject, (query) -> {
					query.getQuerySQL().append("|> limit(n:1000, offset:0)");
				});
				//list = influxdbTools.queryData(startDate.getTime(), null, jsonObject);
			} else {
				list = influxdbTools.queryData(null, null, jsonObject, (query) -> {
					query.getQuerySQL().append("|> limit(n:1000, offset:0)");
				});
				//list = influxdbTools.queryData(null, null, jsonObject);
			}
			List<SensorDataDTO> sensorDataList = new ArrayList<>();
			list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
			if (Func.isNotEmpty(sensorDataList)) {
				log.info("INFLUXDB采样数据：============================{}", Objects.requireNonNull(BeanUtil.copy(sensorDataList.get(0), SensorData.class)));
				Map<String, Long> diagramMap = this.operateSaveSpectrogram(alarmRecord, fileName, sensorDataList);
				if (Func.isNotEmpty(diagramMap)) {
					if (Func.isNotEmpty(diagramMap)) {
						if (diagramMap.containsKey(KEY_DIAGRAM_CHART[0])) {
							alarmChart.setTrendChart(diagramMap.get(KEY_DIAGRAM_CHART[0]));
						}
						if (diagramMap.containsKey(KEY_DIAGRAM_CHART[1])) {
							alarmChart.setTimeDomainDiagram(diagramMap.get(KEY_DIAGRAM_CHART[1]));
						}
						if (diagramMap.containsKey(KEY_DIAGRAM_CHART[2])) {
							alarmChart.setFreqDomainDiagram(diagramMap.get(KEY_DIAGRAM_CHART[2]));
						}
						if (diagramMap.containsKey(KEY_DIAGRAM_CHART[3])) {
							alarmChart.setEnvelopDiagram(diagramMap.get(KEY_DIAGRAM_CHART[3]));
						}
						alarmChart.setAlarmTime(alarmRecord.getAlarmTime());
					}
					return alarmChartService.saveOrUpdate(alarmChart);
				}
			}
		} catch (Exception e) {
			log.warn("查询诊断报告趋势图数据失败！waveId = {}", alarmRecord.getWaveId());
			e.printStackTrace();
			return false;
		}
		return true;
	}

	/**
	 * 保存谱图文件
	 *
	 * @param alarmRecord
	 * @param fileName
	 * @param list
	 * @return java.util.Map<java.lang.String, java.lang.Long>
	 * <AUTHOR>
	 * @date 2024/2/28 9:39
	 */
	private Map<String, Long> operateSaveSpectrogram(AlarmRecordVO alarmRecord, String fileName, List<SensorDataDTO> list) {
		Map<String, Long> resultMap = null;
		// 趋势图
		List<Double> dataList = new ArrayList<>();
		List<Long> timeList = new ArrayList<>();
		if (Func.isNotEmpty(list)) {

			Equipment equipment = equipmentService.getById(alarmRecord.getEquipmentId());
			EquipmentDTO equipmentDTO = BeanUtil.copy(equipment, EquipmentDTO.class);

			WaveDTO waveDTO = waveService.getBy(alarmRecord.getWaveId());
			list.forEach(sensorDataDTO -> {
				if (sensorDataDTO.getValue().compareTo(waveDTO.getHaltLine()) > 0) {
					dataList.add(sensorDataDTO.getValue().doubleValue());
					timeList.add(sensorDataDTO.getOriginTime().getTime());
				}
			});
			if (Func.isEmpty(dataList)) {
				return null;
			}
			double[] yDataArray = dataList.stream().mapToDouble(Double::doubleValue).toArray();
			double[] xDataArray = timeList.stream().mapToDouble(Long::doubleValue).toArray();
			BufferedImage image = ChartUtil.drawChart(xDataArray, yDataArray, Boolean.TRUE, null);
			Long trendChartAttachId = alarmService.saveAlarmChart(fileName + "_趋势.png", image, equipmentDTO);
			//获取 机理模型值列表最后一条数据 对应的时域、频谱、包络波形
			Long timeDomainDiagramId = null, freqDomainDiagramId = null, envelopDiagramId = null;
			SensorDataDTO sensorData = list.get(list.size() - 1);
			if (sensorData != null) {
				String waveFormUrl = sensorData.getWaveformUrl();
				if (Func.isNotEmpty(waveFormUrl)) {
					log.info("报警功能——时域图文件url：================={}", waveFormUrl);
					log.info("报警功能——时域图：=================采样点数{}，采样频率{}", sensorData.getSamplingPoints(), sensorData.getSamplingFreq());
					final String CHARSET_NAME = "UTF-8";
					List<String> timeDomainWaveform = new ArrayList<>();
					try (BufferedReader br = Files.newBufferedReader(Paths.get(rootPath + waveFormUrl), Charset.forName(CHARSET_NAME))) {
						String line;
						while ((line = br.readLine()) != null) {
							timeDomainWaveform.add(line);
						}
					} catch (Exception e) {
						e.printStackTrace();
					}
					//timeDomainWaveform.forEach(System.out::println);
					// 时域图
					double[] timeDomainXDataArray = generateArray(0,
						sensorData.getSamplingPoints().doubleValue() * 1000 / sensorData.getSamplingFreq().doubleValue(),
						sensorData.getSamplingPoints());
					BufferedImage timeDomainImage = ChartUtil.drawChart(timeDomainXDataArray,
						jsonArray2DoubleArray(JSONArray.parseArray(timeDomainWaveform.get(0))),
						false, null);
					timeDomainDiagramId = alarmService.saveAlarmChart(fileName + "_波形.png", timeDomainImage, equipmentDTO);
					// 调用Python服务获取频域和包络波形
					JSONObject jsonObject = pythonServerFeign.freqAndEnvelope(DateUtil.formatDateTime(sensorData.getOriginTime()),
						Func.toStr(sensorData.getWaveId()), Func.toStr(alarmRecord.getMonitorId()));
					if (Func.isNotEmpty(jsonObject)) {
						// 频域图
						String freqData = jsonObject.getString("freq");
						double[] freqDataArr = jsonArray2DoubleArray(JSON.parseArray(freqData));
						double[] freqDomainXDataArray = generateArray(0,
							sensorData.getSamplingFreq().divide(BigDecimal.valueOf(2.56), 2, RoundingMode.HALF_UP).doubleValue(), freqDataArr.length
						);
						BufferedImage freqDomainImage = ChartUtil.drawChart(freqDomainXDataArray,
							freqDataArr, false, null);
						freqDomainDiagramId = alarmService.saveAlarmChart(fileName + "_频谱.png", freqDomainImage, equipmentDTO);
						// 包络图
						String envelopeData = jsonObject.getString("envelope");
						double[] envelopeDataArr = jsonArray2DoubleArray(JSON.parseArray(envelopeData));
						BufferedImage envelopeImage = ChartUtil.drawChart(freqDomainXDataArray,
							envelopeDataArr, false, null);
						envelopDiagramId = alarmService.saveAlarmChart(fileName + "_包络.png", envelopeImage, equipmentDTO);
					}
				}
			} else {
				log.info("此机理模型数据（modelCode = {}）无对应的图谱数据。", alarmRecord.getAlarmIndex());
			}
			// 报警关联诊断报告图片
			if (trendChartAttachId != null) {
				resultMap = new HashMap<>(16);
				resultMap.put(KEY_DIAGRAM_CHART[0], trendChartAttachId);
			}
			if (timeDomainDiagramId != null) {
				resultMap.put(KEY_DIAGRAM_CHART[1], timeDomainDiagramId);
			}
			if (freqDomainDiagramId != null) {
				resultMap.put(KEY_DIAGRAM_CHART[2], freqDomainDiagramId);
			}
			if (envelopDiagramId != null) {
				resultMap.put(KEY_DIAGRAM_CHART[3], envelopDiagramId);
			}
		}
		return resultMap;
	}

	/**
	 * 生成[minValue, maxValue]之间的double数组，长度为arrayLength
	 *
	 * @param minValue    最小值
	 * @param maxValue    最大值
	 * @param arrayLength 数组长度
	 * @return
	 */
	private double[] generateArray(double minValue, double maxValue, int arrayLength) {
		double[] array = new double[arrayLength];
		double step = (maxValue - minValue) / (arrayLength - 1);
		for (int i = 0; i < arrayLength; i++) {
			array[i] = minValue + step * i;
		}
		return array;
	}

	/**
	 * jsonArray数组转double数组
	 *
	 * @param jsonArray 原数据
	 * @return double数组
	 */
	private double[] jsonArray2DoubleArray(JSONArray jsonArray) {
		if (jsonArray == null || jsonArray.size() == 0) {
			return null;
		}
		double[] data = new double[jsonArray.size()];
		for (int i = 0; i < jsonArray.size(); i++) {
			data[i] = jsonArray.getDoubleValue(i);
		}
		return data;
	}

	/**
	 * 门户设备详情报警统计
	 *
	 * @param alarmStatistic
	 * @return com.snszyk.zbusiness.alarm.dto.AlarmStatisticsDTO
	 * <AUTHOR>
	 * @date 2023/07/06 09:32
	 */
	public AlarmStatisticsDTO equipmentAlarmStatistics(AlarmStatisticVO alarmStatistic) {
		AlarmStatisticsDTO dto = new AlarmStatisticsDTO();
		// 报警级别分布
		Map<Integer, Integer> levelMap = new HashMap<>(16);
		Integer levelOneCount = alarmService.count(Wrappers.<Alarm>query().lambda().eq(Alarm::getAlarmLevel, AlarmLevelEnum.LEVEL_ONE.getCode())
			.eq(Alarm::getEquipmentId, alarmStatistic.getEquipmentId()));
		levelMap.put(AlarmLevelEnum.LEVEL_ONE.getCode(), levelOneCount);
		Integer levelTwoCount = alarmService.count(Wrappers.<Alarm>query().lambda().eq(Alarm::getAlarmLevel, AlarmLevelEnum.LEVEL_TWO.getCode())
			.eq(Alarm::getEquipmentId, alarmStatistic.getEquipmentId()));
		levelMap.put(AlarmLevelEnum.LEVEL_TWO.getCode(), levelTwoCount);
		Integer levelThreeCount = alarmService.count(Wrappers.<Alarm>query().lambda().eq(Alarm::getAlarmLevel, AlarmLevelEnum.LEVEL_THREE.getCode())
			.eq(Alarm::getEquipmentId, alarmStatistic.getEquipmentId()));
		levelMap.put(AlarmLevelEnum.LEVEL_THREE.getCode(), levelThreeCount);
		Integer levelFourCount = alarmService.count(Wrappers.<Alarm>query().lambda().eq(Alarm::getAlarmLevel, AlarmLevelEnum.LEVEL_FOUR.getCode())
			.eq(Alarm::getEquipmentId, alarmStatistic.getEquipmentId()));
		levelMap.put(AlarmLevelEnum.LEVEL_FOUR.getCode(), levelFourCount);
		dto.setAlarmLevelCount(levelMap);
		dto.setAlarmCount(levelOneCount + levelTwoCount + levelThreeCount + levelFourCount);
		return dto;
	}

	/**
	 * 获取报警统计
	 *
	 * @param vo vo
	 * @return
	 */
	public List<AlarmStatisticsDTO> alarmAnalysis(AlarmStatisticVO vo) {
		return alarmService.getAlarmStatisticData(vo);
	}

	/**
	 * 导出报警统计
	 *
	 * @param vo       vo
	 * @param response response
	 */
	public void exportAlarmStatistics(AlarmStatisticVO vo, HttpServletResponse response) {
		List<AlarmStatisticsExcelDTO> exportList = new ArrayList<>();
		List<AlarmStatisticsDTO> statisticData = alarmService.getAlarmStatisticData(vo);
		if (Func.isNotEmpty(statisticData)) {
			exportList = statisticData.stream().map(data -> {
				AlarmStatisticsExcelDTO dto = Objects.requireNonNull(BeanUtil.copy(data, AlarmStatisticsExcelDTO.class));
				dto.setLevelOneCount(data.getAlarmLevelCount().get(AlarmLevelEnum.LEVEL_ONE.getCode()))
					.setLevelTwoCount(data.getAlarmLevelCount().get(AlarmLevelEnum.LEVEL_TWO.getCode()))
					.setLevelThreeCount(data.getAlarmLevelCount().get(AlarmLevelEnum.LEVEL_THREE.getCode()))
					.setLevelFourCount(data.getAlarmLevelCount().get(AlarmLevelEnum.LEVEL_FOUR.getCode()));
				dto.setStateZeroCount(data.getAlarmStateCount().get(AlarmStatusEnum.WAIT_HANDLE.getCode()))
					.setStateOneCount(data.getAlarmStateCount().get(AlarmStatusEnum.IS_FAULT.getCode()))
					.setStateTwoCount(data.getAlarmStateCount().get(AlarmStatusEnum.CLOSED.getCode()));
				dto.setAlarmHandleRate(data.getHandleRate() + StringPool.PERCENT);
				return dto;
			}).collect(Collectors.toList());
		}
		try {
			response.addHeader("Content-Type", "application/octet-stream");
			response.setCharacterEncoding("UTF-8");
			response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("报警统计.xlsx", "UTF-8"));
			EasyExcel.write(response.getOutputStream(), AlarmStatisticsExcelDTO.class)
				.autoCloseStream(Boolean.FALSE)
				.sheet("报警统计")
				.doWrite(exportList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * redis记录报警结果
	 *
	 * @param type
	 * @param waveId
	 * @param originTime
	 * @param value
	 * @return void
	 * <AUTHOR>
	 * @date 2024/5/22 15:06
	 */
	public synchronized void setRedisData(String type, Long waveId, Date originTime, Integer value) {
		log.info("设置REDIS标志位：=====================波形：{}，类型：{}，时间：{}", waveId, type, originTime.getTime());
		String key = waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + type;
		szykRedis.setEx(key, value, Duration.ofMinutes(5));
	}

	public List<AiModelDTO> aiModelByWave(Long waveId) {
		Wave wave = waveService.getById(waveId);
		Monitor monitor = monitorService.getById(wave.getMonitorId());
		List<AiModel> list = aiModelService.list(Wrappers.<AiModel>query().lambda()
			.eq(AiModel::getEquipmentId, monitor.getEquipmentId()));
		if (Func.isNotEmpty(list)) {
			return list.stream().map(model -> Objects.requireNonNull(BeanUtil.copy(model, AiModelDTO.class))).collect(Collectors.toList());
		}
		return null;
	}

	/**
	 * 二次确认前置数据处理
	 *
	 * @param key
	 * @return void
	 * <AUTHOR>
	 * @date 2024/5/22 15:06
	 */
	public synchronized void sensorDataHandler(String tenantId, String key) {
		log.info("二次确认生成异常================：{}", key);
		String[] keyArr = key.split(StringPool.COLON);
		Set<String> set = new HashSet<>();
		set.add(KEY_ALARM_HANDLER[0]);
		set.add(KEY_ALARM_HANDLER[1]);
		AtomicBoolean flag = new AtomicBoolean(false);
		Long waveId = Func.toLong(keyArr[0]);
		// 门限和AI判断
		if (!key.contains(KEY_ALARM_HANDLER[1])) {
			String mechanismKey = keyArr[0] + StringPool.COLON + keyArr[1] + StringPool.COLON + KEY_ALARM_HANDLER[1];
			if (Func.isEmpty((Object) szykRedis.get(mechanismKey))
				|| !Func.equals(2, Func.toInt(szykRedis.get(mechanismKey)))) {
				return;
			}
		}
		if (Func.isEmpty(tenantId)) {
			WaveDTO waveDTO = waveService.getBy(waveId);
			if (Func.isNotEmpty(waveDTO)) {
				tenantId = waveDTO.getTenantId();
				log.info("二次确认生成异常：================租户id：{}", tenantId);
			}

		}
		// 判断是否配置AI模型
		List<AiModelDTO> aiModel = aiModelByWave(waveId);

		if (Func.isNotEmpty(aiModel)) {
			set.add(KEY_ALARM_HANDLER[2]);
		}

		if (Func.isNotEmpty(set)) {
			log.info("门限+机理+AI生成异常================：{}", set);
			set.stream().filter(s -> !key.contains(s)).forEach(s -> {
				log.info("报警生成异常================：{}", s);
				if (Func.isNotEmpty((Object) szykRedis.get(keyArr[0] + StringPool.COLON + keyArr[1] + StringPool.COLON + s))) {
					flag.set(true);
				} else {
					flag.set(false);
				}
			});
			log.info("是否进入生成异常业务逻辑================：{}", flag.get());
			if (flag.get()) {
				// 以机理模型为主判断是否生成异常，在生成异常的过程中需叠加判断门限和AI报警的情况
				if (key.contains(KEY_ALARM_HANDLER[1])) {
					if (Func.isNotEmpty((Object) szykRedis.get(key))) {
						log.info("机理报警，缓存标志================：{}", Func.toInt(szykRedis.get(key)));
						if (Func.equals(2, Func.toInt(szykRedis.get(key)))) {
							// 二次确认逻辑
							abnormalLogicService.alarmDataHandler(tenantId, waveId, new Date(Func.toLong(keyArr[1])));
						} else {
							// 自动关闭逻辑
							abnormalLogicService.autoCloseAbnormal(tenantId, waveId, new Date(Func.toLong(keyArr[1])));
						}
					}
				}
			}
		}
	}

}
