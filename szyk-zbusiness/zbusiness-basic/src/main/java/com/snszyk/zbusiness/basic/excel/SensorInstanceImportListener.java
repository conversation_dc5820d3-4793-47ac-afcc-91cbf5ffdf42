package com.snszyk.zbusiness.basic.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.zbusiness.basic.dto.SensorInstanceBindExcelDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Map;

/**
 * 传感器实例导入监听类
 *
 * <AUTHOR>
 */
@Slf4j
public class SensorInstanceImportListener extends AnalysisEventListener<SensorInstanceBindExcelDTO> {

	/**
	 * 导入模板表头
	 */
	private static final String[] IMPORT_TEMPLATE_HEADER = {"序号", "路径", "设备名称", "部位", "传感器编码",
		"传感器类型", "安装方向(温振&应力波)", "编号(应力波)", "相位(电流)", "传感器名称", "传感器厂家",
		"传感器型号", "单值采样间隔(S)", "波形采样间隔(S)"};

	@Override
	public void invoke(SensorInstanceBindExcelDTO data, AnalysisContext context) {
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
	}

	@Override
	public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
		log.info("表头数据 excelHead= {}", headMap);
		if (headMap.size() != IMPORT_TEMPLATE_HEADER.length) {
			log.warn("headMap.size = {}, templateHeader.size = {}", headMap.size(), IMPORT_TEMPLATE_HEADER.length);
			throw new ServiceException("文件数据和模板格式不匹配！");
		}

		headMap.values().forEach(header -> {
			if (!Arrays.asList(IMPORT_TEMPLATE_HEADER).contains(header)) {
				log.warn("header = {} 不存在！", header);
				throw new ServiceException("文件数据和模板格式不匹配！");
			}
		});
	}
}
