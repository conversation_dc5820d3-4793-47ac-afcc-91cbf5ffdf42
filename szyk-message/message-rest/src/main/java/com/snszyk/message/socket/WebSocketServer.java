package com.snszyk.message.socket;

import com.snszyk.core.tool.utils.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * websocket
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/11
 */
@ServerEndpoint("/websocket/{sid}")
@Component
@Slf4j
public class WebSocketServer {

	/**
	 * 静态变量，用来记录当前在线连接数。
	 */
	private static final AtomicInteger ONLINE_COUNT = new AtomicInteger(0);

	/**
	 * 用来存放每个客户端对应的MyWebSocket对象。
	 */
	private static final ConcurrentHashMap<Long, WebSocketServer> SESSION_MAP = new ConcurrentHashMap<>();

	/**
	 * 与某个客户端的连接会话，需要通过它来给客户端发送数据
	 */
	private Session session;

	/**
	 * 接收sid - 用户id
	 */
	private String sid = "";

	/**
	 * 连接建立成功调用的方法
	 */
	@OnOpen
	public void onOpen(Session session, @PathParam("sid") String sid) {
		log.info("session.getId() is {}, sid = {}", session.getId(), sid);
		this.session = session;
		this.sid = sid;

		// 加入session集合
		WebSocketServer oldValue = SESSION_MAP.put(Long.parseLong(sid), this);
		log.info("新连接添加结果：{}", oldValue == null);
		if (oldValue == null) {
			addOnlineCount();
			log.info("{\"msg\": \"有新窗口开始监听:" + sid + ",当前在线人数为" + getOnlineCount() + "\"}");
		}

		try {
			sendMessage("{\"msg\": \"连接成功，当前在线人数为" + getOnlineCount() + "\"}");
		} catch (IOException e) {
			e.printStackTrace();
			log.error("websocket IO异常");
		}
	}

	/**
	 * 连接关闭调用的方法
	 */
	@OnClose
	public void onClose() {
		// 从map中删除
		boolean remove = SESSION_MAP.remove(Long.parseLong(sid), this);
		log.info("连接关闭，移除连接：{}", remove);
		if (remove) {
			subOnlineCount();
			log.info("有一连接关闭！当前在线人数为" + getOnlineCount());
		}
	}

	/**
	 * 收到客户端消息后调用的方法 - 暂时不需要接收客户端消息
	 *
	 * @param message 客户端发送过来的消息
	 */
	@OnMessage
	public void onMessage(String message, Session session) {
		log.info("收到来自窗口" + sid + "的信息:" + message);
        /*for (WebSocketServer item : webSocketSet) {
            try {
                item.sendMessage(message);
            } catch (IOException e) {
                log.error("接收客户端消息出错", e);
            }
        }*/
	}

	/**
	 * @param session
	 * @param error
	 */
	@OnError
	public void onError(Session session, Throwable error) {
		log.error("发生错误", error);
		error.printStackTrace();
	}

	/**
	 * 实现服务器主动推送
	 */
	public void sendMessage(String message) throws IOException {
		this.session.getBasicRemote().sendText(message);
	}

	/**
	 * 群发自定义消息
	 */
	public static void sendInfo(String message, @PathParam("sid") Long[] sidArray) {
		log.info("推送消息，sids = " + Arrays.toString(sidArray) + "，推送内容：" + message);
		List<Long> sidList = Arrays.asList(sidArray);
		if (CollectionUtil.isNotEmpty(sidList)) {
			sidList.forEach(sid -> {
				WebSocketServer webSocketServer = SESSION_MAP.get(sid);
				try {
					if (webSocketServer != null) {
						webSocketServer.sendMessage(message);
					} else {
						log.info("推送消息失败。用户{}当前不在线", sid);
					}
				} catch (Exception e) {
					e.printStackTrace();
					log.error("推送消息到" + sid + "失败", e);
				}
			});
		}
	}

	public static int getOnlineCount() {
		return ONLINE_COUNT.intValue();
	}

	public static synchronized void addOnlineCount() {
		ONLINE_COUNT.addAndGet(1);
	}

	public static synchronized void subOnlineCount() {
		ONLINE_COUNT.decrementAndGet();
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		}
		if (o == null || getClass() != o.getClass()) {
			return false;
		}
		WebSocketServer that = (WebSocketServer) o;
		return Objects.equals(session, that.session) && Objects.equals(sid, that.sid);
	}

	@Override
	public int hashCode() {
		return Objects.hash(session, sid);
	}
}
