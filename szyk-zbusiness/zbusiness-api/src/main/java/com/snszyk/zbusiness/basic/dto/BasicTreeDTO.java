/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.BasicTree;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础树表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BasicTreeDTO对象", description = "基础树表")
public class BasicTreeDTO extends BasicTree {
	private static final long serialVersionUID = 1L;

}
