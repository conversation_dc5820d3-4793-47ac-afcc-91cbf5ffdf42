/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.ops.dto.AbnormalDTO;
import com.snszyk.zbusiness.ops.dto.EquipmentMonitorSimpleDTO;
import com.snszyk.zbusiness.ops.entity.Abnormal;
import com.snszyk.zbusiness.ops.vo.AbnormalVO;
import com.snszyk.zbusiness.ops.vo.EquipmentMonitorQueryVO;
import lombok.NonNull;

import java.util.List;

/**
 * 设备异常信息表 服务类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
public interface IAbnormalService extends IService<Abnormal> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param abnormal
	 * @return
	 */
	IPage<AbnormalDTO> page(IPage<AbnormalDTO> page, AbnormalVO abnormal);

	/**
	 * 详情
	 *
	 * @param id
	 * @return
	 */
	AbnormalDTO detail(Long id);

	/**
	 * 已成故障
	 *
	 * @param abnormalId
	 * @param faultId
	 * @return
	 */
	boolean toFault(Long abnormalId, Long faultId);


	List<AbnormalDTO> listBy(List<String> tenantIds, List<Long> equipmentIds, Integer status);
	/**
	 * 设备监测分页查询
	 *
	 * @param query 查询条件
	 * @return 分页结果
	 */
	@Deprecated
	IPage<AbnormalDTO> equipmentMonitorPage(EquipmentMonitorQueryVO query);

	/**
	 * 设备监测简化分页查询
	 *
	 * @param query 查询条件
	 * @return 简化的分页结果
	 */
	IPage<EquipmentMonitorSimpleDTO> equipmentMonitorSimplePage(EquipmentMonitorQueryVO query);

	AbnormalDTO getByEquipmentId(@NonNull Long equipmentId);
}
