package com.snszyk.message.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 未读消息数
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class MessageCountDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 是否已读
	 */
	@ApiModelProperty("是否已读")
	private Integer hasRead;
	/**
	 * 异常报警数量（每日异常+设备异常）
	 */
	@ApiModelProperty("异常报警数量（每日异常+设备异常）")
	private Integer abnormalCount;
	/**
	 * 诊断报告数量
	 */
	@ApiModelProperty("诊断报告数量")
	private Integer reportCount;

	/**
	 * 初始化
	 */
	public void init() {
		this.abnormalCount = 0;
		this.reportCount = 0;
	}

}
