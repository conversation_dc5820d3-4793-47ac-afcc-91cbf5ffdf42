package com.snszyk.zbusiness.basic.rabbit.bussiness;

import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.enums.NonVibrationDataEnum;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.ICollectionStationChannelService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 1、电流保存数据后，将最新的数据存到Redis
 *		key为 "sensorCode:sensorInstanceParamId:sampleDataType"
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ElectricSaveRedisBusiness extends AbstractBusiness {

	private final ICollectionStationChannelService collectionStationChannelService;
	private final SzykRedis szykRedis;

	@Override
	public String getCommand() {
		return Command.ELECTRIC_COMMAND;
	}

	@Override
	public void business(MessageBean message) {
		super.business(message);
		// 传感器数据最新数据
		if (SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode().equals(message.getType())) {
			// 温度
			String tempKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
				+ StringPool.COLON + message.getType();
			szykRedis.set(tempKey, message.getValue());
		} else {
			// 电流数据 - value即是有效值
			if (message.getValue() != null) {
				String effectiveKey = message.getId() + StringPool.COLON + message.getSensorInstanceParamId()
					+ StringPool.COLON + NonVibrationDataEnum.EFFECTIVE_VALUE.getCode();
				szykRedis.set(effectiveKey, message.getValue());
			}
		}
	}

}
