/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 波形配置表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-02-07
 */
@Data
@Accessors(chain = true)
public class WaveConfigDetailDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 配置id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "配置id")
	private Long id;
	/**
	 * 波形名称
	 */
	@ApiModelProperty(value = "波形名称")
	private String waveName;
	/**
	 * 采样频率（KHz）
	 */
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal samplingFreq;
	/**
	 * 采样点数
	 */
	@ApiModelProperty(value = "采样点数")
	private Integer samplingPoints;
	/**
	 * 初始频率（Hz）
	 */
	@ApiModelProperty(value = "初始频率（Hz）")
	private BigDecimal initialFreq;
	/**
	 * 截止频率（Hz）
	 */
	@ApiModelProperty(value = "截止频率（Hz）")
	private BigDecimal cutoffFreq;
	/**
	 * 采样时间
	 */
	@ApiModelProperty(value = "采样时间")
	private BigDecimal samplingTime;
	/**
	 * 算法类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "算法类型")
	private Integer algorithmType;
	/**
	 * 算法名称
	 */
	@ApiModelProperty(value = "算法名称")
	private String algorithmName;

}
