package com.snszyk.zbusiness.basic.feign;

import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * 调用python服务获取频谱数据
 *
 * <AUTHOR>
 */
@FeignClient(
	url = "${python.server}", name = "algorithm",
	value = "algorithm",
	fallback = PythonServerFeignCallback.class
)
public interface PythonServerFeign {

	/**
	 * 1：频谱相位
	 */
	Integer WAVE_TYPE_FREQ_PHASE = 1;

	/**
	 * 2：bode相位
	 */
	Integer WAVE_TYPE_BODE_PHASE = 2;

	/**
	 * 3：bode振幅
	 */
	Integer WAVE_TYPE_BODE_AMPLITUDE = 3;

	/**
	 * 4：cepstrum图
	 */
	Integer WAVE_TYPE_CEPSTRUM = 4;

	/**
	 * 5：速度图
	 */
	Integer WAVE_TYPE_VELOCITY = 5;

	/**
	 * 机理模型诊断时不发送报警mq
	 */
	String SEND_ALARM_NO = "0";

	/**
	 * 机理模型诊断时发送报警mq
	 */
	String SEND_ALARM_YES = "1";

	/**
	 * 频域频谱图
	 * @param originTime 传感器数据时间
	 * @param waveId 波形配置id
	 * @param monitorId 传感器数据表名
	 * @return
	 */
	@GetMapping("/freqDomainWave")
	List<BigDecimal> freqDomainWave(@RequestParam("origin_time") String originTime,
									@RequestParam("wave_id") String waveId,
									@RequestParam("monitor_id") String monitorId);

	/**
	 * 频谱和包络谱
	 * @param originTime 传感器数据时间
	 * @param waveId 波形配置id
	 * @param monitorId 传感器数据表名
	 * @return {"freq": list(double), "envelope": list(double)}
	 */
	@GetMapping("/freqAndEnvelope")
	JSONObject freqAndEnvelope(@RequestParam("origin_time") String originTime,
							   @RequestParam("wave_id") String waveId,
							   @RequestParam("monitor_id") String monitorId);

	/**
	 * 其他谱图波形
	 * @param originTime 传感器数据时间
	 * @param waveId 波形配置id
	 * @param type 频谱图类型（1-频谱相位；2-波特图相位（已删除）；3-波特图振幅（已删除）；4-倒谱图；5-速度图）
	 * @param monitorId 传感器数据表名
	 * @return
	 */
	@GetMapping("/wave")
	List<BigDecimal> wave(@RequestParam("origin_time") String originTime,
						  @RequestParam("wave_id") String waveId,
						  @RequestParam("wave_type") Integer type,
						  @RequestParam("monitor_id") String monitorId);


	/**
	 * bode图
	 * @param originTime 传感器数据时间
	 * @param waveId 波形配置id
	 * @param monitorId 传感器数据表名
	 * @return {"bode": list(bode_value), "angle": list(bode_angle), "freq":list(freq)}  bode幅值，angle相位
	 */
	@GetMapping("/bode")
	JSONObject bode(@RequestParam("origin_time") String originTime,
					@RequestParam("wave_id") String waveId,
					@RequestParam("monitor_id") String monitorId);

	/**
	 * 瀑布图
	 * @param originTime 传感器数据时间，JSONArray.toString()转。
	 * @param waveId 波形配置id
	 * @param monitorId 传感器数据表名
	 * @return
	 */
	@GetMapping("/waterfall")
	List<List<BigDecimal>> waterfall(@RequestParam("origin_time") String originTime,
									 @RequestParam("wave_id") String waveId,
									 @RequestParam("monitor_id") String monitorId);

	/**
	 * 趋势图 - 增值、增幅变换
	 * @param beginTime 开始时间
	 * @param endTime 结束时间
	 * @param waveId 波形配置id
	 * @param monitorId 传感器数据表名
	 * @return {
	 *         'time': list(increase_value_series.index),
	 *         'increaseValue': list(increase_value_series.values),
	 *         'increaseRatio': list(increase_ratio_series.values)
	 *            }
	 */
	@GetMapping("/increase")
	JSONObject increase(@RequestParam("begin_time") String beginTime,
						@RequestParam("end_time") String endTime,
						@RequestParam("wave_id") String waveId,
						@RequestParam("monitor_id") String monitorId);


	/**
	 * 机理模型
	 * @param originTime 传感器数据时间
	 * @param waveId 波形配置id
	 * @param sendAlarm 是否发送报警：0-否（智能诊断时不发送）；1-是
	 * @param monitorId 传感器数据表名
	 * @return {
	 *         	"alarmLevel": fault_grade,
	 *         	"monitorId": monitor_id,
	 *         	"result": data, # data="0"表示正常；data="1,2,3" 表示存在故障，多个故障用','分隔
	 *         	"waveId": wave_id,
	 *         	"originTime": origin_time
	 *           }
	 */
	@GetMapping("/mechanism_model")
	JSONObject mechanismModel(@RequestParam("origin_time") String originTime,
							  @RequestParam("wave_id") String waveId,
							  @RequestParam("send_alarm") String sendAlarm,
							  @RequestParam("monitor_id") String monitorId);

	/**
	 * 能量脉冲接口
	 * @param originTime 传感器数据时间
	 * @param waveId 波形配置id
	 * @param monitorId 传感器数据表名
	 * @return  {"dbm": 能量值, "dbc": 润滑度值}
	 **/
	@GetMapping("/dbm")
	JSONObject dbm(@RequestParam("origin_time") String originTime,
							  @RequestParam("wave_id") String waveId,
							  @RequestParam("monitor_id") String monitorId);

	/**
	 * 获取停机线接口
	 *
	 * @param waveId 波形配置id
	 * @return  {"stopLine": 停机线}
	 **/
	@GetMapping("/getStopLineByWaveId")
	JSONObject getStopLineByWaveId(@RequestParam("wave_id") String waveId);
}
