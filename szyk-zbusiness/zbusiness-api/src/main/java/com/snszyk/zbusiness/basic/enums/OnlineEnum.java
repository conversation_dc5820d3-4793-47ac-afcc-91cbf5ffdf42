/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 在线离线枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OnlineEnum {

	/**
	 * 离线
	 */
	OFFLINE(0, "离线"),

	/**
	 * 在线
	 */
	ONLINE(1, "在线");

	final Integer code;
	final String name;

	public static OnlineEnum getByCode(Integer code){
		for (OnlineEnum value : OnlineEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
