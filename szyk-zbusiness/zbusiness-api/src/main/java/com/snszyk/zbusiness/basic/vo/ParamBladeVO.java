/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备测点参数-叶片信息视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@ApiModel(value = "MonitorParamVO对象", description = "设备测点参数-叶片信息")
public class ParamBladeVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 叶片名称
	 */
	@ApiModelProperty(value = "叶片名称")
	private String bladeName;

	/**
	 * 叶片个数
	 */
	@ApiModelProperty(value = "叶片个数")
	private Integer bladeNumber;

}
