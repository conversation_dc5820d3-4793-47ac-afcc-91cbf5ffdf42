/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.flow.engine.entity;

import lombok.Data;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntityImpl;
import com.snszyk.flow.engine.utils.FlowCache;

import java.io.Serializable;
import java.util.Date;

/**
 * FlowProcess
 *
 * <AUTHOR>
 */
@Data
public class FlowProcess implements Serializable {

	private String id;
	private String tenantId;
	private String name;
	private String key;
	private String category;
	private String categoryName;
	private Integer version;
	private String deploymentId;
	private String resourceName;
	private String diagramResourceName;
	private Integer suspensionState;
	private Date deploymentTime;

	public FlowProcess(ProcessDefinitionEntityImpl entity) {
		this.id = entity.getId();
		this.tenantId = entity.getTenantId();
		this.name = entity.getName();
		this.key = entity.getKey();
		this.category = entity.getCategory();
		this.categoryName = FlowCache.getCategoryName(entity.getCategory());
		this.version = entity.getVersion();
		this.deploymentId = entity.getDeploymentId();
		this.resourceName = entity.getResourceName();
		this.diagramResourceName = entity.getDiagramResourceName();
		this.suspensionState = entity.getSuspensionState();
	}

}
