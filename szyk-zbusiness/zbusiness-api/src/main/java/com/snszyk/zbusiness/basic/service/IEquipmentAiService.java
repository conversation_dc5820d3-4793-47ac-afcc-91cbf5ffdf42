/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.dto.EquipmentAiDTO;
import com.snszyk.zbusiness.basic.entity.EquipmentAi;
import com.snszyk.zbusiness.basic.vo.EquipmentAiVO;

/**
 * 设备AI模型表 服务类
 *
 * <AUTHOR>
 * @since 2023-07-08
 */
public interface IEquipmentAiService extends IService<EquipmentAi> {

	/**
	 * 根据设备id物理删除
	 *
	 * @param equipmentId
	 * @return
	 */
	boolean removeByEquipment(Long equipmentId);

	/**
	 * 根据测点id物理删除
	 *
	 * @param monitorId
	 * @return
	 */
	boolean removeByMonitor(Long monitorId);

	/**
	 * 设备解除引用AI模型
	 *
	 * @param equipmentId
	 * @param aiModelId
	 * @return
	 */
	boolean removeEquipmentAi(Long equipmentId, Long aiModelId);

	/**
	 * 删除传感器的ai模型
	 *
	 * @param sensorCode
	 * @return
	 */
	boolean removeBySensorCode(String sensorCode);

	/**
	 * AI配置分页
	 *
	 * @param page
	 * @param equipmentAi
	 * @return
	 */
	IPage<EquipmentAiDTO> equipmentAiPage(IPage<EquipmentAiDTO> page, EquipmentAiVO equipmentAi);

}
