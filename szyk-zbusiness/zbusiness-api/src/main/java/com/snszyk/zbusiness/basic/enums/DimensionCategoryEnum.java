/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 维度枚举类
 *
 * <AUTHOR>
 * @date 2023/05/17 15:37
 **/
@Getter
@AllArgsConstructor
public enum DimensionCategoryEnum {

	/**
	 * 2D
	 */
	TWO_DIMENSION(0, "2D"),

	/**
	 * 3D
	 */
	THREE_DIMENSION(1, "3D"),

	;

	final Integer code;
	final String name;

	public static DimensionCategoryEnum getByCode(Integer code) {
		for (DimensionCategoryEnum value : DimensionCategoryEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}
		return null;
	}

}
