package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 传感器实例参数表
 * <AUTHOR>
 */
@Data
@TableName("basic_sensor_instance_param")
@Accessors(chain = true)
@EqualsAndHashCode()
@ApiModel(value = "传感器实例参数表")
public class SensorInstanceParam implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	private Long id;

	/**
	 * 传感器实例id
	 */
	@ApiModelProperty(value = "传感器实例id")
	@JsonSerialize(using = ToStringSerializer.class)
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	private Long instanceId;

	/**
	 * 参数名称
	 */
	@ApiModelProperty(value = "参数名称")
	private String paramName;

	/**
	 * 采样数据类型（字典：sampled_data_type）
	 */
	@ApiModelProperty(value = "采样数据类型（字典：sampled_data_type）")
	private String sampleDataType;

	/**
	 * 是否振动（0：非振动:1：振动）
	 */
	@ApiModelProperty(value = "是否振动（0：非振动:1：振动）")
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer vibrationType;

	/**
	 * 轴方向（0:z轴，1:x轴，2:y轴）
	 */
	@ApiModelProperty(value = "轴方向（0:z轴，1:x轴，2:y轴）")
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer axialDirection;

	/**
	 * 特征值（字典：acceleration_feature、velocity_feature、displacement_feature，仅振动类型有）
	 */
	@ApiModelProperty(value = "特征值（字典：acceleration_feature、velocity_feature、displacement_feature，仅振动类型有）")
	private String feature;

	/**
	 * 最大采样点数（字典：sampling_points）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "采样点数")
	private Integer maxSamplingPoints;

	/**
	 * 最大采样频率KHz（字典：sampling_freq）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal maxSamplingFreq;

	/**
	 * 采样点数（字典：sampling_points）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "采样点数")
	private Integer samplingPoints;

	/**
	 * 采样频率KHz（字典：sampling_freq）
	 */
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal samplingFreq;

	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("创建时间")
	private Date createTime;
}
