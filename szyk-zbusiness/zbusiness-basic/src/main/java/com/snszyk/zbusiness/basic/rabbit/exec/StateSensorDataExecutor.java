package com.snszyk.zbusiness.basic.rabbit.exec;

import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 状态类型（传感器在线状态、传感器剩余电量）传感器数据保存：不保存，只将最新数据保存到Redis
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class StateSensorDataExecutor  extends AbstractSensorDataExecutor {


	@Override
	public String getCommand() {
		// 针对传感器在线状态、传感器剩余电量 - 不存数据库，只存最新数据到Redis
		return Command.STATE_COMMAND;
	}

	@Override
	public boolean save(MessageBean message) {
		log.info("开始执行状态处理----------------------");
		// 状态类型（传感器在线状态、传感器剩余电量）传感器数据保存：不保存，只将最新数据保存到Redis
		return true;
	}

	@Override
	protected SensorData createSensorData(MessageBean message, SensorData sensorData, Wave wave) {
		return sensorData;
	}
}
