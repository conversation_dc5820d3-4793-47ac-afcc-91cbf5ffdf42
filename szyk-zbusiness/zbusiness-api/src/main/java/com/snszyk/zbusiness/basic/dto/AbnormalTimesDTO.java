package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备累计报警、故障数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "EquipmentDTO对象", description = "设备信息表")
public class AbnormalTimesDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 累计报警数
	 */
	@ApiModelProperty(value = "累计报警数")
	private Integer alarmTimes;

	/**
	 * 累计故障数
	 */
	@ApiModelProperty(value = "累计故障数")
	private Integer faultTimes;

}
