package com.snszyk.zbusiness.basic.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.SensorData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 瀑布图采样时间
 *
 * <AUTHOR>
 * @date 2023/04/17 10:30
 **/
@Data
@Accessors(chain = true)
public class SampleTimeDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 采样时间
	 */
	@ApiModelProperty(value = "采样时间")
	private Date sampleTime;

	/**
	 * 采样频率（Hz）
	 */
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal samplingFreq;

	/**
	 * 采样点数
	 */
	@ApiModelProperty(value = "采样点数")
	private Integer samplingPoints;

	/**
	 * 采样值
	 */
	@ApiModelProperty(value = "采样值")
	private BigDecimal sampleValue;

	/**
	 * 数据长度
	 */
	@ApiModelProperty(value = "数据长度")
	private String dataLength;

	/**
	 * 波形id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "波形id")
	private Long waveConfigId;

	/**
	 * 波形名称
	 */
	@ApiModelProperty(value = "波形名称")
	private String waveConfigName;

	public SampleTimeDTO(SensorData sensorData) {
		this.waveConfigId = sensorData.getWaveId();
		this.sampleTime = sensorData.getOriginTime();
		this.dataLength = sensorData.getSamplingPoints() == null ? "" : sensorData.getSamplingPoints().toString();
		this.sampleValue = sensorData.getValue();
		this.samplingFreq = sensorData.getSamplingFreq();
		this.samplingPoints = sensorData.getSamplingPoints();

	}
}
