/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.zbusiness.ai.entity.StandardDict;
import com.snszyk.zbusiness.ai.service.IStandardDictService;
import com.snszyk.zbusiness.basic.config.MonitorTableNameHandler;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.enums.SampledDataUnitEnum;
import com.snszyk.zbusiness.basic.mapper.MechanismModelMapper;
import com.snszyk.zbusiness.basic.mapper.MonitorMapper;
import com.snszyk.zbusiness.basic.mapper.SensorInstanceMapper;
import com.snszyk.zbusiness.basic.mapper.WaveMapper;
import com.snszyk.zbusiness.basic.service.*;
import com.snszyk.zbusiness.basic.vo.MonitorVO;
import com.snszyk.zbusiness.basic.vo.WaveVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 设备测点表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-13
 */
@AllArgsConstructor
@Service
public class MonitorServiceImpl extends BaseServiceImpl<MonitorMapper, Monitor> implements IMonitorService {

	private final IDeviceService deviceService;
	private final IEquipmentService equipmentService;
	private final SensorInstanceMapper sensorInstanceMapper;
	private final IMonitorModelService monitorModelService;
	private final MechanismModelMapper mechanismModelMapper;
	private final IStandardDictService standardDictService;
	private final WaveMapper waveMapper;
	private final IBasicTreeService basicTreeService;
	private final IDictBizService dictBizService;

	@Override
	public IPage<MonitorDTO> page(IPage<MonitorDTO> page, MonitorVO vo) {
		List<MonitorDTO> list;
		if (vo.getIsConfig()) {
			list = baseMapper.monitorPage(page, vo);
		} else {
			list = baseMapper.page(page, vo);
		}
		if (Func.isNotEmpty(list)) {
			list.forEach(dto -> {
				//设备名称和分类
				Equipment equipment = equipmentService.getById(dto.getEquipmentId());
				if (Func.isNotEmpty(equipment)) {
					dto.setEquipmentName(equipment.getName()).setSceneId(equipment.getSceneId());
					DictBiz dictBiz = dictBizService.getDictValue(DictBizEnum.EQ_CATEGORY.getName(), Func.toStr(equipment.getCategory()));
					if (Func.isNotEmpty(dictBiz)) {
						dto.setEquipmentCategory(dictBiz.getDictValue());
					}
				}
				Integer count = sensorInstanceMapper.selectCount(Wrappers.<SensorInstance>query().lambda()
					.eq(SensorInstance::getMonitorId, dto.getId()));
				dto.setSensorNumber(count).setPathName(dto.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
			});
		}
		return page.setRecords(list);
	}

	@Override
	public DetectionRangeDTO detectionRange(Long id) {
		Device device = deviceService.getById(id);
		if (device == null) {
			return new DetectionRangeDTO(0, 0, 0, "0");
		}

		//设备总数量
		int allEquipmentCount = equipmentService.count(Wrappers.<Equipment>lambdaQuery()
			.eq(Equipment::getIsDeleted, SzykConstant.DB_NOT_DELETED)
			.like(Equipment::getPath, device.getPath()));

		//监测设备测点数量 - 绑定了传感器的测点
		List<MonitorDTO> monitorWithSensorCodeList;
		if (Func.isNotEmpty(id)) {
			monitorWithSensorCodeList = baseMapper.detectMonitors(device.getPath());
		} else {
			monitorWithSensorCodeList = baseMapper.detectMonitors(null);
		}
		if (Func.isEmpty(monitorWithSensorCodeList)) {
			return new DetectionRangeDTO(allEquipmentCount, 0, 0, "0");
		}

		//检测的测点数量
		int detectionMonitorAmount = (int) (monitorWithSensorCodeList.stream().map(Monitor::getId).distinct().count());

		//监测设备数量
		int detectionEquipmentAmount = (int) (monitorWithSensorCodeList.stream().map(Monitor::getEquipmentId).distinct().count());

		//覆盖率
		BigDecimal coverage = new BigDecimal(detectionEquipmentAmount)
			.divide(new BigDecimal(allEquipmentCount), 2, RoundingMode.HALF_UP)
			.multiply(new BigDecimal(100));

		return new DetectionRangeDTO(allEquipmentCount, detectionEquipmentAmount, detectionMonitorAmount, coverage.stripTrailingZeros().toEngineeringString());
	}

	@Override
	public MonitorThresholdDTO queryBySensorCode(String sensorCode) {
		return baseMapper.queryBySensorCode(sensorCode);
	}

	@Override
	public Integer getMaxSort() {
		Integer maxSort = baseMapper.getMaxSort();
		return maxSort == null ? 0 : maxSort;
	}

	@Override
	public IPage<MonitorDTO> modelMonitorPage(IPage<MonitorDTO> page, MonitorVO vo) {
		MechanismModel mechanismModel = mechanismModelMapper.selectById(vo.getModelId());
		if (mechanismModel == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		vo.setEquipmentType(mechanismModel.getApplyEquipment());
		vo.setApplyPower(mechanismModel.getApplyPower());
		List<MonitorDTO> list = baseMapper.modelMonitorPage(page, vo);
		if (Func.isNotEmpty(list)) {
			list.forEach(dto -> {
				dto.setIsModelApply(Boolean.FALSE).setIsModelEnabled(Boolean.FALSE);
				dto.setPathName(dto.getPathName().split(StringPool.COMMA + dto.getName())[0].replace(StringPool.COMMA, StringPool.SLASH));
				//归属设备类型
				dto.setEquipmentTypeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, dto.getEquipmentType()));
				MonitorModel monitorModel = monitorModelService.getOne(Wrappers.<MonitorModel>query().lambda()
					.eq(MonitorModel::getMonitorId, dto.getId()).eq(MonitorModel::getModelId, vo.getModelId()));
				if (monitorModel != null) {
					dto.setIsModelApply(Boolean.TRUE);
				}
			});
		}
		return page.setRecords(list);
	}

	@Override
	public IPage<WaveDTO> thresholdWavePage(IPage<WaveDTO> page, WaveVO vo) {
		StandardDict standardDict = standardDictService.getById(vo.getThresholdId());
		if (standardDict == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		vo.setEquipmentType(standardDict.getApplyEquipment()).setSampleDataType(standardDict.getSampleDataType());
		List<WaveDTO> list = waveMapper.queryWave(page, vo);
		if (Func.isNotEmpty(list)) {
			list.forEach(dto -> {
				dto.setPathName(dto.getPathName().split(StringPool.COMMA + dto.getMonitorName())[0]
					.replace(StringPool.COMMA, StringPool.SLASH));
				//归属设备类型
				dto.setEquipmentTypeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, dto.getEquipmentType()));
			});
		}
		return page.setRecords(list);
	}

	@Override
	public int createSensorDataTable(Long monitorId) {
		return baseMapper.createSensorDataTable(MonitorTableNameHandler.SENSOR_DATA_TABLE_NAME + StringPool.UNDERSCORE + monitorId);
	}

	@Override
	public List<WaveDTO> waveList(Long monitorId) {
		List<WaveDTO> waveList = baseMapper.waveList(monitorId);

		// 排序：具体逻辑详见com.snszyk.zbusiness.basic.dto.WaveDTO.compareTo()
		Collections.sort(waveList);

		// 获取测点路径信息
		BasicTree basicTree = basicTreeService.getOne(Wrappers.<BasicTree>query().lambda()
			.eq(BasicTree::getId, monitorId)
			.select(BasicTree::getPath, BasicTree::getPathName));
		if (Func.isNotEmpty(waveList)) {
			waveList.forEach(vo -> {
				// 设置path、pathName
				vo.setPath(basicTree.getPath())
					.setPathName(basicTree.getPathName());

				// 设置采样时间
				if (vo.getSamplingPoints() != null && vo.getSamplingFreq() != null) {
					vo.setSamplingTime(BigDecimal.valueOf(vo.getSamplingPoints())
						.divide(vo.getSamplingFreq(), 2, RoundingMode.HALF_UP)
						.doubleValue());
				}

				// 设置nodeLevel和nodeName
				vo.setNodeLevel(3).setNodeName(vo.getWaveName());
				vo.setNodeCategoryName("波形");

				// 单位
				vo.setDataUnit(SampledDataUnitEnum.getByCode(vo.getSampleDataType()).getValue());
				vo.setDataUnitName(DictBizCache.getValue(DictBizEnum.SAMPLED_DATA_UNIT, vo.getDataUnit()));
			});
		}

		return waveList;
	}

	@Override
	public MonitorDTO getByIdIncludeEquipment(Long monitorId) {
		Monitor monitor = this.getById(monitorId);
		if (ObjectUtil.isEmpty(monitor)) {
			return null;
		}
		MonitorDTO dto = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorDTO.class));
		// 查詢設備信息
		Equipment equipment = equipmentService.getById(monitor.getEquipmentId());
		if (ObjectUtil.isEmpty(equipment)) {
			return null;
		}
		EquipmentDTO equipmentInfo = Objects.requireNonNull(BeanUtil.copy(equipment, EquipmentDTO.class));
		dto.setEquipmentInfo(equipmentInfo);
		return dto;
	}

}
