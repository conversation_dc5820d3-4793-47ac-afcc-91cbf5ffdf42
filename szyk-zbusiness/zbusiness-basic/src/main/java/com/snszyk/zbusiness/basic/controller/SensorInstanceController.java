/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.excel.util.ExcelUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.FileUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.excel.SensorInstanceImportListener;
import com.snszyk.zbusiness.basic.excel.SensorInstanceImporter;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.logic.SensorInstanceLogicService;
import com.snszyk.zbusiness.basic.vo.EqumentSensorListVO;
import com.snszyk.zbusiness.basic.vo.ImportSensorInstanceVO;
import com.snszyk.zbusiness.basic.vo.SensorInstanceParamVO;
import com.snszyk.zbusiness.basic.vo.SensorInstanceVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


/**
 * 传感器实例表 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sensorInstance")
@Api(value = "传感器实例表", tags = "传感器实例接口")
public class SensorInstanceController extends SzykController {

	private final ISensorInstanceService sensorInstanceService;
	private final SensorInstanceLogicService logicService;
	private final SzykRedis szykRedis;

	/**
	 * 详情
	 */
	@GetMapping("{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<SensorInstanceDTO> detail(@PathVariable Long id) {
		return R.data(logicService.detail(id));
	}

	/**
	 * 分页 传感器实例表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "nameOrModelOrCode", value = "名称或型号或编码", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "supplier", value = "生产厂家", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "category", value = "传感器类型", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "isBind", value = "是否已分配（0：未分配；1：已分配）", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "isBindStation", value = "是否已绑定采集站（0：未绑定；1：已绑定）", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "isWireless", value = "有线无线", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "pathId", value = "安装路径id", paramType = "query", dataType = "string")
	})
	@ApiOperation(value = "分页", notes = "传入sensorInstanceVO")
	public R<IPage<SensorInstanceDTO>> page(@ApiIgnore SensorInstanceVO vo, Query query) {
		IPage<SensorInstanceDTO> pages = logicService.page(Condition.getPage(query), vo);
		return R.data(pages);
	}

	/**
	 * 新增（支持批量新增） 传感器实例
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增", notes = "传入vo")
	public R<Boolean> save(@Valid @RequestBody SensorInstanceVO vo) {
		return R.status(logicService.save(vo));
	}

	/**
	 * 修改 采集站表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "修改", notes = "传入vo")
	public R<Boolean> update(@Valid @RequestBody SensorInstanceVO vo) {
		return R.status(logicService.update(vo));
	}

	/**
	 * 带校验的删除 传感器实例表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "带校验的删除", notes = "传入ids")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(logicService.removeSensorInstance(Func.toLongList(ids)));
	}

	/**
	 * 生成传感器编码 传感器实例表
	 */
	@GetMapping("/generateSensorCode")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "生成传感器编码", notes = "传入monitorId")
	public R<List<String>> generateSensorCode(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.data(logicService.generateSensorCode(Func.toLongList(ids)));
	}

	/**
	 * 根据编码获取传感器实例
	 */
	@GetMapping("/detailByCode")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "根据编码获取传感器实例", notes = "传入code")
	public R<SensorInstanceDTO> detailByCode(@RequestParam String code) {
		return R.data(logicService.detailByCode(code));
	}

	/**
	 * 获取传感器实例参数
	 */
	@GetMapping("/getSensorInstanceParams")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "获取传感器实例参数", notes = "传入id")
	public R<List<SensorInstanceParamVO>> getSensorInstanceParams(@ApiParam(value = "传感器实例id", required = true) @RequestParam Long id) {
		return R.data(logicService.getSensorInstanceParams(id));
	}

	/**
	 * 门户端-传感器数据概览
	 */
	@GetMapping("/onlineStat")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "门户端-传感器数据概览")
	public R<SensorTypeOnlineStatDTO> onlineStat() {
		return R.data(logicService.onlineStat());
	}

	/**
	 * 门户端-传感器工作状态概览 传感器实例表
	 */
	@GetMapping("/portalPage")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "门户端-传感器工作状态概览", notes = "传入sensorInstance")
	public R<IPage<SensorInstanceDTO>> portalPage(@ApiIgnore SensorInstanceVO sensorInstance, Query query) {
		IPage<SensorInstanceDTO> pages = sensorInstanceService.portalPage(Condition.getPage(query), sensorInstance);
		return R.data(pages);
	}

	/**
	 * 导出列表Excel 传感器实例表
	 */
	@GetMapping("/exportExcel")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "门户端-传感器工作状态概览一键导出", notes = "传入sensorInstance")
	public void exportExcel(@ApiIgnore SensorInstanceVO sensorInstance, HttpServletResponse response) {
		sensorInstanceService.exportExcel(sensorInstance, response);
		//return R.success("操作成功");
	}

	/**
	 * 导出传感器实例导入模板Excel
	 */
	@GetMapping("/exportInstanceBindTemplate")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "导出传感器实例导入模板Excel")
	public void exportInstanceBindTemplate(HttpServletResponse response) {
		sensorInstanceService.exportInstanceBindTemplate(response);
	}



	@GetMapping("/equmentSensorList")
	@ApiOperation(value = "根据地点查询设备所有传感器信息")
	public R<List<EqumentSensorListDTO>> equmentSensorList(EqumentSensorListVO vo){
		return R.data(this.sensorInstanceService.equmentSensorList(vo));
	}

	/**
	 * 导入传感器实例绑定关系
	 */
	@PostMapping("/importInstanceBind")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "导入传感器实例绑定关系", notes = "传入excel")
	public R<ImportSensorInstanceVO> importInstanceBind(MultipartFile file, Integer isCovered) {
		// 1、校验文件格式
		String fileName = file.getOriginalFilename();
		String fileExtension = FileUtil.getFileExtension(fileName);
		if (!Arrays.asList(EolmConstant.ImportFile.FILE_FORMAT_EXCEL).contains(StringPool.DOT + fileExtension)) {
			throw new ServiceException("仅支持导入以下格式的文件：" + String.join(",", Arrays.asList(EolmConstant.ImportFile.FILE_FORMAT_EXCEL)));
		}
		ImportSensorInstanceVO resultVO = new ImportSensorInstanceVO();
		R<ImportSensorInstanceVO> result = new R<>();
		try {
			// 监听-校验表头数据
			EasyExcel.read(file.getInputStream(), SensorInstanceBindExcelDTO.class, new SensorInstanceImportListener())
				.ignoreEmptyRow(false)
				.sheet()
				.doRead();
		} catch (Exception e) {
			e.printStackTrace();
			resultVO.setSuccessNumber(0)
				.setFirstFailNumber(1)
				.setFailureMonitorPath("")
				.setFailureMessage("文件数据和模板格式不匹配！");
			result.setCode(ResultCode.SUCCESS.getCode());
			result.setData(resultVO);
			result.setSuccess(Boolean.TRUE);
			return result;
		}

		// 2、导入
		SensorInstanceImporter instanceImporter = new SensorInstanceImporter(sensorInstanceService, isCovered == 1);
		ExcelUtil.save(file, instanceImporter, SensorInstanceBindExcelDTO.class);

		// 3、获取导入结果
		resultVO.setSuccessNumber(szykRedis.get(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_SUCCESS_NUMBER))
			.setFirstFailNumber(szykRedis.get(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_FIRST_FAIL_NUMBER))
			.setFailureMonitorPath(szykRedis.get(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_FAILURE_MONITOR_PATH))
			.setFailureMessage(szykRedis.get(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_FAILURE_MESSAGE));
		return R.data(resultVO);
	}


	@GetMapping("/equipment-sensor")
	@ApiOperation(value = "根据设备ID查询传感器")
	public R<List<EquipmentSensorDTO>> equipmentSensor(@RequestParam("id") Long id){
		EquipmentSensorDTO.szykRedis = this.szykRedis;
		return R.data(this.sensorInstanceService.equipmentSensor(id));
	}

}
