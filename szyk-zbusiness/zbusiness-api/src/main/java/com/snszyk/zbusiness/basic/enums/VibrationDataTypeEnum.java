/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 振动采样数据类型 枚举类
 *
 * <AUTHOR>
 * @date 2023/07/07 15:56
 **/
@Getter
@AllArgsConstructor
public enum VibrationDataTypeEnum {

	/**
	 * 加速度
	 */
	ACCELERATION("ACCELERATION", "加速度"),

	/**
	 * 速度
	 */
	VELOCITY("VELOCITY", "速度"),

	/**
	 * 位移
	 */
	DISPLACEMENT("DISPLACEMENT", "位移"),

	/**
	 * 应力波
	 */
	STRESS("STRESS", "应力波"),

	/**
	 * 电流
	 */
	ELECTRIC("ELECTRIC", "电流"),

	/**
	 * 未知
	 */
	NOT_SUPPORTED("NOT_SUPPORTED", "未知"),
	;

	final String code;
	final String name;

	public static VibrationDataTypeEnum getByCode(String code){
		for (VibrationDataTypeEnum value : VibrationDataTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
