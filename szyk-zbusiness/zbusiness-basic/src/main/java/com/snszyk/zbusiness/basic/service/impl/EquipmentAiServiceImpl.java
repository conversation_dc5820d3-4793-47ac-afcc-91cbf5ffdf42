/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.dto.EquipmentAiDTO;
import com.snszyk.zbusiness.basic.entity.EquipmentAi;
import com.snszyk.zbusiness.basic.mapper.EquipmentAiMapper;
import com.snszyk.zbusiness.basic.service.IEquipmentAiService;
import com.snszyk.zbusiness.basic.vo.EquipmentAiVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备AI模型表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-08
 */
@Service
@AllArgsConstructor
public class EquipmentAiServiceImpl extends ServiceImpl<EquipmentAiMapper, EquipmentAi> implements IEquipmentAiService {

	@Override
	public boolean removeByEquipment(Long equipmentId) {
		return baseMapper.removeByEquipment(equipmentId) >= 0;
	}

	@Override
	public boolean removeByMonitor(Long monitorId) {
		return baseMapper.removeByMonitor(monitorId) >= 0;
	}

	@Override
	public boolean removeEquipmentAi(Long equipmentId, Long aiModelId) {
		return baseMapper.removeEquipmentAi(equipmentId, aiModelId) >= 0;
	}

	@Override
	public boolean removeBySensorCode(String sensorCode) {
		return baseMapper.removeBySensorCode(sensorCode) >= 0;
	}

	@Override
	public IPage<EquipmentAiDTO> equipmentAiPage(IPage<EquipmentAiDTO> page, EquipmentAiVO equipmentAi) {
		List<EquipmentAiDTO> list = baseMapper.equipmentAiPage(page, equipmentAi);
		if(Func.isNotEmpty(list)){
			list.forEach(dto -> {
				dto.setMonitorId(dto.getId());
				dto.setPathName(dto.getPathName().substring(0, dto.getPathName().lastIndexOf(StringPool.COMMA))
					.replace(StringPool.COMMA, StringPool.SLASH));
				if (dto.getHasChildren() > 0){
					List<EquipmentAiDTO> children = baseMapper.getChildrenList(dto.getId(), dto.getAiModelId());
					children.forEach(child -> child.setAiModelId(child.getId()));
					dto.setChildren(children);
				}
			});
		}
		return page.setRecords(list);
	}

}
