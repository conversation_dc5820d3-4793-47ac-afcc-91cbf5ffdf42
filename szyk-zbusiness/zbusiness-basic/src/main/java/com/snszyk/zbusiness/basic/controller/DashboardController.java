package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.service.DashboardService;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.vo.DeviceFilterVO;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningMonitorVO;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 基础模块门户端大屏接口
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/dashboard")
@Api(value = "基础模块门户端大屏接口", tags = "基础模块门户端大屏接口")
public class DashboardController extends SzykController {

	private final DashboardService dashboardService;
	private final IEquipmentService equipmentService;

	/**
	 * 集团驾驶舱-检测范围 （检测设备、测点）
	 */
	@ApiOperation("集团驾驶舱-检测范围")
	@ApiOperationSupport(order = 1)
	@GetMapping("/group/detection-range")
	public R<DetectionRangeDTO> groupDetectionRange(SzykUser szykUser) {
		return dashboardService.groupDetectionRange(szykUser.getTenantId(), szykUser.getDeptId());
	}

	/**
	 * 集团驾驶舱-厂区位置信息
	 */
	@ApiOperation("集团驾驶舱-厂区位置信息")
	@ApiOperationSupport(order = 2)
	@GetMapping("/group/plant-location")
	public R<List<PlantLocationDTO>> groupPlantLocation(SzykUser szykUser) {
		return dashboardService.groupPlantLocation(szykUser.getTenantId(), szykUser.getDeptId());
	}

	/**
	 * 厂区驾驶舱-检测范围
	 * @param id 地点id
	 * @return
	 */
	@ApiOperation("厂区驾驶舱-检测范围")
	@ApiOperationSupport(order = 3)
	@GetMapping("/plant/detection-range/{id}")
	public R<DetectionRangeDTO> plantDetectionRange(@PathVariable(name = "id") String id) {
		return dashboardService.plantDetectionRange(id);
	}

	/**
	 * 厂区驾驶舱-厂区设备列表
	 */
	@ApiOperation("厂区驾驶舱-厂区设备列表")
	@ApiOperationSupport(order = 4)
	@GetMapping("/plant/device-page")
	public R<IPage<DeviceDetailDTO>> plantDevicePage(DeviceFilterVO vo, Query query) {
		return dashboardService.plantDevicePage(vo, query);
	}

	/**
	 * 厂区驾驶舱-厂区设备列表统计（alarm：待处理报警、fault：待处理故障）
	 */
	@ApiOperation("厂区驾驶舱-厂区设备列表统计（alarm：待处理报警、fault：待处理故障）")
	@ApiOperationSupport(order = 5)
	@GetMapping("/plant/device-sum")
	public R<Map<String, Integer>> plantDeviceSum(@RequestParam(name = "deviceId", required = false) String deviceId) {
		return dashboardService.plantDeviceSum(deviceId);
	}

	/**
	 * 厂区驾驶舱-厂区模型图
	 */
	@ApiOperation("厂区驾驶舱-厂区模型图")
	@ApiOperationSupport(order = 6)
	@GetMapping("/plant/image-model/{id}")
	public R<ProductionLineLocationDTO> plantImageModel(@PathVariable(name = "id") String id) {
		return dashboardService.plantImageModel(id);
	}

	/**
	 * 厂区驾驶舱-厂区列表
	 */
	@ApiOperation("厂区驾驶舱-厂区列表")
	@ApiOperationSupport(order = 7)
	@GetMapping("/plant/list")
	public R<List<Map<String, Object>>> plantList(SzykUser szykUser) {
		return dashboardService.plantList(szykUser);
	}

	/**
	 * 设备运行状态统计
	 */
	@GetMapping("/equipment-running-state")
	@ApiOperation("设备运行状态统计")
	@ApiOperationSupport(order = 8)
	public R<EquipmentRunningStateDTO> equipmentRunningState() {
		return dashboardService.equipmentRunningState(AuthUtil.getTenantId());
	}


	/**
	 * 异常设备统计
	 * @return
	 */
	@GetMapping("/abnormal-equipment-total")
	public R<AbnormalEquipmentTotal> abnormalEquipmentTotal(){
		return R.data(this.equipmentService.abnormalEquipmentTotal());
	}

	/**
	 * 重点关注设备
	 * @return
	 */
	@GetMapping("/important-equipment")
	R<List<ImportantEquipment>> importantEquipment(@RequestParam(name = "id", required = false) Long id){
		return R.data(this.equipmentService.importantEquipment(id));
	}

	/**
	 * 设备故障类型统计
	 * @return
	 */
	@GetMapping("/equipment-type-total")
	R<List<EquipmentTypeTotal>> equipmentTypeTotal(@RequestParam(name = "id", required = false) Long equipmentId){
		return R.data(this.equipmentService.equipmentTypeTotal(equipmentId));
	}


	/**
	 * 设备频发次数统计
	 * @return
	 */
	@GetMapping("/equipment-frequency")
	R<List<EquipmentFrequency>> equipmentFrequency(){
		List<EquipmentFrequency> list = equipmentService.equipmentFrequency();
		list.forEach(action->action.convert());
		return R.data(list);
	}

	/**
	 * 异常状态分布统计
	 * @return
	 */
	@GetMapping("/abnormal-scatter")
	R<int[]> abnormalScatter(@RequestParam(name = "id", required = false) Long id){
		List<AbnormalScatter> list = this.equipmentService.abnormalScatter(id);
		int[] data = new int[]{0,0,0,0};
		list.forEach(item->data[item.getAbnormalLevel()-1]=item.getCt());
		return R.data(data);
	}

	/**
	 * 设备运行监控
	 * @param vo
	 * @param query
	 * @return
	 */
	@GetMapping("/equipment-running-monitor")
	public  R<IPage<EquipmentRunningMonitorDTO>> equipmentRunningMonitor(EquipmentRunningMonitorVO vo, Query query){
		IPage<EquipmentRunningMonitorDTO> page = Condition.getPage(query);
		List<EquipmentRunningMonitorDTO> list = this.equipmentService.equipmentRunningMonitor(page,vo);
		page.setRecords(list);
		return R.data(page);
	}

	/**
	 * 设备异常信息列表
	 * @param id
	 * @return
	 */

	@GetMapping("/equipment-monitor-abnormal")
	public  R<List<EquipmentMonitorAbnormalDTO>> equipmentMonitorAbnormal(@RequestParam(name = "id", required = false) Long id){
		return R.data(this.equipmentService.equipmentMonitorAbnormal(id));
	}
	@GetMapping("/equipment-ledger")
	public  R<IPage<EquipmentDTO>> equipmentLedger(EquipmentVO vo, Query query){
		IPage<EquipmentDTO> page = Condition.getPage(query);
//		EquipmentRunningMonitorDTO.key = DICT;
		List<EquipmentDTO> list = this.equipmentService.equipmentLedger(page,vo);
		page.setRecords(list);
		return R.data(page);
	}

}
