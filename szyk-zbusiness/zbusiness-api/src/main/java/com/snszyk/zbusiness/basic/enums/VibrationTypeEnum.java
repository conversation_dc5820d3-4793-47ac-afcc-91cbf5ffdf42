/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 传感器数据振动类型枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VibrationTypeEnum {

	/**
	 * 非振动
	 */
	NON_VIBRATION(0, "非振动"),

	/**
	 * 振动
	 */
	IS_VIBRATION(1, "振动");

	final Integer code;
	final String name;

	public static VibrationTypeEnum getByCode(Integer code){
		for (VibrationTypeEnum value : VibrationTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
