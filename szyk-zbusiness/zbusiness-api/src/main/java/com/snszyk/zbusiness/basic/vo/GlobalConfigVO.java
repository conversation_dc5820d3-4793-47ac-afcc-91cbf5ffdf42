/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.GlobalConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.poi.hsmf.MAPIMessage;

import javax.validation.constraints.NotBlank;

/**
 * 全局配置表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-06-12
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "GlobalConfigVO对象", description = "全局配置表")
public class GlobalConfigVO extends GlobalConfig {
	private static final long serialVersionUID = 1L;

	/**
	 * 报警周期配置
	 */
	@ApiModelProperty(value = "报警周期配置")
	private AlarmPeriodConfigVO alarmPeriodConfig;

	/**
	 * 润滑策略配置
	 */
	@ApiModelProperty(value = "润滑策略配置")
	private LubricateStrategyConfigVO lubricateStrategyConfig;

	/**
	 * 设备异常策略配置
	 */
	@ApiModelProperty(value = "设备异常策略配置")
	private AbnormalStrategyConfigVO abnormalStrategyConfig;



	public GlobalConfigVO() {

	}

	public GlobalConfigVO(String tenantId) {
		this.setTenantId(tenantId);
	}

}
