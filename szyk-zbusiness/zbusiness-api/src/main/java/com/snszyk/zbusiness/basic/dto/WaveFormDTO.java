package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 数据波形
 *
 * <AUTHOR>
 * @date 2023/02/16 15:30
 **/
@Data
@Accessors(chain = true)
public class WaveFormDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 特征值数据列表
	 */
	@ApiModelProperty(value = "特征值数据列表")
	private List<SensorDataDTO> characterDataList;

	/**
	 * 特征值数据列表
	 */
	@ApiModelProperty(value = "特征值数据列表")
	private List<SensorDataDTO> dataList;

	/**
	 * 报警数据列表
	 */
	@ApiModelProperty(value = "报警数据列表")
	private List<SensorDataDTO> alarmDataList;

	/**
	 * 时域波形数据列表
	 */
	@ApiModelProperty(value = "时域波形数据列表")
	private List<BigDecimal> timeDomainWaveForm;

	/**
	 * 频域振幅数据列表
	 */
	@ApiModelProperty(value = "频域振幅数据列表")
	private List<BigDecimal> freqDomainWaveForm;

	/**
	 * 频域相位数据列表
	 */
	@ApiModelProperty(value = "频域相位数据列表")
	private List<BigDecimal> freqPhaseWaveForm;

	/**
	 * 包络图谱数据列表
	 */
	@ApiModelProperty(value = "包络图数据列表")
	private List<BigDecimal> envelopeWaveForm;

	/**
	 * 倒谱图数据列表
	 */
	@ApiModelProperty(value = "倒谱图数据列表")
	private List<BigDecimal> cepstrumWaveForm;

	/**
	 * 速度图数据列表
	 */
	@ApiModelProperty(value = "速度图数据列表")
	private List<BigDecimal> velocityWaveForm;

	/**
	 * 波特图X轴频率数据
	 */
	private List<BigDecimal> bodeFreqX;

}
