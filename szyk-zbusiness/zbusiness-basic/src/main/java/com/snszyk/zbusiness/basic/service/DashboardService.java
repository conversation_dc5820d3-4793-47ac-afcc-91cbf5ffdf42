package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.constant.CommonConstant;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.mp.support.SzykPage;
import com.snszyk.core.mp.utils.PageUtil;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tenant.SzykTenantProperties;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.*;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Dept;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.enums.SampledDataUnitEnum;
import com.snszyk.zbusiness.basic.vo.DeviceFilterVO;
import com.snszyk.zbusiness.basic.vo.EquipmentSearchVO;
import com.snszyk.zbusiness.ops.service.IAlarmService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 基础模块门户端大屏服务类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class DashboardService {

	/**
	 * 当前数据采样时间
	 */
	public static final String DATA_ORIGIN_TIME_KEY_PREFIX = "equipment:data:originTime:";
	private final IDeviceService deviceService;
	private final IEquipmentService equipmentService;
	private final IMonitorService monitorService;
	private final IBasicTreeService basicTreeService;
	private final IWaveService waveService;
	private final IAttachService attachService;
	private final SzykTenantProperties tenantProperties;
	private final SzykRedis szykRedis;
	private final IAlarmService alarmService;

	/**
	 * 获取集团检测范围
	 *
	 * @param tenantId 租户id
	 * @param deptId   部门id
	 * @return
	 */
	public R<DetectionRangeDTO> groupDetectionRange(String tenantId, String deptId) {
		String code = getGroupCockpitDeptId(deptId);
		DeviceDTO deviceDTO = deviceService.getByCode(code);
		if (deviceDTO != null) {
			return R.data(monitorService.detectionRange(deviceDTO.getId()));
		}

		return R.data(new DetectionRangeDTO(0, 0, 0, "0"));
	}

	/**
	 * 获取集团驾驶舱的deptId
	 *
	 * @param deptId deptId
	 * @return
	 */
	private String getGroupCockpitDeptId(String deptId) {
		Dept dept = SysCache.getDept(Func.toLong(deptId));
		if (Func.isEmpty(dept)) {
			throw new ServiceException(ResultCode.UN_AUTHORIZED);
		}
		String ancestors = dept.getAncestors();
		String[] ids = Func.split(ancestors, StringPool.COMMA);
		int length = ids.length;
		if (length > 1) {
			return ids[1];
		}
		return deptId;
	}

	/**
	 * 获取集团驾驶舱的位置信息
	 *
	 * @param tenantId 租户id
	 * @param deptId   部门id
	 * @return
	 */
	public R<List<PlantLocationDTO>> groupPlantLocation(String tenantId, String deptId) {
		List<PlantLocationDTO> list = new ArrayList<>();
		DeviceDTO deviceDTO = deviceService.getByCode(getGroupCockpitDeptId(deptId));
		if (deviceDTO != null) {
			List<DeviceDTO> deviceChildList = deviceService.getByParentId(deviceDTO.getId());
			if (CollectionUtil.isNotEmpty(deviceChildList)) {
				for (Device device : deviceChildList) {
					list.add(new PlantLocationDTO(device.getId(), device.getName(),
						device.getLongitudeLatitude(), plantDeviceStatistics(device.getId(), tenantId)));
				}
			}
		}
		return R.data(list);
	}

	/**
	 * 获取集团驾驶舱的设备统计信息
	 *
	 * @param id       设备id
	 * @param tenantId 租户id
	 * @return
	 */
	private PlantDeviceStatisticsDTO plantDeviceStatistics(Long id, String tenantId) {
		//设备总数
		Integer equipmentAmount = equipmentService.count(Wrappers.<Equipment>lambdaQuery()
			.eq(Func.isNotEmpty(tenantId), Equipment::getTenantId, tenantId)
			.like(Func.isNotEmpty(id), Equipment::getPath, id));

		//测点总数
		Integer equipmentMonitorAmount = monitorService.count(Wrappers.<Monitor>lambdaQuery()
			.eq(Monitor::getTenantId, tenantId)
			.like(Monitor::getPath, id));

		//检修设备总数
		Integer repairEquipmentAmount = 0;
//		R<Integer> repairEquipmentAmountR = faultClient.repairEquipmentAmount(Func.toStr(id), Collections.singletonList(1));
//		if (repairEquipmentAmountR.isSuccess() && Func.isNotEmpty(repairEquipmentAmountR.getData())) {
//			repairEquipmentAmount = repairEquipmentAmountR.getData();
//		}

		//报警设备统计
		Integer alarmDevice = alarmService.count(Func.toStr(id), Arrays.asList("0", "1"));
		//待处理报警设备统计
		Integer pendingAlarmDevice = alarmService.count(Func.toStr(id), Collections.singletonList("0"));
		return new PlantDeviceStatisticsDTO(equipmentAmount, equipmentMonitorAmount, repairEquipmentAmount, alarmDevice, pendingAlarmDevice);
	}

	/**
	 * 获取厂区驾驶舱的检测范围
	 *
	 * @param id 设备id
	 * @return
	 */
	public R<DetectionRangeDTO> plantDetectionRange(String id) {
		return R.data(monitorService.detectionRange(Func.toLong(id)));
	}

	/**
	 * 获取厂区驾驶舱的设备列表
	 *
	 * @param vo    设备过滤条件
	 * @param query 分页条件
	 * @return
	 */
	public R<IPage<DeviceDetailDTO>> plantDevicePage(DeviceFilterVO vo, Query query) {
		Long deviceId = vo.getDeviceId();
		if (Func.isEmpty(deviceId)) {
			if (tenantProperties.getEnhance()) {
				DeviceDTO deviceDTO = deviceService.getByCode(getGroupCockpitDeptId(AuthUtil.getDeptId()));
				if (Func.isEmpty(deviceDTO)) {
					IPage<DeviceDetailDTO> emptyPage = new Page<>();
					emptyPage.setCurrent(1);
					emptyPage.setSize(10);
					emptyPage.setTotal(0);
					emptyPage.setRecords(Collections.emptyList());
					return R.data(emptyPage);
				}
				deviceId = deviceDTO.getId();
			} else {
				List<DeviceDTO> deviceDTOList = deviceService.getByLevel(EolmConstant.Device.TOP_LEVEL);
				if (CollectionUtil.isNotEmpty(deviceDTOList)) {
					deviceId = deviceDTOList.get(0).getId();
				} else {
					throw new ServiceException("获取机构失败！");
				}

			}
		}
		SzykPage<Equipment> equipmentPage = equipmentPage(new EquipmentSearchVO(deviceId, vo.getName(),
			vo.getAlarmLevel(), vo.getCategory(), vo.getProduceTech(), query));
		if (Func.isNotEmpty(equipmentPage)) {
			List<DeviceDetailDTO> detailList = new ArrayList<>();
			if (Func.isNotEmpty(equipmentPage.getRecords())) {
				detailList = equipmentPage.getRecords().stream()
					.map(this::getDeviceDetail)
					.collect(Collectors.toList());
			}

			IPage<DeviceDetailDTO> iPage = new Page<>();
			iPage.setCurrent(equipmentPage.getCurrent());
			iPage.setSize(equipmentPage.getSize());
			iPage.setTotal(equipmentPage.getTotal());
			iPage.setRecords(detailList);
			return R.data(iPage);
		}
		return R.data(PageUtil.toPage(Condition.getPage(query), new ArrayList<>()));
	}

	/**
	 * 获取厂区驾驶舱的设备列表
	 *
	 * @param searchVO 设备过滤条件
	 */
	private SzykPage<Equipment> equipmentPage(EquipmentSearchVO searchVO) {
		LambdaQueryWrapper<Equipment> queryWrapper = Wrappers.lambdaQuery();
		queryWrapper.like(Func.isNotEmpty(searchVO.getDeviceId()), Equipment::getPath, searchVO.getDeviceId());
		queryWrapper.like(Func.isNotEmpty(searchVO.getName()), Equipment::getName, searchVO.getName());
		queryWrapper.in(Func.isNotEmpty(searchVO.getCategory()), Equipment::getCategory, Func.toIntList(searchVO.getCategory()));
		queryWrapper.eq(Func.isNotEmpty(searchVO.getProduceTech()), Equipment::getProduceTech, searchVO.getProduceTech());
		IPage<Equipment> iPage = equipmentService.page(Condition.getPage(searchVO.getQuery()), queryWrapper);
		return SzykPage.of(iPage);
	}

	/**
	 * 获取设备详情
	 *
	 * @param equipment 设备
	 * @return
	 */
	@NotNull
	private DeviceDetailDTO getDeviceDetail(Equipment equipment) {
		DeviceDetailDTO deviceDetail = BeanUtil.copy(equipment, DeviceDetailDTO.class);
		assert deviceDetail != null;
		//编码：只保留设备的编码
		if (StringUtil.isNotBlank(deviceDetail.getCode()) && deviceDetail.getCode().contains(StringPool.COLON)) {
			deviceDetail.setCode(deviceDetail.getCode().substring(deviceDetail.getCode().lastIndexOf(StringPool.COLON) + 1));
		}
		//计算运行率
		if (equipment.getOriginRuntime() != null) {
			Long immediateOriginTime = Func.toLong(szykRedis.get(DATA_ORIGIN_TIME_KEY_PREFIX + equipment.getId()));
			Long lastTimeInterval = DateUtil.now().getTime() - immediateOriginTime;
			if (equipment.getIsRunning() == 1) {
				equipment.setRunningTime(equipment.getRunningTime() + lastTimeInterval);
			} else {
				equipment.setShutdownTime(equipment.getShutdownTime() + lastTimeInterval);
			}
		}
		Long totalTime = equipment.getRunningTime() + equipment.getShutdownTime();
		if (totalTime > 0) {
			deviceDetail.setRunningPercentage(BigDecimal.valueOf(equipment.getRunningTime())
				.multiply(BigDecimal.valueOf(100)).divide(BigDecimal.valueOf(totalTime), 2, RoundingMode.HALF_UP));
		} else {
			deviceDetail.setRunningPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
		}
		deviceDetail.setRunningTime(BigDecimal.valueOf(equipment.getRunningTime())
			.divide(BigDecimal.valueOf(1000 * 60 * 60), 2, RoundingMode.HALF_UP));
		Optional.ofNullable(attachService.getById(equipment.getImage()))
			.ifPresent(attach -> deviceDetail.setAttach(attach));
		//查询所有测点
		List<MonitorDTO> monitorDTOList = equipmentMonitor(equipment.getId());
		if (Func.isNotEmpty(monitorDTOList)) {
			List<DeviceMonitorDTO> deviceMonitorList = new ArrayList<>();
			//查询每个测点的特征值（振动数据有效值、设备温度）
			monitorDTOList.forEach(monitorDTO -> {
				DeviceMonitorDTO deviceMonitor = new DeviceMonitorDTO();
				deviceMonitor.setMonitorId(monitorDTO.getId());
				deviceMonitor.setMonitorName(monitorDTO.getName());
				// 从Redis获取测点的所有波形的最新数据（加速度、速度、位移、温度）
				List<Wave> waveList = waveService.list(Wrappers.<Wave>lambdaQuery()
					.eq(Wave::getMonitorId, monitorDTO.getId())
					.in(Wave::getSampleDataType, SampledDataTypeEnum.ACCELERATION.getCode(),
						SampledDataTypeEnum.VELOCITY.getCode(),
						SampledDataTypeEnum.DISPLACEMENT.getCode(),
						SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode()));
				if (Func.isNotEmpty(waveList)) {
					List<DeviceMonitorDTO.SensorData> sensorDataList = new ArrayList<>();
					waveList.forEach(wave -> {
						DeviceMonitorDTO.SensorData sensorData = new DeviceMonitorDTO.SensorData();
						// 根据Redis Key，sensorCode+":"+sensorInstanceParamId+":"+sampleDataType 取传感器最新数据
						if (Func.isNotEmpty((Object) szykRedis.get(wave.getSensorCode() + StringPool.COLON
							+ wave.getSensorInstanceParamId() + StringPool.COLON + wave.getSampleDataType()))) {
							sensorData.setValue(BigDecimal.valueOf(Func.toDouble(szykRedis.get(wave.getSensorCode() + StringPool.COLON
								+ wave.getSensorInstanceParamId() + StringPool.COLON + wave.getSampleDataType()))));
						}
						sensorData.setUnit(DictBizCache.getValue(DictBizEnum.SAMPLED_DATA_UNIT,
							SampledDataUnitEnum.getByCode(wave.getSampleDataType()).getValue()));
						if (Func.isNotEmpty(sensorData.getValue())) {
							sensorDataList.add(sensorData);
						}
					});
					deviceMonitor.setSensorDataList(sensorDataList);
				} else {
					log.info("获取测点（monitorId = {}）的WaveList为空！", monitorDTO.getId());
				}
				deviceMonitorList.add(deviceMonitor);
			});
			deviceDetail.setMonitorList(deviceMonitorList);
		} else {
			log.info("获取设备（id = {}）的测点为空！", equipment.getId());
		}
		return deviceDetail;
	}

	/**
	 * 获取设备的测点列表
	 *
	 * @param equipmentId 设备id
	 * @return
	 */
	private List<MonitorDTO> equipmentMonitor(Long equipmentId) {
		List<Monitor> list = monitorService.list(Wrappers.<Monitor>lambdaQuery()
			.eq(Monitor::getEquipmentId, equipmentId));
		return list.stream()
			.map(monitor -> BeanUtil.copy(monitor, MonitorDTO.class))
			.collect(Collectors.toList());
	}

	/**
	 * 获取厂区设备列表统计（alarm：待处理报警、fault：待处理故障）
	 *
	 * @param deviceId 地点id
	 */
	public R<Map<String, Integer>> plantDeviceSum(String deviceId) {
		Map<String, Integer> map = new HashMap<>(16);
		if (Func.isEmpty(deviceId)) {
			DeviceDTO deviceDTO = deviceService.getByCode(getGroupCockpitDeptId(AuthUtil.getDeptId()));
			if (deviceDTO == null) {
				return R.fail("获取地点数据失败");
			}
			deviceId = Func.toStr(deviceDTO.getId());
		}
		//待处理报警
		Integer alarmCount = alarmService.count(deviceId, Arrays.asList("0", "1"));
		//待处理故障
//		R<Integer> repairEquipmentAmount = faultClient.repairEquipmentAmount(deviceId, Collections.singletonList(0));
//		if (!repairEquipmentAmount.isSuccess()) {
//			return R.fail("获取故障数据失败");
//		}

		map.put("alarm", alarmCount);
		map.put("fault", 0);
		return R.data(map);
	}

	/**
	 * 获取地点模型图
	 *
	 * @param id 地点id
	 */
	public R<ProductionLineLocationDTO> plantImageModel(String id) {
		ProductionLineLocationDTO lineLocation = new ProductionLineLocationDTO();
		DeviceDTO deviceDTO = deviceById(Func.toLong(id));
		if (Func.isEmpty(deviceDTO) || Func.isEmpty(deviceDTO.getImage())) {
			return R.data(null);
		}

		Long image = deviceDTO.getImage();
		List<Attach> attachList = attachService.listByIds(Collections.singletonList(image));
		if (ObjectUtil.isNotEmpty(attachList)) {
			lineLocation.setAttach(attachList.get(0));
		}

		List<DeviceDTO> deviceChildList = deviceService.getByParentId(deviceDTO.getId());
		if (Func.isNotEmpty(deviceChildList)) {
			lineLocation.setDeviceList(deviceChildList);
		}
		return R.data(lineLocation);
	}

	/**
	 * 获取地点详情
	 */
	public DeviceDTO deviceById(Long id) {
		Device device = deviceService.getById(id);
		DeviceDTO dto = Objects.requireNonNull(BeanUtil.copy(device, DeviceDTO.class));
		BasicTree basicTree = basicTreeService.getById(device.getId());
		dto.setPath(basicTree.getPath()).setPathName(basicTree.getPathName());
		return dto;
	}

	public R<List<Map<String, Object>>> plantList(SzykUser szykUser) {
		List<Map<String, Object>> list = new ArrayList<>();
		DeviceDTO deviceDTO = deviceService.getByCode(szykUser.getDeptId());
		// 多租户
		if (Func.isNotEmpty(deviceDTO)) {
			if (deviceDTO.getParentId().equals(CommonConstant.TOP_PARENT_ID)) {
				List<DeviceDTO> deviceChildList = deviceService.getByParentId(deviceDTO.getId());
				getDeviceIdAndNameList(list, deviceChildList);
			} else if (deviceDTO.getLevel() == 1) {
				Map<String, Object> map = new HashMap<>(16);
				map.put("id", Func.toStr(deviceDTO.getId()));
				map.put("name", deviceDTO.getName());
				list.add(map);
			}
		} else {
			// 单租户
			List<DeviceDTO> deviceDTOList = deviceService.getByLevel(EolmConstant.Device.TOP_LEVEL);
			getDeviceIdAndNameList(list, deviceDTOList);
		}
		return R.data(list);
	}

	/**
	 * 获取设备id&name信息
	 *
	 * @param list       设备信息列表
	 * @param deviceList 原始设备数据列表
	 */
	private void getDeviceIdAndNameList(List<Map<String, Object>> list, List<DeviceDTO> deviceList) {
		if (Func.isNotEmpty(deviceList)) {
			deviceList.forEach(device -> {
				Map<String, Object> map = new HashMap<>(16);
				map.put("id", Func.toStr(device.getId()));
				map.put("name", device.getName());
				list.add(map);
			});
		}
	}

	/**
	 * 按地点统计设备运行状态
	 *
	 * @param tenantId 租户id
	 * @return
	 */
	public R<EquipmentRunningStateDTO> equipmentRunningState(String tenantId) {
		if ("000000".equals(tenantId)) {
			tenantId = null;
		}

		// 查找所有二级地点
		List<Device> deviceList = deviceService.listSecondLevelDevice(tenantId);
		if (CollectionUtil.isNotEmpty(deviceList)) {
			EquipmentRunningStateDTO result = new EquipmentRunningStateDTO()
				.setAddressList(new ArrayList<>())
				.setRunningStateList(new ArrayList<>())
				.setStopStateList(new ArrayList<>());

			// 设置地点的运行、停机设备数
			deviceList.forEach(device -> {
				//添加地点名称
				result.getAddressList().add(device.getName());

				//添加运行设备数
				result.getRunningStateList().add(equipmentService.count(Wrappers.<Equipment>lambdaQuery()
					.like(Equipment::getPath, device.getId().toString())
					.eq(Equipment::getIsRunning, 1)
					.eq(BaseEntity::getIsDeleted, 0)));

				//添加停机设备数
				result.getStopStateList().add(equipmentService.count(Wrappers.<Equipment>lambdaQuery()
					.like(Equipment::getPath, device.getId().toString())
					.eq(Equipment::getIsRunning, 0)
					.eq(BaseEntity::getIsDeleted, 0)));
			});

			return R.data(result);
		} else {
			return R.data(null);
		}
	}
}
