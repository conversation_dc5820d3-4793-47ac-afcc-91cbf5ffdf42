/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.builder.sms;

import com.qiniu.sms.SmsManager;
import com.qiniu.util.Auth;
import lombok.SneakyThrows;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.sms.SmsTemplate;
import com.snszyk.core.sms.props.SmsProperties;
import com.snszyk.core.sms.QiniuSmsTemplate;
import com.snszyk.resource.entity.Sms;

/**
 * 七牛云短信构建类
 *
 * <AUTHOR>
 */
public class QiniuSmsBuilder {

	@SneakyThrows
	public static SmsTemplate template(Sms sms, SzykRedis szykRedis) {
		SmsProperties smsProperties = new SmsProperties();
		smsProperties.setTemplateId(sms.getTemplateId());
		smsProperties.setAccessKey(sms.getAccessKey());
		smsProperties.setSecretKey(sms.getSecretKey());
		smsProperties.setSignName(sms.getSignName());
		Auth auth = Auth.create(smsProperties.getAccessKey(), smsProperties.getSecretKey());
		SmsManager smsManager = new SmsManager(auth);
		return new QiniuSmsTemplate(smsProperties, smsManager, szykRedis);
	}

}
