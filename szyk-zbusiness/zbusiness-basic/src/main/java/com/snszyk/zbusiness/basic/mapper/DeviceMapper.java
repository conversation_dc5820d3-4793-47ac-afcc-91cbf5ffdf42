/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.basic.dto.DeviceCoordinateSubDTO;
import com.snszyk.zbusiness.basic.dto.DeviceDTO;
import com.snszyk.zbusiness.basic.entity.Device;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * DeviceMapper 接口
 *
 * <AUTHOR>
 */
public interface DeviceMapper extends BaseMapper<Device> {

	/**
	 * 获取子级列表
	 *
	 * @param id
	 * @return
	 */
	List<DeviceDTO> getByParentId(Long id);

	/**
	 * 根据层级查询
	 *
	 * @param level
	 * @return
	 */
	List<DeviceDTO> getByLevel(Integer level);

	/**
	 * 查询门户区域点位视图列表
	 * @param id 区域id
	 * @return
	 */
	List<DeviceCoordinateSubDTO> deviceView(Long id);

	/**
	 * 获取当前最大排序
	 *
	 * @return
	 */
	Integer selectMaxSort();

    List<Device> listSecondLevelDevice(@Param("tenantId") String tenantId);
}
