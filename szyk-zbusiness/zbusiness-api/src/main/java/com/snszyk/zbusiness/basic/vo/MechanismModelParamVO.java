/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.MechanismModelParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 机理模型参数表视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MechanismModelParamVO对象", description = "机理模型参数表")
public class MechanismModelParamVO extends MechanismModelParam {
	private static final long serialVersionUID = 1L;

	/**
	 * 机理参数名称
	 */
	@ApiModelProperty(value = "机理参数名称")
	private String paramTypeName;

	/**
	 * 参数集合
	 */
	@ApiModelProperty(value = "参数集合")
	private List<ModelParamVO> paramList;

}
