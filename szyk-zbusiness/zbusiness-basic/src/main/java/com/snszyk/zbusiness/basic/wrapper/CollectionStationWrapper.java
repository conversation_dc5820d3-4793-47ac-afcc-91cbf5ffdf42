/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.vo.CollectionStationVO;

import java.util.Objects;
import java.util.Optional;

/**
 * 机理模型库包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
public class CollectionStationWrapper extends BaseEntityWrapper<CollectionStation, CollectionStationVO> {

	public static CollectionStationWrapper build() {
		return new CollectionStationWrapper();
	}

	@Override
	public CollectionStationVO entityVO(CollectionStation collectionStation) {
		CollectionStationVO collectionStationVO = Objects.requireNonNull(BeanUtil.copy(collectionStation, CollectionStationVO.class));
		String online = DictBizCache.getValue(DictBizEnum.ONLINE_STATE, collectionStation.getOnline());
		collectionStationVO.setOnlineName(online);
		String isWireless = DictBizCache.getValue(DictBizEnum.SENSOR_TYPE, collectionStation.getIsWireless());
		collectionStationVO.setIsWirelessName(isWireless);

		Optional.ofNullable(collectionStation.getCreateUser())
			.map(UserCache::getUser)
			.ifPresent(user -> collectionStationVO.setCreateUserName(user.getName()));

		Optional.ofNullable(collectionStation.getUpdateUser())
			.map(UserCache::getUser)
			.ifPresent(user -> collectionStationVO.setUpdateUserName(user.getName()));

		return collectionStationVO;
	}

}
