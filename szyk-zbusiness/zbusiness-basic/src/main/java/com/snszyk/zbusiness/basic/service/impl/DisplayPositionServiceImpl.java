/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.entity.DisplayPosition;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import com.snszyk.zbusiness.basic.enums.DimensionCategoryEnum;
import com.snszyk.zbusiness.basic.mapper.DisplayPositionMapper;
import com.snszyk.zbusiness.basic.service.IDisplayPositionService;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.vo.DisplayPositionVO;
import com.snszyk.zbusiness.basic.vo.PlanarPositionVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 虚拟展示位置表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
@AllArgsConstructor
@Service
public class DisplayPositionServiceImpl extends ServiceImpl<DisplayPositionMapper, DisplayPosition> implements IDisplayPositionService {

	private final ISensorInstanceService sensorInstanceService;

	@Override
	public boolean submit(PlanarPositionVO vo) {
		// 位置列表为空，清空当前设备下的所有位置信息
		if(Func.isEmpty(vo.getDisplayPositionList())){
			return baseMapper.removeByEquipment(vo.getEquipmentId()) >= 0;
		}
		List<DisplayPosition> list = this.list(Wrappers.<DisplayPosition>query().lambda()
			.eq(DisplayPosition::getEquipmentId, vo.getEquipmentId()).eq(DisplayPosition::getCategory, DimensionCategoryEnum.TWO_DIMENSION.getCode()));
		if(Func.isEmpty(list)){
			list = vo.getDisplayPositionList().stream().map(displayPositionVO -> {
				DisplayPosition displayPosition = Objects.requireNonNull(BeanUtil.copy(displayPositionVO, DisplayPosition.class));
				SensorInstance sensorInstance = sensorInstanceService.getOne(Wrappers.<SensorInstance>query().lambda()
					.eq(SensorInstance::getCode, displayPosition.getSensorCode())
					.eq(SensorInstance::getIsDeleted, 0));
				displayPosition.setEquipmentId(sensorInstance.getEquipmentId())
					.setMonitorId(sensorInstance.getMonitorId())
					.setCategory(DimensionCategoryEnum.TWO_DIMENSION.getCode())
					.setCreateUser(AuthUtil.getUserId())
					.setCreateTime(DateUtil.now());
				return displayPosition;
			}).collect(Collectors.toList());
			return this.saveBatch(list);
		}
		List<String> oldSensorCodes = list.stream().map(DisplayPosition::getSensorCode).collect(Collectors.toList());
		List<String> newSensorCodes = vo.getDisplayPositionList().stream().map(DisplayPositionVO::getSensorCode).collect(Collectors.toList());
		oldSensorCodes.forEach(oldSensorCode -> {
			if (!newSensorCodes.contains(oldSensorCode)) {
				baseMapper.removeBySensorCode(oldSensorCode, 0);
			}
		});
		list = vo.getDisplayPositionList().stream().map(displayPositionVO -> {
			DisplayPosition displayPosition = this.getOne(Wrappers.<DisplayPosition>query().lambda()
				.eq(DisplayPosition::getEquipmentId, vo.getEquipmentId()).eq(DisplayPosition::getSensorCode, displayPositionVO.getSensorCode()));
			if(Func.isNotEmpty(displayPosition)){
				displayPosition.setPosition(displayPositionVO.getPosition());
			} else {
				displayPosition = Objects.requireNonNull(BeanUtil.copy(displayPositionVO, DisplayPosition.class));
			}
			SensorInstance sensorInstance = sensorInstanceService.getOne(Wrappers.<SensorInstance>query().lambda()
				.eq(SensorInstance::getCode, displayPosition.getSensorCode())
				.eq(SensorInstance::getIsDeleted, 0));
			displayPosition.setEquipmentId(vo.getEquipmentId())
				.setMonitorId(sensorInstance.getMonitorId())
				.setCategory(DimensionCategoryEnum.TWO_DIMENSION.getCode())
				.setCreateUser(AuthUtil.getUserId())
				.setCreateTime(DateUtil.now());
			return displayPosition;
		}).collect(Collectors.toList());
		return this.saveOrUpdateBatch(list);
	}

	@Override
	public boolean removeByEquipment(Long equipmentId) {
		return baseMapper.removeByEquipment(equipmentId) >= 0;
	}

	@Override
	public boolean removeByMonitor(Long monitorId) {
		return baseMapper.removeByMonitor(monitorId) >= 0;
	}

	@Override
	public boolean removeBySensorCode(String sensorCode) {
		return baseMapper.removeBySensorCode(sensorCode, null) >= 0;
	}

}
