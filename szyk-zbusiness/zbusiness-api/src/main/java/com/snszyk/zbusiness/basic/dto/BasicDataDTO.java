package com.snszyk.zbusiness.basic.dto;


import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.entity.Wave;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 基础数据（设备、测点、传感器）集合
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "基础数据（设备、测点、传感器、波形配置）集合", description = "基础数据（设备、测点、传感器、波形配置）集合")
public class BasicDataDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 是否需要点检：0-不需要，1-需要
	 */
	@ApiModelProperty(value = "是否需要点检：0-不需要，1-需要")
	private Integer needSpotCheck;

	/**
	 * 设备列表
	 */
	@ApiModelProperty(value = "设备列表")
	private List<Equipment> equipmentList;

	/**
	 * 测点列表
	 */
	@ApiModelProperty(value = "测点列表")
	private List<Monitor> monitorList;

	/**
	 * 测点绑定的传感器列表
	 */
	@ApiModelProperty(value = "测点绑定的传感器列表")
	private List<SensorInstanceDTO> sensorList;

	/**
	 * 传感器配置的波形列表
	 */
	@ApiModelProperty(value = "传感器配置的波形列表")
	private List<Wave> waveList;
}
