# 波形数据保存机制详解

## 概述

本文档详细介绍了AI运维平台中波形数据的保存时机、保存流程、存储位置以及相关技术实现。波形数据是设备状态监测和故障诊断的重要基础数据，系统采用实时处理和分层存储的方式确保数据的完整性和可用性。

## 1. 波形数据保存时机

### 1.1 触发条件

波形数据的保存是**实时触发**的，具体触发条件如下：

- **数据源**：传感器采集到的原始数据
- **传输方式**：通过RabbitMQ消息队列传输
- **触发时机**：系统接收到包含波形数据的消息时
- **判断条件**：`message.getWave()` 字段不为空

### 1.2 数据类型分类

不同类型的传感器数据有不同的处理方式：

| 传感器类型 | 数据类型 | 是否保存波形 | 处理器 |
|-----------|---------|-------------|--------|
| 振动传感器 | 加速度、速度、位移 | ✅ 是 | VibrateSensorDataExecutor |
| 应力波传感器 | 应力波数据 | ✅ 是 | StressSensorDataExecutor |
| 状态传感器 | 在线状态、电量 | ❌ 否 | StateSensorDataExecutor |
| 温度传感器 | 温度数据 | ❌ 否 | VibrateSensorDataExecutor |

## 2. 数据处理流程

### 2.1 整体流程图

```mermaid
flowchart TD
    A[传感器采集数据] --> B[发送到消息队列]
    B --> C[RabbitMQHandler接收消息]
    C --> D{根据command类型选择处理器}
    D --> E[VibrateSensorDataExecutor<br/>振动传感器数据处理器]
    D --> F[StressSensorDataExecutor<br/>应力波传感器数据处理器]
    D --> G[StateSensorDataExecutor<br/>状态传感器数据处理器]
    
    E --> H[AbstractSensorDataExecutor.saveData]
    F --> H
    G --> I[只保存到Redis，不保存波形]
    
    H --> J{消息中是否包含波形数据?}
    J -->|是| K[设置波形文件路径]
    J -->|否| L[只保存特征值数据]
    
    K --> M[writeFile方法<br/>保存时域波形文件到磁盘]
    K --> N[保存数据到InfluxDB]
    K --> O[保存波形数据到InfluxDB<br/>表名: waveId_1]
    
    L --> N
    
    M --> P[波形文件保存完成<br/>路径: /diagnosis_wave/年/月/日/waveId/时间戳]
    N --> Q[数据保存完成]
    O --> Q
```

### 2.2 详细处理步骤

1. **消息接收**
   - 队列名称：`EolmConstant.Rabbit.QUEUE_POINT_VALUE_EOLM`
   - 处理类：`RabbitMQHandler`
   - 方法：`handler(MessageBean message)`

2. **数据验证**
   - 验证传感器实例是否存在
   - 验证波形配置是否正确
   - 获取测量方向等参数

3. **路径生成**
   - 根据时间戳生成文件存储路径
   - 格式：`/diagnosis_wave/{年}/{月}/{日}/{waveId}/`

4. **数据保存**
   - 保存波形文件到磁盘
   - 保存元数据到InfluxDB
   - 更新Redis缓存

## 3. 存储架构

### 3.1 分层存储设计

系统采用三层存储架构：

```
┌─────────────────┐
│   Redis缓存     │  ← 最新数据、停机线等
├─────────────────┤
│   InfluxDB      │  ← 时序数据、元数据
├─────────────────┤
│   文件系统      │  ← 波形原始数据
└─────────────────┘
```

### 3.2 文件存储结构

**存储路径格式：**
```
{配置根路径}/diagnosis_wave/{年}/{月}/{日}/{waveId}/{时间戳}
```

**示例路径：**
```
/data/attach/diagnosis_wave/2025/0/17/123456789/1737097200000
```

**路径说明：**
- `diagnosis_wave`：波形数据根目录
- `年/月/日`：按日期分层，便于管理和清理
- `waveId`：波形ID，对应具体的传感器波形配置
- `时间戳`：数据采集的原始时间戳，作为文件名

### 3.3 InfluxDB存储

**数据表结构：**

| 表名 | 用途 | 数据内容 |
|------|------|----------|
| `{waveId}` | 特征值数据 | 有效值、峰值、频率等特征参数 |
| `{waveId}_1` | 波形数据 | 包含波形文件路径的完整数据记录 |
| `sensor_raw_data` | 原始数据 | 全局数据表，用于统计和查询 |

**字段配置：**
- **Tag字段**：`sensorCode`, `sampleDataType`, `waveId`, `invalid`, `monitorId`
- **Field字段**：`value`, `hasWaveData`, `waveformUrl`, `samplingFreq` 等

## 4. 核心代码实现

### 4.1 波形数据判断和保存

```java
// 判断是否包含波形数据
if (Func.isNotEmpty(message.getWave())) {
    // 设置波形文件URL
    sensorData.setWaveformUrl(String.format("%s%s", fileUrl, message.getOriginTime()));
    fileUrl = String.format("%s%s", szykAttachConfig.szykAttachProperties().getPath(), fileUrl);
    sensorData.setHasWaveData(1);
    
    // 保存时域波形文件到磁盘
    writeFile(fileUrl, sensorData, message.getWave());
    
    // 保存波形数据到InfluxDB
    String table = String.format("%s_%s", wave.getId().toString(), "1");
    influxdbTools.insert(wave.getMonitorId().toString(), table, data, 
                        sensorData.getOriginTime().getTime());
}
```

### 4.2 文件写入实现

```java
private void writeFile(String path, SensorData data, String wave) {
    File file = new File(path);
    if (!file.exists()) {
        boolean flag = file.mkdirs();
        log.info("创建文件{}", flag);
    }
    file = new File(String.format("%s/%s", path, data.getOriginTime().getTime()));
    try (FileWriter writer = new FileWriter(file)) {
        writer.write(wave);
        writer.flush();
    } catch (Exception e) {
        e.printStackTrace();
    }
}
```

## 5. 数据查询和使用

### 5.1 波形数据查询

系统提供多种波形数据查询方式：

1. **特征图数据查询**
   - 方法：`WaveFormLogicService.waveForm()`
   - 用途：获取时间序列的特征值数据

2. **时域和频域波形查询**
   - 方法：`WaveFormLogicService.timeAndFreqWave()`
   - 用途：获取具体时刻的波形详细数据

3. **波形文件直接读取**
   - 通过文件路径直接读取原始波形数据
   - 用于详细分析和诊断

### 5.2 数据使用场景

- **实时监控**：显示设备当前状态
- **历史分析**：查看设备历史运行趋势
- **故障诊断**：分析异常时刻的波形特征
- **报告生成**：生成设备健康报告和诊断报告

## 6. 性能优化和注意事项

### 6.1 性能优化措施

1. **分层存储**：热数据在Redis，温数据在InfluxDB，冷数据在文件系统
2. **按时间分片**：文件按日期分层存储，便于管理和清理
3. **异步处理**：使用线程池异步处理业务逻辑
4. **批量操作**：支持批量数据插入和查询

### 6.2 注意事项

1. **磁盘空间管理**：定期清理过期的波形文件
2. **并发控制**：数据插入使用同步锁避免冲突
3. **异常处理**：完善的异常处理机制确保数据不丢失
4. **监控告警**：监控存储空间和处理性能

## 7. 配置说明

### 7.1 相关配置项

- **文件存储路径**：`szykAttachConfig.szykAttachProperties().getPath()`
- **InfluxDB配置**：连接信息、数据库名称等
- **Redis配置**：缓存策略、过期时间等
- **消息队列配置**：队列名称、处理器映射等

### 7.2 环境要求

- **Java版本**：JDK 8+
- **InfluxDB版本**：2.x
- **Redis版本**：5.x+
- **RabbitMQ版本**：3.8+

## 8. 总结

波形数据保存机制是AI运维平台的核心功能之一，通过实时处理、分层存储和高效查询，为设备监测和故障诊断提供了可靠的数据基础。系统设计充分考虑了性能、可靠性和可扩展性，能够满足大规模工业设备监测的需求。

---

**文档版本**：v1.0  
**最后更新**：2025-01-17  
**作者**：qcl
