<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.EquipmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="equipmentResultMap" type="com.snszyk.zbusiness.basic.entity.Equipment">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="device_id" property="deviceId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="model" property="model"/>
        <result column="frequency" property="frequency"/>
        <result column="rev" property="rev"/>
        <result column="power" property="power"/>
        <result column="image" property="image"/>
        <result column="grade" property="grade"/>
        <result column="category" property="category"/>
        <result column="is_fault" property="isFault"/>
        <result column="scene_id" property="sceneId"/>
        <result column="produce_tech" property="produceTech"/>
        <result column="is_running" property="isRunning"/>
        <result column="origin_runtime" property="originRuntime"/>
        <result column="running_time" property="runningTime"/>
        <result column="shutdown_time" property="shutdownTime"/>
        <result column="current_runtime" property="currentRuntime"/>
        <result column="path" property="path"/>
        <result column="sort" property="sort"/>
        <result column="inspect_status" property="inspectStatus"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="equipmentDTOResultMap" type="com.snszyk.zbusiness.basic.dto.EquipmentDTO">
        <result column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="model" property="model"/>
        <result column="grade" property="grade"/>
        <result column="category" property="category"/>
        <result column="scene_id" property="sceneId"/>
        <result column="produce_tech" property="produceTech"/>
        <result column="is_running" property="isRunning"/>
        <result column="origin_runtime" property="originRuntime"/>
        <result column="running_time" property="runningTime"/>
        <result column="path" property="path"/>
        <result column="path_name" property="pathName"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="sort" property="sort"/>
        <result column="inspect_status" property="inspectStatus"/>
        <result column="status" property="status"/>
    </resultMap>


    <select id="page" resultMap="equipmentDTOResultMap">
        SELECT
            e.*,t.path_name,t.alarm_level
        FROM
            eolm_equipment e
        LEFT JOIN sidas_basic_tree t ON t.id = e.id
        WHERE
            e.is_deleted = 0 and e.device_id IN
        <foreach collection="equipment.deviceIds" item="deviceIds" index="index" open="(" close=")" separator=",">
            #{deviceIds}
        </foreach>
        <if test="equipment.keywords!=null and equipment.keywords != ''">
            and (e.code like concat('%',#{equipment.keywords},'%') or e.`name` like
            concat('%',#{equipment.keywords},'%'))
        </if>
        <if test="equipment.isFault!=null">
            and e.is_fault =#{equipment.isFault}
        </if>
        <if test="equipment.alarmLevel!=null">
            and t.alarm_level =#{equipment.alarmLevel}
        </if>
        <if test="equipment.grade!=null">
            and e.grade =#{equipment.grade}
        </if>
        ORDER BY e.create_time DESC
    </select>

    <select id="equipmentRunningPage" resultType="com.snszyk.zbusiness.basic.dto.EquipmentRunningStatExcelDTO">
        SELECT e.`code`, e.`name`, e.model, REPLACE(t.path_name,',','/') AS path_name, e.power,
            e.manufacturer, e.is_running, e.origin_runtime, e.running_time, e.shutdown_time, e.create_time
        FROM eolm_equipment e
        LEFT JOIN sidas_basic_tree t ON t.id = e.id
        <where>
            e.is_deleted = 0 and (`type` = 1 or `type` = 3)
            <if test="tenantId != null and tenantId != ''">
                AND e.tenant_id = #{tenantId}
            </if>
            <if test="vo.pathId != null and vo.pathId != ''">
                AND e.path LIKE CONCAT('%', #{vo.pathId}, '%')
            </if>
        </where>
        ORDER BY e.create_time DESC
    </select>

    <select id="equipmentStat" resultType="com.snszyk.zbusiness.basic.dto.EquipmentStatDTO">
        SELECT count(*) AS totalCount,
               sum(`is_running` = 1) AS runningCount,
               sum(`is_running` = 0) AS shutdownCount
        FROM eolm_equipment
        <where>
            is_deleted = 0 and (`type` = 1 or `type` = 3)
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
        </where>
    </select>

    <select id="getIdListOfSameCategory" resultType="java.lang.Long">
        SELECT id
        FROM eolm_equipment
        WHERE is_deleted = 0 and (`type` = 1 or `type` = 3) AND (tenant_id, category) = (SELECT tenant_id, category
                                                          FROM eolm_equipment WHERE id = #{equipmentId})
    </select>

    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT max(sort)
        FROM eolm_equipment where is_deleted = 0 and (`type` = 1 or `type` = 3)
    </select>

    <select id="alarmTimes" resultType="java.lang.Integer">
        SELECT count(*) AS alarmTimes
        FROM eolm_alarm_record
        WHERE equipment_id = #{equipmentId}
    </select>

    <select id="faultTimes" resultType="java.lang.Integer">
        SELECT count(*) AS faultTimes
        FROM eolm_fault_biz
        WHERE equipment_id = #{equipmentId} AND is_deleted = 0
    </select>

    <select id="getByMonitorId" resultType="com.snszyk.zbusiness.basic.entity.Equipment">
        SELECT *
        FROM eolm_equipment
        WHERE id = (SELECT equipment_id FROM eolm_monitor WHERE id = #{monitorId})
    </select>

    <delete id="removeByIds">
        delete from eolm_equipment where id in
        <foreach collection="list" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
    </delete>

    <update id="resetEquipmentNeedSpotCheck">
        UPDATE eolm_equipment
        SET need_spot_check = 0
        WHERE id in
        <foreach collection="list" item="id" index="i" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="abnormalEquipmentTotal" resultType="com.snszyk.zbusiness.basic.dto.AbnormalEquipmentTotal">
        SELECT COUNT(a.id) ct,SUM(CASE WHEN b.abnormal_level IS NULL THEN 0 ELSE 1 END) fail
        FROM `eolm_equipment` a LEFT JOIN `eolm_abnormal` b ON a.id=b.equipment_id AND b.status!=2
    </select>

    <select id="importantEquipment" resultType="com.snszyk.zbusiness.basic.dto.ImportantEquipment">
        SELECT a.id,a.code,a.name,abnormal_level abnormalLevel,conclusion,suggestion,abnormalReason  FROM `eolm_equipment` a JOIN `eolm_abnormal` b ON a.id=b.equipment_id
        LEFT JOIN (
        SELECT  abnormal_id,GROUP_CONCAT(mechanism_type)abnormalReason  FROM (SELECT abnormal_id,mechanism_type FROM `eolm_abnormal_record` WHERE mechanism_type IS NOT NULL GROUP BY abnormal_id,mechanism_type) a GROUP BY abnormal_id
        ) s ON b.id=s.abnormal_id
        WHERE
            <if test="equipmentId == null">
        <![CDATA[  abnormal_level>1 AND b.create_time > DATE_SUB(NOW(), INTERVAL 1 YEAR) ]]>
            </if>
            <if test="equipmentId != null and  equipmentId != ''">
                a.id = #{equipmentId}
            </if>
            AND b.status!=2
            ORDER BY b.create_time DESC
    </select>
    <select id="equipmentTypeTotal" resultType="com.snszyk.zbusiness.basic.dto.EquipmentTypeTotal">
        SELECT mechanism_type mechanismType,COUNT(*)ct FROM eolm_abnormal a  JOIN (SELECT abnormal_id,mechanism_type,abnormal_time FROM eolm_abnormal_record   GROUP BY  abnormal_id,mechanism_type,abnormal_time) b ON a.id=b.abnormal_id
        <![CDATA[ WHERE mechanism_type IS NOT NULL AND  create_time > DATE_SUB(NOW(), INTERVAL 1 YEAR) ]]>
        <if test="equipmentId != null and  equipmentId != ''">
            AND a.equipment_id=#{equipmentId}
        </if>
        GROUP BY mechanism_type

    </select>
    <select id="equipmentFrequency" resultType="com.snszyk.zbusiness.basic.dto.EquipmentFrequency">
        SELECT `name`,GROUP_CONCAT(CONCAT(ye,'-',lpad(mon,2,'0'))) mon,GROUP_CONCAT(ct) ct FROM (
            SELECT a.name,YEAR(first_time) ye,MONTH(first_time) mon,COUNT(*) ct FROM `eolm_equipment` a JOIN `eolm_abnormal` b ON a.id=b.equipment_id
            WHERE b.create_time>DATE_SUB(NOW(), INTERVAL 1 YEAR) GROUP BY a.name,YEAR(first_time),MONTH(first_time)  ORDER BY a.name,YEAR(first_time),MONTH(first_time) ASC
        ) a
        GROUP BY a.name ORDER BY SUM(ct) DESC  LIMIT 0,3
    </select>
    <select id="abnormalScatter" resultType="com.snszyk.zbusiness.basic.dto.AbnormalScatter">
        SELECT abnormal_level abnormalLevel,COUNT(*) ct  FROM `eolm_equipment` a
            LEFT JOIN `eolm_abnormal` b ON a.id=b.equipment_id
            WHERE abnormal_level IS NOT NULL
            <if test="equipmentId != null">
                <![CDATA[ AND a.id=#{equipmentId}
                AND  b.create_time>DATE_SUB(NOW(), INTERVAL 1 YEAR) ]]>
            </if>
            GROUP BY abnormal_level
    </select>

    <select id="equipmentRunningMonitor" resultType="com.snszyk.zbusiness.basic.dto.EquipmentRunningMonitorDTO">
        SELECT SUM(IF(ct IS NULL, 0, ct)) ct ,a.id,a.name,a.code,a.image,a.start_date_of_use startDateOfUse,a.life_expectancy lifeExpectancy,a.path_name pathName,COALESCE(b.alarm_level,0) alarmLevel,COALESCE(MAX(abnormal_level),0) abnormalLevel,GROUP_CONCAT(conclusion)conclusion
        FROM `eolm_equipment`  a JOIN `sidas_basic_tree` b ON a.id=b.id AND a.is_deleted=0
        LEFT JOIN `eolm_abnormal` d ON a.id=d.equipment_id and d.status!=2
        LEFT JOIN (SELECT equipment_id,COUNT(*) ct FROM eolm_monitor WHERE is_deleted=0 GROUP BY equipment_id) c ON a.id=c.equipment_id
        <where>
            <if test="equipment.id != null and equipment.id != ''">
                and a.id=#{equipment.id}
            </if>
            <if test="equipment.category != null and equipment.category != ''">
                and a.category=#{equipment.category}
            </if>
            <if test="equipment.alarmLevel != null and equipment.alarmLevel != ''">
                and b.alarm_level=#{equipment.alarmLevel}
            </if>
            <if test="equipment.path != null and equipment.path != ''">
                and a.path LIKE concat('%',#{equipment.path},'%')
            </if>
            <if test="equipment.important != null and equipment.important==1">
                <![CDATA[ and abnormal_level>1  AND d.status!=2]]>
            </if>
            <if test="equipment.failLevel != null">
                and COALESCE(abnormal_level,0)=#{equipment.failLevel}
                <if test="equipment.failLevel != 0">
                    AND d.status!=2
                </if>
            </if>
        </where>
        GROUP BY a.id,a.name,a.code,a.image,a.start_date_of_use,a.life_expectancy,a.path_name,b.alarm_level
    </select>

    <select id="equipmentMonitorAbnormal" resultType="com.snszyk.zbusiness.basic.dto.EquipmentMonitorAbnormalDTO">
        SELECT d.name,d.code,a.suggestion,GROUP_CONCAT(DISTINCT mechanism_type) abnormalType FROM `eolm_abnormal` a JOIN `eolm_abnormal_detail` b ON a.id=b.abnormal_id JOIN `eolm_abnormal_record` c ON b.wave_id=c.wave_id JOIN `eolm_monitor` d ON b.monitor_id=d.id
        WHERE a.equipment_id=#{equipmentId} AND mechanism_type IS NOT NULL
        GROUP BY d.name,d.code,a.suggestion
    </select>

    <select id="equipmentLedger" resultType="com.snszyk.zbusiness.basic.dto.EquipmentDTO">
        SELECT
            a.id,
            a.device_id deviceId,
            a.CODE,
            a.NAME,
            a.model,
            a.frequency,
            a.rev,
            a.power,
            a.image,
            a.grade,
            a.category,
            a.path_name pathName,
            a.is_fault isFault,
            IFNULL( b.abnormal_level, 0 ) abnormalLevel,
            b.abnormal_reason,
            b.conclusion,
            b.suggestion,
            b.first_time,
            b.last_time
        FROM
        `eolm_equipment` a
        LEFT JOIN eolm_abnormal b ON a.id = b.equipment_id AND b.STATUS = 0
        <where>
            a.is_deleted = 0
            <if test="equipment.deviceId != null">
                AND a.path LIKE CONCAT('%', #{equipment.deviceId}, '%')
            </if>
            <if test="equipment.isFault != null">
                AND a.is_fault=#{equipment.isFault}
            </if>
            <if test="equipment.grade != null">
                AND a.grade=#{equipment.grade}
            </if>
            <if test="equipment.abnormalLevel != null">
                AND IFNULL(b.abnormal_level,0) = #{equipment.abnormalLevel}
            </if>
            <if test="equipment.keywords!=null and equipment.keywords != ''">
                and (a.code like concat('%',#{equipment.keywords},'%') or a.`name` like
                concat('%',#{equipment.keywords},'%'))
            </if>
            <if test="equipment.isAbnormal!=null">
                <choose>
                    <when test="equipment.isAbnormal == 1">
                        AND b.id IS NOT NULL
                    </when>
                    <when test="equipment.isAbnormal == 0">
                        AND b.id IS NULL
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="getWaveList" resultType="com.snszyk.zbusiness.ai.dto.AiWaveDto">
        SELECT w.*
        FROM sidas_wave w
                 LEFT JOIN basic_sensor_instance_param s
                           ON w.sensor_instance_param_id = s.id
        WHERE w.monitor_id = #{monitorId}
          AND s.vibration_type = 1
          AND w.unbind = 0
    </select>

    <select id="waveList" resultType="com.snszyk.zbusiness.ai.dto.AiWaveDto">
        SELECT w.*, #{monitorId} as parent_id, s.sampling_freq*1000 as sampling_freq, s.sampling_points,
               t.initial_freq, s.sampling_freq*1000/2.56 as cutoff_freq, s.vibration_type
        FROM sidas_wave w
                 LEFT JOIN basic_sensor_instance_param s ON w.sensor_instance_param_id = s.id
                 LEFT JOIN basic_sensor_instance i ON s.instance_id = i.id
                 LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        WHERE w.monitor_id = #{monitorId} AND w.unbind = 0 and s.vibration_type = 1
    </select>

    <select id="getEquipmentInfo" resultType="com.snszyk.zbusiness.ai.dto.AiEquipmentDto">
        select * from eolm_equipment where id = #{equipmentId} and is_deleted = 0
    </select>

    <select id="getEquipmentByMonitorId" resultType="com.snszyk.zbusiness.ai.dto.AiEquipmentDto">
        select e.* from eolm_equipment e
                            left join eolm_monitor m
                                      on e.id = m.equipment_id
        where m.id = #{monitorId} and e.is_deleted = 0
    </select>

    <select id="getMonitorList" resultType="com.snszyk.zbusiness.ai.dto.AiMonitorDto">
        select * from eolm_monitor where equipment_id = #{equipmentId} and is_deleted = 0
    </select>

    <select id="equipmentAlarm" resultType="com.snszyk.zbusiness.ai.dto.AiEquipmentAlarmDto">
        select * from eolm_diagnosis_record where equipment_id = #{equipmentId} order by diagnose_time desc limit 1
    </select>

</mapper>
