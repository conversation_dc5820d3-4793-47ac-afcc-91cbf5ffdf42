//package com.snszyk.zbusiness.basic.config;
//
//import com.influxdb.client.InfluxDBClient;
//import com.snszyk.common.config.InfluxdbProperties;
//import com.snszyk.common.utils.CollectSensorDataInfluxdb;
//import com.snszyk.common.utils.InfluxdbTools;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import javax.annotation.Resource;
//
//@Configuration
//public class InfluxdbConfig {
//
//	@Resource
//	private InfluxdbProperties influxdbProperties;
//	@Resource
//	private InfluxDBClient influxDBClient;
//	@Bean
//	public InfluxdbTools createInfluxdb(){
//		return new InfluxdbTools(influxDBClient,influxdbProperties);
//	}
//
//	@Bean
//	public CollectSensorDataInfluxdb createCollectSensorDataInfluxdb(){
//		return new CollectSensorDataInfluxdb(influxDBClient,influxdbProperties);
//	}
//}
