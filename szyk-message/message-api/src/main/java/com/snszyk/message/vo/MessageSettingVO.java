package com.snszyk.message.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalTime;

/**
 * 消息设置视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "消息设置视图对象", description = "消息设置视图对象")
public class MessageSettingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型", required = true)
    private String bizType;

    /**
     * 发送策略
     */
    @NotBlank(message = "发送策略不能为空")
    @ApiModelProperty(value = "发送策略", required = true)
    private String sendStrategy;

    /**
     * 固定发送时间
     */
    @ApiModelProperty(value = "固定发送时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime fixedSendTime;

    /**
     * 检查间隔值
     */
    @ApiModelProperty(value = "检查间隔值")
    private Integer checkInterval;

    /**
     * 间隔单位
     */
    @ApiModelProperty(value = "间隔单位")
    private String intervalUnit;

    /**
     * 接收人类型
     */
    @NotBlank(message = "接收人类型不能为空")
    @ApiModelProperty(value = "接收人类型", required = true)
    private String receiverType;

    /**
     * 接收人信息
     */
    @NotBlank(message = "接收人信息不能为空")
    @ApiModelProperty(value = "接收人信息", required = true)
    private String receiverInfo;

    /**
     * 发送渠道
     */
    @NotBlank(message = "发送渠道不能为空")
    @ApiModelProperty(value = "发送渠道", required = true)
    private String channel;

    /**
     * 是否启用
     */
    @NotNull(message = "是否启用不能为空")
    @ApiModelProperty(value = "是否启用", required = true)
    private Integer enabled;
}