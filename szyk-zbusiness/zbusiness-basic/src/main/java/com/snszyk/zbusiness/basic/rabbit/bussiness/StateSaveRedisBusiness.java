package com.snszyk.zbusiness.basic.rabbit.bussiness;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.DingTalkMessageVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.entity.User;
import com.snszyk.system.service.IUserSearchService;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceParamDTO;
import com.snszyk.zbusiness.basic.dto.SensorOfflineDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.entity.CollectionStationChannel;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 1、状态类型（传感器在线状态、传感器剩余电量）保存最新的数据到Redis
 * key为 "sensorCode:sensorInstanceParamId:sampleDataType"
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class StateSaveRedisBusiness extends AbstractBusiness {

	private final ISensorInstanceParamService sensorInstanceParamService;
	private final ISensorInstanceService sensorInstanceService;
	private final IMonitorService monitorService;
	private final MessageLogicService messageLogicService;
	private final IUserSearchService userSearchService;
	private final SzykRedis szykRedis;
	private final RabbitTemplate rabbitTemplate;

	@Override
	public String getCommand() {
		return Command.STATE_COMMAND;
	}

	@Resource
	private ICollectionStationChannelService channelService;

	@Resource
	private ICollectionStationService stationService;

	@Override
	public void business(MessageBean message) {
		super.business(message);

		if (SampledDataTypeEnum.SENSOR_ONLINE.getCode().equals(message.getType())) {
			String key = message.getId() + StringPool.COLON + message.getType();
			if (BigDecimal.ONE.compareTo(new BigDecimal(message.getValue())) == 0) {
				szykRedis.setEx(key, message.getValue(),
					Duration.ofMinutes(EolmConstant.Cache.COLLECTION_STATION_AND_SENSOR_STATUS_TIMEOUT));
			} else {
				log.warn("发送报警信息");
				sendAlarm(message);
				szykRedis.del(key);
			}
		} else {
			SensorInstanceParamDTO stateParam = sensorInstanceParamService.getStateParam(message.getId(), message.getType());
			if (stateParam == null) {
				log.warn("StateSaveRedisBusiness.business() - 没找到对应的传感器实例参数！message = {}", message);
				return;
			}
			String key = message.getId() + StringPool.COLON + stateParam.getId() + StringPool.COLON + message.getType();
			szykRedis.set(key, message.getValue());
		}

		log.info("开始更新采集站状态及传感器在线状态");
		List<CollectionStationChannel> channelList = channelService.list(new QueryWrapper<CollectionStationChannel>()
			.lambda().eq(CollectionStationChannel::getSensorCode, message.getId()));
		if (Func.isEmpty(channelList)) {
			return;
		}
		channelList.forEach(channel -> {
			CollectionStation station = new CollectionStation();
			station.setId(channel.getStationId());
			station.setOnline(1);
			stationService.updateById(station);
		});
	}


	private void sendAlarm(MessageBean message) {
		// 查询传感器参数id
		String onlineStateKey = message.getId() + StringPool.COLON + message.getType();
		SensorInstanceDTO sensorInstance = sensorInstanceService.detailByCode(message.getId());
		Monitor monitor = monitorService.getById(sensorInstance.getMonitorId());
		if (szykRedis.get(onlineStateKey) == null) {
			log.info("StateSendAlarmBusiness - 已是离线，暂不发送离线消息！");
			return;
		} else {
			log.info("StateSendAlarmBusiness - 在线中，变为离线，发送传感器离线通知消息...");
		}
		// 发送离线通知
		List<User> users = userSearchService.listAllUser(monitor.getTenantId());
		if (Func.isNotEmpty(users)) {
			//传感器实例（测点路径）
			SensorInstanceDTO sensorInstanceDTO = sensorInstanceService.detailByCode(message.getId());
			//发送采集站离线待办消息
			MessageVo messageVo = new MessageVo();
			messageVo.setAppKey("SiDAs");
			messageVo.setTitle(MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知");
			SensorOfflineDTO messageContent = new SensorOfflineDTO()
				.setPathName(sensorInstanceDTO.getPathName().replace(StringPool.COMMA, StringPool.SLASH))
				.setSensorCode(sensorInstanceDTO.getCode());
			messageVo.setContent(JSONObject.toJSONString(messageContent));
			messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
			messageVo.setBizType(MessageBizTypeEnum.SENSOR_OFFLINE.getCode());
			messageVo.setBizId(sensorInstanceDTO.getId().toString());
			messageVo.setSender("SiDAs");
			messageVo.setIsImmediate(YesNoEnum.YES.getCode());
			messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
			ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
			List<ReceiverInfoVo.UserVo> userVoList = users.stream().map(user -> {
				ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
				userVo.setId(user.getId());
				userVo.setRealName(user.getRealName());
				return userVo;
			}).collect(Collectors.toList());
			receiverInfoVo.setUserList(userVoList);
			messageVo.setReceiverInfoVo(receiverInfoVo);
			log.info("发送" + MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知 - 接收人：{}", JSONObject.toJSONString(userVoList));
			R messageResult = messageLogicService.commitMessage(messageVo);
			log.info("发送" + MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "通知 - 结果：{}", JSONObject.toJSONString(messageResult));
			// 发送钉钉消息
			Tenant tenant = SysCache.getTenant(monitor.getTenantId());
			log.info(MessageBizTypeEnum.SENSOR_OFFLINE.getMessage() + "-发送钉钉消息：==================={}", sensorInstanceDTO.getCode());
			DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(tenant.getTenantId(),
				tenant.getTenantName(), sensorInstanceDTO.getCode(),
				MessageBizTypeEnum.SENSOR_OFFLINE.getCode(), JSONUtil.toJsonStr(sensorInstanceDTO));
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
				EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE, dingTalkMessage);
		} else {
			log.warn("获取用户列表失败！code = {}, msg = {}.");
		}
	}

}
