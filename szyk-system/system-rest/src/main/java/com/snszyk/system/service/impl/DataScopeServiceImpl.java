/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.system.entity.DataScope;
import com.snszyk.system.entity.Role;
import com.snszyk.system.entity.RoleScope;
import com.snszyk.system.entity.User;
import com.snszyk.system.mapper.DataScopeMapper;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.system.mapper.RoleMapper;
import com.snszyk.system.mapper.RoleScopeMapper;
import com.snszyk.system.service.IDataScopeService;
import com.snszyk.system.vo.DelDetailVO;
import com.snszyk.system.vo.DelResultVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class DataScopeServiceImpl extends BaseServiceImpl<DataScopeMapper, DataScope> implements IDataScopeService {

	private final RoleMapper roleMapper;
	private final RoleScopeMapper roleScopeMapper;
	@Override
	public DelResultVO checkAndRemoveScope(List<Long> ids) {
		DelResultVO resultVO = new DelResultVO();
		ids.stream().forEach(id -> {
			//查询数据权限信息
			DataScope dataScope = this.getById(id);
			//如果数据权限不存在，直接失败返回
			if (dataScope == null) {
				resultVO.getDetailVOList().add(new DelDetailVO(id.toString(),Boolean.FALSE, "数据权限不存在"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//如果存在角色引用，不允许删除
			List<RoleScope> refs = roleScopeMapper.selectList(Wrappers.<RoleScope>query().lambda().like(RoleScope::getScopeId, id));
			if (CollectionUtil.isEmpty(refs)) {
				//如果无角色引用，则删除此数据权限
				this.removeById(id);
				resultVO.getDetailVOList().add(new DelDetailVO(dataScope.getScopeName(),Boolean.TRUE, "删除成功"));
				//成功次数+1
				resultVO.setSuccessNumber(resultVO.getSuccessNumber() + 1);
			} else {
				//存在角色引用不允许删除，收集引用的角色名称放入失败提示信息
				Long[] roleIdArr = refs.stream().map(RoleScope::getRoleId).collect(Collectors.toSet()).toArray(new Long[0]);
				List<String> roleNames = roleMapper.getRoleNames(roleIdArr);
				resultVO.getDetailVOList().add(new DelDetailVO(dataScope.getScopeName(),Boolean.FALSE, "数据权限信息被角色引用，无法删除，具体如下："
					+ StringUtil.collectionToDelimitedString(roleNames, ",")));
				//失败次数+1
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
			}
		});
		return resultVO;
	}
}
