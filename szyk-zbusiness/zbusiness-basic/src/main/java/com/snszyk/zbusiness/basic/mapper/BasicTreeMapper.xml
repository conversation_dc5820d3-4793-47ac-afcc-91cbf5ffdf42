<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.BasicTreeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="basicTreeResultMap" type="com.snszyk.zbusiness.basic.entity.BasicTree">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="path" property="path"/>
        <result column="path_name" property="pathName"/>
        <result column="node_category" property="nodeCategory"/>
        <result column="node_code" property="nodeCode"/>
        <result column="node_name" property="nodeName"/>
        <result column="alarm_level" property="alarmLevel"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <resultMap id="basicTreeVOResultMap" type="com.snszyk.zbusiness.basic.vo.BasicTreeVO">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="ancestors" property="ancestors"/>
        <result column="path" property="path"/>
        <result column="path_name" property="pathName"/>
        <result column="node_category" property="nodeCategory"/>
        <result column="node_code" property="nodeCode"/>
        <result column="node_name" property="nodeName"/>
        <result column="sort" property="sort"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="com.snszyk.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <select id="lazyList" resultMap="basicTreeVOResultMap">
        SELECT
        tree.* ,
        <if test="param3.nodeCategory != 1">
            (
            SELECT
            CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
            FROM
            sidas_basic_tree
            WHERE
            parent_id = tree.id
            ) AS "has_children"
        </if>
        <if test="param3.nodeCategory == 1">
            (
            SELECT
            CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
            FROM
            sidas_basic_tree temp
            LEFT JOIN `sidas_wave` wave ON wave.monitor_id = temp.id
            WHERE wave.monitor_id = tree.id
            ) AS "has_children"
        </if>
        FROM
        sidas_basic_tree tree
        WHERE 1=1
        <if test="param1!=null and param1!=''">
            and tree.tenant_id = #{param1}
        </if>
        <if test="param2!=null">
            and tree.parent_id = #{param2}
        </if>
        <if test="param3.nodeName!=null and param3.nodeName!=''">
            and tree.node_name like concat(concat('%', #{param3.nodeName}),'%')
        </if>
        <if test="param3.clientType=='PORTAL'">
            and tree.node_type != 2
        </if>
        <if test="param3.nodeCategory != null">
            and tree.node_category = #{param3.nodeCategory}
        </if>
        ORDER BY tree.sort
    </select>

    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, node_name as title, id as "value", id as "key" from sidas_basic_tree where 1=1
        <if test="_parameter!=null and _parameter!=''">
            and tenant_id = #{_parameter}
        </if>
        ORDER BY sort
    </select>

    <select id="lazyTree" resultMap="treeNodeResultMap" >
        SELECT
            tree.id,
            tree.parent_id,
            tree.node_name AS title,
            tree.id AS "value",
            tree.id AS "key",
            (
                SELECT
                    CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
                FROM
                    sidas_basic_tree
                WHERE
                    parent_id = tree.id
            ) AS "has_children"
        FROM
            sidas_basic_tree tree
        WHERE
            tree.parent_id = #{param2}
        <if test="param1!=null and param1!=''">
            and tree.tenant_id = #{param1}
        </if>
        ORDER BY tree.sort
    </select>

    <select id="getNodeNames" resultType="java.lang.String">
        SELECT
            node_name
        FROM
            sidas_basic_tree
        WHERE
            id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
    </select>

    <select id="selectDeviceTree" resultMap="basicTreeVOResultMap">
        SELECT
            tree.*,
            (
                SELECT
                    CASE WHEN count(1) > 0 THEN 1 ELSE 0 END
                FROM
                    sidas_basic_tree
                WHERE
                    parent_id = tree.id
            ) AS "has_children"
        FROM sidas_basic_tree tree WHERE tree.node_category = 0
        <if test="param1!=null and param1!=''">
            and tree.tenant_id = #{param1}
        </if>
        <if test="param2!=null">
            and (tree.id = #{param2} or tree.ancestors like concat(concat('%', #{param2}),'%'))
        </if>
        ORDER BY tree.create_time
    </select>

    <select id="cockpitDeviceTree" resultMap="treeNodeResultMap">
        SELECT
            tree.id,
            tree.parent_id,
            tree.node_name AS title,
            tree.node_level AS "value",
            0 AS "key"
        FROM
            `sidas_basic_tree` tree
        WHERE
            tree.node_category = 0
        <if test="param1==null">
            AND tree.node_level != 0
        </if>
        <if test="param2!=null">
            AND tree.path like concat('%',#{param2},'%')
        </if>
        ORDER BY tree.create_time
    </select>

    <select id="areaList" resultType="com.snszyk.zbusiness.basic.dto.TreeDTO">
        SELECT b.node_name nodeName,REPLACE(b.path_name,',','/') pathName,b.path,b.id FROM `eolm_equipment` a
            JOIN `sidas_basic_tree` b ON a.device_id=b.id group by b.path,b.id,b.node_name,b.path_name
    </select>

</mapper>
