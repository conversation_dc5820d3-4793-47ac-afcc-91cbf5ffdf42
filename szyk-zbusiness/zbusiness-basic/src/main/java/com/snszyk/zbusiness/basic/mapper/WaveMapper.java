package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.WaveDTO;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.vo.WaveVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 传感器波形表 Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface WaveMapper extends BaseMapper<Wave> {

	/**
	 * 查询波形
	 *
	 * @param page
	 * @param wave
	 * @return
	 */
	List<WaveDTO> queryWave(IPage page, @Param("wave") WaveVO wave);

	/**
     * 根据波形id删除波形的报警阈值
     * @param waveIdList 波形id
     * @return 删除的条数
     */
    int removeAlarmThresholdByWave(@Param("list") List<Long> waveIdList);

	/**
	 * 解绑波形
	 * @param waveIdList 波形id列表
	 * @return
	 */
    int unbindWaveByIds(@Param("list") List<Long> waveIdList);
}
