/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.core.crud.vo.BaseCrudVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 诊断报告表实体类
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@ApiModel(value = "AiopsDiagnosisReportAVo对象", description = "诊断报告表")
public class AiopsDiagnosisReportAVo {
	private Long id;
	/**
	 * 报告名称
	 */
	@NotBlank(message = "报告名称不能为空")
	@ApiModelProperty(value = "报告名称", required = true)
	private String title;
	/**
	 * 报告类型，类型字典带定义
	 */
	@ApiModelProperty(value = "报告类型，类型字典自定义")
	private Integer type;

	/**
	 * 报告附件id（对应szyk_attach表#id）
	 */
	@NotNull(message = "报告附件id不能为空")
	@ApiModelProperty(value = "报告附件id（对应szyk_attach表#id）", required = true)
	private Long attachId;

	/**
	 * 数据开始日期
	 */
	@NotNull(message = "数据开始日期不能为空")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "数据开始日期", required = true)
	private LocalDate dataStartDate;

	/**
	 * 数据结束日期
	 */
	@NotNull(message = "数据结束日期不能为空")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty(value = "数据结束日期", required = true)
	private LocalDate dataEndDate;

	@ApiModelProperty(value = "租户ID")
	private String tenantId;


}
