/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.common.cache.MybatisRedisCache;
import com.snszyk.zbusiness.basic.dto.SensorInstanceParamDTO;
import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;
import org.apache.ibatis.annotations.CacheNamespace;
import org.apache.ibatis.annotations.Param;

/**
 * 传感器实例参数表 Mapper 接口
 *
 * <AUTHOR>
 */
@CacheNamespace(implementation = MybatisRedisCache.class)
public interface SensorInstanceParamMapper extends BaseMapper<SensorInstanceParam> {

	/**
	 * 查询状态类型的传感器实例参数
	 * @param sensorCode 传感器编码
	 * @param sampleDataType 采样数据类型 - 状态类型
	 * @return
	 */
	SensorInstanceParamDTO getStateParam(@Param("sensorCode") String sensorCode, @Param("sampleDataType") String sampleDataType);
}
