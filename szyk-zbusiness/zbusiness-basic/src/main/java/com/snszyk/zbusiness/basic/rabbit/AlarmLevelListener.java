package com.snszyk.zbusiness.basic.rabbit;

import com.snszyk.common.constant.EolmConstant;
import com.snszyk.zbusiness.basic.service.logic.EquipmentLogicService;
import com.snszyk.zbusiness.basic.service.logic.MonitorLogicService;
import com.snszyk.zbusiness.basic.vo.AlarmLevelVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 报警等级监听Listener
 *
 * <AUTHOR>
 * @date 2023/02/21 13:56
 **/
@Slf4j
@Configuration
public class AlarmLevelListener {

	@Resource
	private EquipmentLogicService equipmentLogicService;
	@Resource
	private MonitorLogicService monitorLogicService;

	@RabbitHandler
	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_ALARM_LEVEL_UPDATE)
	public void updateAlarm(AlarmLevelVO vo){
		log.info("=====》开始处理报警等级数据：========{}", vo);
		monitorLogicService.updateAlarmLevel(vo);
		log.info("=====》报警等级数据处理结束：========{}", vo);
	}

	@RabbitHandler
	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_ALARM_LEVEL_CLOSE)
	public void closeAlarm(Long equipmentId){
		log.info("=====》关闭设备报警：========{}", equipmentId);
		equipmentLogicService.closeAlarm(equipmentId);
	}


}
