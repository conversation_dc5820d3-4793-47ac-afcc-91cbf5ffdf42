package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * AI模型表视图实体类
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
@ApiModel(value = "AiModelVO对象", description = "AI模型表")
public class SwitchSortVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 交换sort的对象类型：0-地点；1-设备；2-测点
	 */
	@ApiModelProperty("交换sort的对象类型：0-地点；1-设备；2-测点")
	private Integer category;

	/**
	 * id1
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "id1")
	private Long srcId;

	/**
	 * id1的原sort值
	 */
	@ApiModelProperty(value = "id1的原sort值")
	private Integer srcSort;

	/**
	 * id2
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "id2")
	private Long destId;

	/**
	 * id2的原sort值
	 */
	@ApiModelProperty(value = "id2的原sort值")
	private Integer destSort;

}
