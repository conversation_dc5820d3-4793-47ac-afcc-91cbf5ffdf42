package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 传感器离线消息内容实体
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorOfflineDTO对象", description = "传感器离线消息内容实体")
public class SensorOfflineDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 路径名称
	 */
	@ApiModelProperty(value = "路径名称")
	private String pathName;

	/**
	 * 传感器编码
	 */
	@ApiModelProperty(value = "传感器编码")
	private String sensorCode;

}
