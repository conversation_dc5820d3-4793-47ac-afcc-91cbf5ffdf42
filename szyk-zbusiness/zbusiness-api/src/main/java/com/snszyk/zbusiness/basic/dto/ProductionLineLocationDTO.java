package com.snszyk.zbusiness.basic.dto;

import com.snszyk.resource.entity.Attach;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductionLineLocationDTO {

	@ApiModelProperty(value = "厂区模型图")
	private Attach attach;

	@ApiModelProperty(value = "产线列表")
	private List<DeviceDTO> deviceList;

}
