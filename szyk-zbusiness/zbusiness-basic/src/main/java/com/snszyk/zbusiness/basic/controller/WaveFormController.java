/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.basic.dto.SampleTimeDTO;
import com.snszyk.zbusiness.basic.dto.SensorDataDTO;
import com.snszyk.zbusiness.basic.dto.WaterfallPlotDTO;
import com.snszyk.zbusiness.basic.dto.WaveFormDTO;
import com.snszyk.zbusiness.basic.service.ISensorDataService;
import com.snszyk.zbusiness.basic.service.logic.WaveFormLogicService;
import com.snszyk.zbusiness.basic.vo.CancelWaveMarkVO;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import com.snszyk.zbusiness.basic.vo.WaveFormVO;
import com.snszyk.zbusiness.basic.vo.WaveVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 数据波形表 控制器
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@RestController
@AllArgsConstructor
@RequestMapping("/waveForm")
@Api(value = "数据波形", tags = "数据波形表接口")
public class WaveFormController extends SzykController {

	private final WaveFormLogicService waveFormLogicService;
	private final ISensorDataService sensorDataService;

	/**
	 * 振动特征值波形 波形表
	 */
	@GetMapping("/waveForm")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "showWave", value = "展示波形（1：全部，0：波形（默认））", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "振动特征值波形", notes = "传入waveForm")
	public R<WaveFormDTO> waveForm(@ApiIgnore WaveFormVO waveForm) {
		return R.data(JSONObject.toJavaObject(waveFormLogicService.mainWaveForm(waveForm),WaveFormDTO.class));
	}

	/**
	 * 特征图谱
	 */
	@GetMapping("/feature")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "waveId", value = "波形id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "showWave", value = "展示波形（1：全部，0：波形（默认））", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "特征图谱", notes = "传入waveForm")
	public List<JSONObject> featureWaveform(@ApiIgnore WaveFormVO waveForm) {
		return waveFormLogicService.waveForm(waveForm);
	}


	@GetMapping("/waveFormPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "waveId", value = "波形id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "startTime", value = "开始时间（yyyy-MM-dd HH:mm:ss）", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endTime", value = "结束时间（yyyy-MM-dd HH:mm:ss）", paramType = "query", dataType = "string"),
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入sensorDataVO")
	public R<IPage<SensorDataDTO>> page(@ApiIgnore SensorDataVO vo, Query query) {
		IPage<SensorDataDTO> result = sensorDataService.page(Condition.getPage(query), vo);
		return R.data(result);
	}

	/**
	 * 时域和频谱图谱 波形表
	 */
	@GetMapping("/timeAndFreqWave")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "originTime", value = "时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "时域和频谱图谱", notes = "传入originTime、waveId")
	public R<WaveFormDTO> timeAndFreqWave(@ApiIgnore SensorDataVO vo) {
		return R.data(waveFormLogicService.timeAndFreqWave(vo));
	}

	/**
	 * 时域频域和包络图谱 波形表
	 */
	@GetMapping("/domainWaveForm")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", required = true, paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "originTime", value = "时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "时域频域和包络图谱", notes = "传入graph")
	public R<WaveFormDTO> domainWaveForm(@ApiIgnore SensorDataVO vo) {
		return R.data(waveFormLogicService.freqAndEnvelope(vo));
	}

	/**
	 * 趋势图 波形表
	 */
	@GetMapping("/trendPlot")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "趋势图（增值、增幅变化波形）", notes = "传入waveForm")
	public R<JSONObject> trendPlot(@RequestParam("startDate") String startDate,
								   @RequestParam("endDate") String endDate,
								   @RequestParam("waveId") String waveId,
								   @RequestParam("monitorId") String monitorId) {
		return R.data(waveFormLogicService.trendPlot(startDate, endDate, waveId, monitorId));
	}

	/**
	 * 机理模型时频域波形 波形表
	 */
	@GetMapping("/sampleTimeByWave")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "根据波形id查询采样时间", notes = "传入monitorId、waveId")
	public R<IPage<SampleTimeDTO>> sampleTimeByWave(SensorDataVO vo, Query query) {
		return R.data(waveFormLogicService.sampleTimeByWave(Condition.getPage(query), vo));
	}

	/**
	 * 瀑布图频域波形 波形表
	 */
	@GetMapping("/waterfallPlot")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "瀑布图频域波形", notes = "传入originTimes(多个用英文','分隔)、waveId、monitorId")
	public R<WaterfallPlotDTO> waterfallPlot(@RequestParam String originTimes, @RequestParam String waveId, @RequestParam String monitorId) {
		return R.data(waveFormLogicService.waterfallPlot(originTimes, waveId, monitorId));
	}

	/**
	 * 波特图 波形表
	 */
	@GetMapping("/bodePlot")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "originTime", value = "时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "波特图", notes = "传入waveForm")
	public R<WaveFormDTO> bodePlot(@ApiIgnore SensorDataVO vo) {
		return R.data(waveFormLogicService.bodePlot(vo));
	}

	/**
	 * 倒谱图 波形表
	 */
	@GetMapping("/cepstrumPlot")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "originTime", value = "时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "倒谱图", notes = "传入waveForm")
	public R<WaveFormDTO> cepstrumPlot(@ApiIgnore SensorDataVO vo) {
		return R.data(waveFormLogicService.cepstrumPlot(vo));
	}

	/**
	 * 相位图 波形表
	 */
	@GetMapping("/phasePlot")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "originTime", value = "时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "相位图", notes = "传入waveForm")
	public R<WaveFormDTO> phasePlot(@ApiIgnore SensorDataVO vo) {
		return R.data(waveFormLogicService.phasePlot(vo));
	}

	/**
	 * 速度图 波形表
	 */
	@GetMapping("/velocityPlot")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "long"),
		@ApiImplicitParam(name = "originTime", value = "时间", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "速度图", notes = "传入waveForm")
	public R<WaveFormDTO> velocityPlot(@ApiIgnore SensorDataVO vo) {
		return R.data(waveFormLogicService.velocityPlot(vo));
	}

	/**
	 * 非振动数据波形 波形表
	 */
//	@GetMapping("/nonVibrationWaveForm")
//	@ApiImplicitParams({
//		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "long"),
//		@ApiImplicitParam(name = "monitorId", value = "测点id", required = true, paramType = "query", dataType = "Long"),
//		@ApiImplicitParam(name = "dataType", value = "特征值类型（字典：sampled_data_type）", required = true, paramType = "query", dataType = "string"),
//		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
//		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
//	})
//	@ApiOperationSupport(order = 13)
//	@ApiOperation(value = "非振动数据波形", notes = "传入waveForm")
//	public R<WaveFormDTO> nonVibrationWaveForm(@ApiIgnore WaveFormVO waveForm) {
//		return R.data(waveFormLogicService.nonVibrationWaveForm(waveForm));
//	}

	/**
	 * 特征图谱：查询波形某种振动数据类型（加速度、速度、位移）的6种通用指标数据
	 */
	@GetMapping("/specificChromatogram")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "showWave", value = "展示波形（1：全部，0：波形（默认））", required = true, paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "特征图谱", notes = "传入waveForm")
	public R<WaveFormDTO> specificChromatogram(@ApiIgnore WaveFormVO waveForm) {
		return R.data(new WaveFormDTO().setDataList(waveFormLogicService.characterWaveForm(waveForm).getDataList()));
	}

	/**
	 * 根据测点id获取振动数据类型下拉
	 */
	@GetMapping("/getVibrationDataType")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "根据测点id获取振动数据类型下拉", notes = "monitorId、waveId二选一传入")
	public R<List<Map<String, String>>> getVibrationDataType(@ApiParam(value = "测点id") @RequestParam(required = false) Long monitorId,
															 @ApiParam(value = "波形id") @RequestParam(required = false) Long waveId) {
		return R.data(waveFormLogicService.getVibrationDataType(monitorId, waveId));
	}

	/**
	 * 有效值趋势图标注
	 */
	@PostMapping("/waveMark")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "有效值趋势图标注", notes = "传入waveForm")
	public R waveMark(@RequestBody WaveFormVO waveForm) {
		return R.data(waveFormLogicService.waveMark(waveForm.getWaveId(), waveForm.getOriginTime(),waveForm.getShowWave()));
	}

	/**
	 * 取消有效值趋势图标注
	 */
	@PostMapping("/cancelWaveMark")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "取消有效值趋势图标注", notes = "传入waveForm")
	public R cancelWaveMark(@RequestBody CancelWaveMarkVO waveForm) {
		return R.data(waveFormLogicService.cancelWaveMark(waveForm));
	}

	/**
	 * 获取停机线
	 */
	@GetMapping("/getShutdownLine")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "获取停机线")
	public R<BigDecimal> getShutdownLine(@ApiParam(value = "波形id") @RequestParam Long waveId) {
		return R.data(waveFormLogicService.getShutdownLine(waveId));
	}

	/**
	 * 获取能量图谱
	 * @param vo
	 * @return
	 */
	@GetMapping("/queryEnergyList")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "waveId", value = "波形id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "monitorId", value = "测点id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "startTime", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endTime", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "能量图谱", notes = "传入SensorDataVO")
	public R<List<JSONObject>> queryEnergyList(@ApiIgnore WaveFormVO vo) {
		return R.data(waveFormLogicService.queryEnergyList(vo));
	}

	/**
	 * 获取停机线
	 */
	@GetMapping("/getHaltLine")
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "获取停机线", notes = "传入waveId")
	public R<BigDecimal> getHaltLine(@ApiParam(value = "波形id") @RequestParam Long waveId) {
		return R.data(waveFormLogicService.getHaltLine(waveId));
	}

	/**
	 * 设置停机线
	 */
	@PostMapping("/setHaltLine")
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "设置停机线", notes = "传入wave")
	public R setHaltLine(@RequestBody WaveVO wave) {
		return R.status(waveFormLogicService.setHaltLine(wave));
	}

}
