/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.auth.enums.UserEnum;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.core.mp.support.Query;
import com.snszyk.system.dto.UserDTO;
import com.snszyk.system.dto.UserDeptDTO;
import com.snszyk.system.entity.User;
import com.snszyk.system.entity.UserDept;
import com.snszyk.system.entity.UserInfo;
import com.snszyk.system.entity.UserOauth;
import com.snszyk.system.excel.UserExcel;
import com.snszyk.system.vo.UserVO;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IUserService extends BaseService<User> {

	/**
	 * 新增用户
	 *
	 * @param user
	 * @return
	 */
	boolean submit(User user);

	/**
	 * 修改用户
	 *
	 * @param user
	 * @return
	 */
	boolean updateUser(User user);

	/**
	 * 修改用户基本信息
	 *
	 * @param user
	 * @return
	 */
	boolean updateUserInfo(User user);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param user
	 * @param deptId
	 * @param tenantId
	 * @return
	 */
	IPage<User> selectUserPage(IPage<User> page, User user, Long deptId, String tenantId);

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param tenantId
	 * @param deptId
	 * @param userName
	 * @return
	 */
	IPage<UserVO> selectUserPage(IPage<UserVO> page, String tenantId, Long deptId, String userName);

	/**
	 * 自定义分页
	 *
	 * @param user
	 * @param query
	 * @return
	 */
	IPage<UserVO> selectUserSearch(UserVO user, Query query);

	/**
	 * 根据账号获取用户
	 *
	 * @param tenantId
	 * @param account
	 * @return
	 */
	User userByAccount(String tenantId, String account);

	/**
	 * 用户信息
	 *
	 * @param userId
	 * @return
	 */
	UserInfo userInfo(Long userId);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param account
	 * @param password
	 * @return
	 */
	UserInfo userInfo(String tenantId, String account, String password);

	/**
	 * 用户信息
	 *
	 * @param tenantId
	 * @param account
	 * @param password
	 * @param userEnum
	 * @return
	 */
	UserInfo userInfo(String tenantId, String account, String password, UserEnum userEnum);

	/**
	 * 用户信息
	 *
	 * @param userOauth
	 * @return
	 */
	UserInfo userInfo(UserOauth userOauth);

	/**
	 * 给用户设置角色
	 *
	 * @param userIds
	 * @param roleIds
	 * @return
	 */
	boolean grant(String userIds, String roleIds);

	/**
	 * 初始化密码
	 *
	 * @param userIds
	 * @return
	 */
	boolean resetPassword(String userIds);

	/**
	 * 修改密码
	 *
	 * @param userId
	 * @param oldPassword
	 * @param newPassword
	 * @param newPassword1
	 * @return
	 */
	boolean updatePassword(Long userId, String oldPassword, String newPassword, String newPassword1);

	/**
	 * 删除用户
	 *
	 * @param userIds
	 * @return
	 */
	boolean removeUser(String userIds);

	/**
	 * 导入用户数据
	 *
	 * @param data
	 * @param isCovered
	 * @return
	 */
	void importUser(List<UserExcel> data, Boolean isCovered);

	/**
	 * 导出用户数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<UserExcel> exportUser(Wrapper<User> queryWrapper);

	/**
	 * 注册用户
	 *
	 * @param user
	 * @param oauthId
	 * @return
	 */
	boolean registerGuest(User user, Long oauthId);

	/**
	 * 配置用户平台
	 *
	 * @param userId
	 * @param userType
	 * @param userExt
	 * @return
	 */
	boolean updatePlatform(Long userId, Integer userType, String userExt);

	/**
	 * 用户详细信息
	 *
	 * @param user
	 * @return
	 */
	UserVO platformDetail(User user);

	List<UserDept> queryUserDeptListByCondition(String userName);

	List<UserDTO> queryUserDeptListByIds(List<Long> idList);

	List<UserDTO> queryUserDeptListByDeptIds(List<Long> deptIdList);

	List<UserDTO> queryUserDeptListByPostIds(List<Long> postIdList);

	List<UserDTO> queryUserDeptListByRoleIds(List<Long> roleIdList);

	UserDTO queryUserById(Long userId);

	UserDeptDTO queryUserDept(Long userId, Long deptId);

	List<String> roleKeyList(Long userId, Long deptId);

	/**
	 * 更新用户状态
	 *
	 * @param userId   用户ID
	 * @param status   状态：0-禁用，1-启用
	 * @param tenantId 租户ID（仅平台管理员可指定）
	 * @return 操作结果
	 */
	Boolean updateUserStatus(Long userId, Integer status, String tenantId);
}
