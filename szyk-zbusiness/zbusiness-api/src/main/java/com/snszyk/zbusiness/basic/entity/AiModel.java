/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI模型表实体类
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
@TableName("eolm_ai_model")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AiModel对象", description = "AI模型表")
public class AiModel extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 模型名称
	 */
	@ApiModelProperty(value = "模型名称")
	private String name;
	/**
	 * 设备主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备主键")
	private Long equipmentId;
	/**
	 * 描述
	 */
	@ApiModelProperty(value = "描述")
	private String description;
	/**
	 * 参数个数
	 */
	@ApiModelProperty(value = "参数个数")
	private Integer params;
	/**
	 * 返回值字典
	 */
	@ApiModelProperty(value = "返回值字典")
	private String dict;
	/**
	 * 版本号
	 */
	@ApiModelProperty(value = "版本号")
	private String version;
	/**
	 * 是否预处理（0:否  1:是）
	 */
	@ApiModelProperty(value = "是否预处理（0:否  1:是）")
	private Integer isPretreat;


}
