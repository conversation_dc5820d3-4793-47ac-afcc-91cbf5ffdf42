/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.basic.dto.DeviceCollectionStationDTO;
import com.snszyk.zbusiness.basic.entity.DeviceCollectionStation;

import java.util.List;

/**
 * 厂区采集站绑定表 服务类
 *
 * <AUTHOR>
 */
public interface IDeviceCollectionStationService extends BaseService<DeviceCollectionStation> {


	/**
	 * 删除厂区绑定的采集站
	 *
	 * @param deviceId 厂区id
	 * @return
	 */
	int deleteByDeviceId(Long deviceId);

	/**
	 * 查询厂区绑定的采集站列表
	 *
	 * @param deviceId 厂区id
	 * @return
	 */
	List<DeviceCollectionStationDTO> listCollectionStation(Long deviceId);

}
