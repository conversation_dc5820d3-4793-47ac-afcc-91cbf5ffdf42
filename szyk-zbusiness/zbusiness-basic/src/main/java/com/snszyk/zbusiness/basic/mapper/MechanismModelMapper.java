/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.MechanismModelDTO;
import com.snszyk.zbusiness.basic.entity.MechanismModel;
import com.snszyk.zbusiness.basic.vo.MechanismModelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 机理模型 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
public interface MechanismModelMapper extends BaseMapper<MechanismModel> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param mechanismModel
	 * @return
	 */
	List<MechanismModelDTO> page(IPage page, @Param("mechanismModel") MechanismModelVO mechanismModel);

}
