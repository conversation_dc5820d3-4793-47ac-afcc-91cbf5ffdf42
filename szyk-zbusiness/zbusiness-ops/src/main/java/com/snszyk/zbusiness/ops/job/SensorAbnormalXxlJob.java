package com.snszyk.zbusiness.ops.job;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.StringUtil;
import com.snszyk.message.entity.MessageSetting;
import com.snszyk.message.enums.*;
import com.snszyk.message.service.IMessageSettingService;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * 传感器异常定时任务
 * 每30分钟执行一次（整点和半点），检查传感器状态并发送异常通知
 * xxl-job配置：0 0,30 * * * ?
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SensorAbnormalXxlJob {

	/**
	 * 消息标题
	 */
	private static final String MESSAGE_TITLE = "传感器异常";

	/**
	 * Redis缓存键前缀
	 */
	private static final String CACHE_KEY_PREFIX = "sensor_abnormal:executed:";

	/**
	 * 缓存过期时间：25小时（秒）
	 */
	private static final long CACHE_EXPIRE_SECONDS = 25 * 60 * 60;

	/**
	 * 异常信息固定内容
	 */
	private static final String ABNORMAL_MESSAGE = "传感器数据传输监测异常，请及时查看";

	private final ISensorInstanceService sensorInstanceService;
	private final IMessageSettingService messageSettingService;
	private final MessageLogicService messageLogicService;
	private final SzykRedis szykRedis;

	/**
	 * 传感器异常定时任务
	 * 每30分钟执行一次（整点和半点），检查是否有需要发送的消息
	 * xxl-job配置：0 0,30 * * * ?
	 *
	 * @param param 参数，可以为空
	 * @return 执行结果
	 */
	@XxlJob("sensorAbnormalJobHandler")
	public ReturnT<String> sensorAbnormalJobHandler(String param) {
		log.info("传感器异常定时任务开始执行，参数: {}", param);

		try {
			LocalDateTime now = LocalDateTime.now();
			int totalSentCount = 0;

			// 查询所有启用的传感器异常消息设置
			List<MessageSetting> messageSettings = messageSettingService.list(Wrappers.<MessageSetting>query().lambda()
				.eq(MessageSetting::getBizType, MessageBizTypeEnum.SENSOR_ABNORMAL.getCode())
				.eq(MessageSetting::getEnabled, SzykConstant.DB_STATUS_NORMAL));

			log.info("找到{}个启用的传感器异常消息设置", messageSettings.size());

			for (MessageSetting messageSetting : messageSettings) {
				String tenantId = messageSetting.getTenantId();
				String receiverType = messageSetting.getReceiverType();
				String receiverInfo = messageSetting.getReceiverInfo();

				try {
					// 检查是否应该发送消息
					if (!shouldSendMessage(tenantId, messageSetting, now)) {
						continue;
					}

					log.info("开始为租户[{}]统计传感器异常数据", tenantId);

					// 统计传感器异常数据
					SensorAbnormalStatistics statistics = buildSensorAbnormalStatistics(tenantId);

					if (statistics.getAbnormalCount() == 0) {
						log.info("租户[{}]当前无传感器异常，跳过发送", tenantId);
						// 即使没有异常也要标记已执行，避免重复检查
						markAsExecuted(tenantId);
						continue;
					}

					// 发送消息
					boolean sendResult = sendSensorAbnormalMessage(tenantId, statistics, receiverType, receiverInfo);
					if (sendResult) {
						// 标记今天已执行，防止重复发送
						markAsExecuted(tenantId);
						totalSentCount += 1;
					}

				} catch (Exception e) {
					log.error("处理租户[{}]的传感器异常消息异常", tenantId, e);
				}
			}

			String result = String.format("传感器异常定时任务执行完成，共发送%d条消息", totalSentCount);
			log.info(result);
			return ReturnT.SUCCESS;

		} catch (Exception e) {
			log.error("传感器异常定时任务执行异常", e);
			return new ReturnT<>(ReturnT.FAIL_CODE, "执行失败: " + e.getMessage());
		}
	}

	/**
	 * 检查是否应该发送消息
	 * 支持整点和半点的精确匹配，防止重复发送
	 *
	 * @param tenantId       租户ID
	 * @param messageSetting 消息设置
	 * @param now            当前时间
	 * @return 是否应该发送消息
	 */
	private boolean shouldSendMessage(String tenantId, MessageSetting messageSetting, LocalDateTime now) {
		String sendStrategy = messageSetting.getSendStrategy();

		// 检查今天是否已经发送过
		if (hasExecutedToday(tenantId)) {
			return false;
		}

		// 如果是固定时间发送，精确匹配整点或半点
		if (SendStrategyEnum.FIXED_TIME.getCode().equals(sendStrategy)) {
			LocalTime fixedSendTime = messageSetting.getFixedSendTime();
			if (fixedSendTime == null) {
				log.warn("租户[{}]固定发送时间为空，跳过发送", tenantId);
				return false;
			}

			// 获取当前时间和目标时间
			int currentHour = now.getHour();
			int currentMinute = now.getMinute();
			int targetHour = fixedSendTime.getHour();
			int targetMinute = fixedSendTime.getMinute();

			// 只在整点(0分)或半点(30分)执行
			if (currentMinute != 0 && currentMinute != 30) {
				return false;
			}

			// 精确匹配用户设置的时间
			return currentHour == targetHour && currentMinute == targetMinute;
		}

		return false;
	}

	/**
	 * 构建传感器异常统计信息
	 *
	 * @param tenantId 租户ID
	 * @return 传感器异常统计信息
	 */
	private SensorAbnormalStatistics buildSensorAbnormalStatistics(String tenantId) {
		try {
			SensorAbnormalStatistics statistics = new SensorAbnormalStatistics();
			int abnormalCount = 0;

			// 查询该租户下所有传感器实例
			List<SensorInstance> sensorInstances = sensorInstanceService.list(Wrappers.<SensorInstance>query().lambda()
				.eq(SensorInstance::getTenantId, tenantId)
				.isNotNull(SensorInstance::getCode));

			log.info("租户[{}]共有{}个传感器实例", tenantId, sensorInstances.size());

			// 检查每个传感器的状态，统计异常数量
			for (SensorInstance sensor : sensorInstances) {
				String sensorCode = sensor.getCode();
				if (StringUtil.isBlank(sensorCode)) {
					continue;
				}

				// 检查传感器状态：status不为0时表示异常
				Integer status = sensor.getStatus();
				if (status != null && status != 0) {
					abnormalCount++;
				}
			}

			// 设置异常信息（固定内容）
			statistics.setAbnormalMessage(ABNORMAL_MESSAGE);

			// 构建汇总信息（固定模板）
			String currentTime = DateUtil.format(new Date(), "yyyy/MM/dd HH:mm:ss");
			String summaryMessage = String.format("截止当前%s，共有%d个传感器数据传输异常",
				currentTime, abnormalCount);
			statistics.setSummaryMessage(summaryMessage);

			// 设置异常数量
			statistics.setAbnormalCount(abnormalCount);

			log.info("租户[{}]传感器异常统计完成，异常传感器数量: {}", tenantId, abnormalCount);
			return statistics;

		} catch (Exception e) {
			log.error("构建传感器异常统计信息异常，租户ID: {}", tenantId, e);
			return new SensorAbnormalStatistics();
		}
	}

	/**
	 * 发送传感器异常消息
	 *
	 * @param tenantId     租户ID
	 * @param statistics   传感器异常统计信息
	 * @param receiverType 接收人类型
	 * @param receiverInfo 接收人信息
	 * @return 发送结果
	 */
	private boolean sendSensorAbnormalMessage(String tenantId, SensorAbnormalStatistics statistics,
											  String receiverType, String receiverInfo) {
		try {
			// 构建消息内容
			String content = JSON.toJSONString(statistics);

			// 构建消息对象
			MessageVo messageVo = new MessageVo();
			messageVo.setAppKey("SiDAs");
			messageVo.setTitle(MESSAGE_TITLE);
			messageVo.setContent(content);
			messageVo.setType(MessageTypeEnum.SYSTEM.getCode()); // 系统消息
			messageVo.setSender(MessageTypeEnum.SYSTEM.getCode()); // 系统发送
			messageVo.setIsImmediate(YesNoEnum.YES.getCode()); // 立即发送
			messageVo.setBizType(MessageBizTypeEnum.SENSOR_ABNORMAL.getCode());
			messageVo.setScheduleTime(DateUtil.now());
			messageVo.setChannel(MessageChannelEnum.MINI_PROGRAM.getCode());

			// 设置接收人信息 - 让messageLogicService自己解析
			messageVo.setReceiverType(receiverType);
			messageVo.setReceiverInfo(receiverInfo);
			messageVo.setReceiverInfoVo(JSON.parseObject(receiverInfo, ReceiverInfoVo.class));

			// 发送消息
			R<String> result = messageLogicService.commitMessage(messageVo);
			boolean success = result.isSuccess();

			if (success) {
				log.info("租户[{}]传感器异常消息发送成功，异常传感器数量: {}", tenantId, statistics.getAbnormalCount());
			} else {
				log.error("租户[{}]传感器异常消息发送失败: {}", tenantId, result.getMsg());
			}

			return success;

		} catch (Exception e) {
			log.error("发送传感器异常消息异常，租户ID: {}", tenantId, e);
			return false;
		}
	}

	/**
	 * 检查今天是否已经执行过
	 *
	 * @param tenantId 租户ID
	 * @return 是否已执行
	 */
	private boolean hasExecutedToday(String tenantId) {
		try {
			String today = DateUtil.format(new Date(), "yyyy-MM-dd");
			String cacheKey = CACHE_KEY_PREFIX + tenantId + ":" + today;
			return StringUtil.isNotBlank(szykRedis.get(cacheKey));
		} catch (Exception e) {
			log.error("检查执行状态异常，租户ID: {}", tenantId, e);
			return false;
		}
	}

	/**
	 * 标记今天已执行
	 *
	 * @param tenantId 租户ID
	 */
	private void markAsExecuted(String tenantId) {
		try {
			String today = DateUtil.format(new Date(), "yyyy-MM-dd");
			String cacheKey = CACHE_KEY_PREFIX + tenantId + ":" + today;
			// 缓存25小时过期
			szykRedis.setEx(cacheKey, "executed", CACHE_EXPIRE_SECONDS);
			log.info("租户[{}]今日传感器异常通知已标记为已执行", tenantId);
		} catch (Exception e) {
			log.error("标记执行状态异常，租户ID: {}", tenantId, e);
		}
	}

	/**
	 * 传感器异常统计信息内部类
	 * 简化版本，仅包含基本字段
	 */
	@Data
	private static class SensorAbnormalStatistics {
		/**
		 * 异常信息（固定内容）
		 */
		private String abnormalMessage;

		/**
		 * 汇总信息（固定模板：截止当前时间，共有X个传感器数据传输异常）
		 */
		private String summaryMessage;

		/**
		 * 异常传感器数量
		 */
		private Integer abnormalCount;
	}
}
