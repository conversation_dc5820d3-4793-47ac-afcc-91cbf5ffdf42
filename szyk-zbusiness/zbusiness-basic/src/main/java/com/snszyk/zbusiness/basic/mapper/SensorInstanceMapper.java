/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.EquipmentSensorDTO;
import com.snszyk.zbusiness.basic.dto.EqumentSensorListDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceBindExcelDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import com.snszyk.zbusiness.basic.vo.EqumentSensorListVO;
import com.snszyk.zbusiness.basic.vo.SensorInstanceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 传感器实例表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface SensorInstanceMapper extends BaseMapper<SensorInstance> {


	/**
	 * 自定义分页
	 * @param page page
	 * @param vo vo
	 * @return
	 */
	List<SensorInstanceDTO> page(IPage<SensorInstanceDTO> page, @Param("vo") SensorInstanceVO vo);

	/**
	 * 根据测点id查询传感器实例
	 * @param monitorId 测点id
	 * @return
	 */
	List<SensorInstanceDTO> listByMonitorId(@Param("monitorId") Long monitorId);

	/**
	 * 根据传感器编码查询 绑定到指定设备的同类型传感器编码列表
	 * @param sensorCode 传感器编码
	 * @param equipmentIdList 设备id列表
	 * @return
	 */
	List<SensorInstance> getSameTypeSensorCodeList(@Param("code") String sensorCode,
												   @Param("list") List<Long> equipmentIdList);

	/**
	 * 根据设备id解绑传感器实例
	 * @param equipmentId 设备id
	 * @return
	 */
	Integer unbindByEquipment(@Param("equipmentId") Long equipmentId);

	/**
	 * 根据测点id解绑传感器实例
	 * @param monitorIdList 测点id列表
	 * @return
	 */
	int unbindByMonitor(@Param("list") List<Long> monitorIdList);

	/**
	 * 根据测点id查询测点下所有传感器实例
	 * @param monitorIdList 测点id
	 * @return
	 */
	List<SensorInstanceDTO> querySensorInfos(@Param("list") List<Long> monitorIdList);

	/**
	 * 绑定采集站
	 * @param sensorCodeList 传感器编码列表
	 * @param stationId 采集站id
	 * @return
	 */
	int bindCollectionStation(@Param("list") List<String> sensorCodeList, @Param("stationId") Long stationId);

	/**
	 * 解绑采集站
	 * @param stationId 采集站id
	 * @return
	 */
	int unbindCollectionStation(@Param("list") List<Long> stationId);

	/**
	 * 根据传感器编码查询传感器实例
	 * @param code 传感器编码
	 * @return
	 */
	SensorInstanceDTO detailByCode(@Param("code") String code);

	/**
	 * 根据传感器实例id解绑传感器实例
	 * @param sensorInstanceId 传感器实例id
	 * @return
	 */
	int unbindBySensorInstance(@Param("sensorInstanceId") Long sensorInstanceId);

	/**
	 * 门户端-传感器工作状态概览
	 * @param page page
	 * @param vo vo
	 * @return
	 */
	List<SensorInstanceDTO> portalPage(IPage<SensorInstanceDTO> page, @Param("vo") SensorInstanceVO vo);

	/**
	 * 导出列表excel
	 *
	 * @return
	 */
	List<SensorInstanceDTO> listExport();

	/**
	 * 导出传感器实例导入模板数据
	 */
	List<SensorInstanceBindExcelDTO> listSensorInstanceBindExport();

	/**
	 * 根据传感器编码删除报警门限
	 *
	 * @param sensorCode
	 * @return
	 */
	int removeAlarmThresholdBySensorCode(@Param("sensorCode") String sensorCode);

	/**
	 * 查询地点下所有设备的传感器信息
	 * @param vo
	 * @return
	 */
	List<EqumentSensorListDTO> equmentSensorList(EqumentSensorListVO vo);

	/**
	 * 根据设备ID查询所有传感器信息
	 * @param equipmentId
	 * @return
	 */
	List<EquipmentSensorDTO> equipmentSensor(@Param("equipmentId") Long equipmentId);
}
