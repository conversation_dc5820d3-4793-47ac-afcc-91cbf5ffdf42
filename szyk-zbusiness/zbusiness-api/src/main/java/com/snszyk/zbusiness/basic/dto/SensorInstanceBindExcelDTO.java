package com.snszyk.zbusiness.basic.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.snszyk.zbusiness.basic.converter.InstallDirectionConverter;
import com.snszyk.zbusiness.basic.converter.SensorCategoryConverter;
import com.snszyk.zbusiness.basic.converter.SensorSupplierConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 传感器实例导入excel模板
 *
 * <AUTHOR>
 */
@Data
@ColumnWidth(25)
public class SensorInstanceBindExcelDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 序号
	 */
	@ColumnWidth(10)
	@ExcelProperty("序号")
	@ApiModelProperty(value = "序号")
	private Integer serialNumber;

	/**
	 * 路径
	 */
	@ColumnWidth(30)
	@ExcelProperty("路径")
	@ApiModelProperty(value = "路径")
	private String pathName;

	/**
	 * 设备名称
	 */
	@ColumnWidth(20)
	@ExcelProperty("设备名称")
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 部位
	 */
	@ColumnWidth(20)
	@ExcelProperty("部位")
	@ApiModelProperty(value = "部位")
	private String monitorName;

	/**
	 * 传感器编码
	 */
	@ExcelProperty("传感器编码")
	@ApiModelProperty(value = "传感器编码")
	@ContentStyle(dataFormat = 49)
	private String sensorCode;

	/**
	 * 传感器类型
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "传感器类型", converter = SensorCategoryConverter.class)
	@ApiModelProperty(value = "传感器类型（字典：sensor_category）")
	private Integer category;

	/**
	 * 安装方向：0-轴向；1-水平；2-垂直。仅温振一体传感器
	 */
	@ColumnWidth(30)
	@ApiModelProperty(value = "安装方向(温振&应力波)")
	@ExcelProperty(value = "安装方向(温振&应力波)", converter = InstallDirectionConverter.class)
	private Integer installDirection;

	/**
	 * 编号，仅应力波传感器
	 */
	@ColumnWidth(25)
	@ExcelProperty("编号(应力波)")
	@ApiModelProperty(value = "编号")
	@ContentStyle(dataFormat = 49)
	private String number;

	/**
	 * 相位（A、B、C），仅电流传感器
	 */
	@ColumnWidth(20)
	@ExcelProperty("相位(电流)")
	@ApiModelProperty(value = "相位（A、B、C），仅电流传感器")
	private String phase;

	/**
	 * 传感器名称
	 */
	@ExcelProperty("传感器名称")
	@ApiModelProperty(value = "传感器名称")
	private String sensorTypeName;

	/**
	 * 传感器厂家
	 */
	@ColumnWidth(20)
	@ExcelProperty(value = "传感器厂家", converter = SensorSupplierConverter.class)
	@ApiModelProperty(value = "传感器厂家")
	private String supplier;

	/**
	 * 传感器型号
	 */
	@ColumnWidth(20)
	@ExcelProperty("传感器型号")
	@ApiModelProperty(value = "传感器型号")
	@ContentStyle(dataFormat = 49)
	private String model;

	/**
	 * 单值采样时间间隔(S)
	 */
	@ColumnWidth(25)
	@ExcelProperty("单值采样间隔(S)")
	@ApiModelProperty(value = "单值采样间隔(S)")
	private Integer singleSampleInterval;

	/**
	 * 波形采样时间间隔(S)
	 */
	@ColumnWidth(25)
	@ExcelProperty("波形采样间隔(S)")
	@ApiModelProperty(value = "波形采样间隔(S)")
	private Integer waveSampleInterval;

}
