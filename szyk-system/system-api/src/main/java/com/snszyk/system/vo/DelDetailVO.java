package com.snszyk.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 基础信息校验结果封装VO
 * <AUTHOR>
 * @Date 2022/09/14 10:38
 */
@Data
@AllArgsConstructor
@ApiModel(value = "DelDetailVO对象", description = "DelDetailVO对象")
public class DelDetailVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "删除条目名称")
	private String item;

	@ApiModelProperty(value = "删除结果，true：成功，false：失败")
	private Boolean success;

	@ApiModelProperty(value = "提示信息")
	private String message;
}
