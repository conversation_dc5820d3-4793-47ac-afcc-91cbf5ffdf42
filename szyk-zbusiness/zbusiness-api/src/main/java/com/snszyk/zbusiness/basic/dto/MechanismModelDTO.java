/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.MechanismModel;
import com.snszyk.zbusiness.basic.vo.MechanismModelParamVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 机理模型数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MechanismModelDTO extends MechanismModel {
	private static final long serialVersionUID = 1L;

	/**
	 * 应用设备类型
	 */
	@ApiModelProperty(value = "应用设备类型名称")
	private String applyEquipmentName;

	/**
	 * 应用功率范围名称
	 */
	@ApiModelProperty(value = "应用功率范围名称")
	private String applyPowerName;

	/**
	 * 应用数据类型名称
	 */
	@ApiModelProperty(value = "应用数据类型名称")
	private String applyDataName;

	/**
	 * 阈值参数名称
	 */
	@ApiModelProperty(value = "机理类型名称")
	private String typeName;

	/**
	 * 机理模型参数集合
	 */
	@ApiModelProperty(value = "机理模型参数集合")
	private List<MechanismModelParamVO> modelParamList;


}
