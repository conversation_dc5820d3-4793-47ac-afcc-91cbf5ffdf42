/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.snszyk.zbusiness.ops.entity.Alarm;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 报警管理信息表视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AlarmVO对象", description = "报警管理信息表")
public class AlarmVO extends Alarm {
	private static final long serialVersionUID = 1L;

	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String startDate;

	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String endDate;

	/**
	 * 报警值
	 */
	@ApiModelProperty(value = "报警值")
	private BigDecimal alarmVal;

	/**
	 * 报警值单位
	 */
	@ApiModelProperty(value = "报警值单位")
	private String unit;

}
