/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 机理模型参数表实体类
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
@Data
@Accessors(chain = true)
@TableName("eolm_mechanism_model_param")
@ApiModel(value = "MechanismModelParam对象", description = "机理模型参数表")
public class MechanismModelParam implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 机理模型id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "机理模型id")
	private Long modelId;
	/**
	 * 机理参数（字典：model_param_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "机理参数（字典：model_param_type）")
	private Integer paramType;
	/**
	 * 参数信息
	 */
	@ApiModelProperty(value = "参数信息")
	private String paramInfo;

}
