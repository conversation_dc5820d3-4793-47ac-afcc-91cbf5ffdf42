/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备测点参数配置表实体类
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@Accessors(chain = true)
@TableName("eolm_monitor_param")
@ApiModel(value = "MonitorParam对象", description = "设备测点参数配置表")
public class MonitorParam implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;
	/**
	 * 转速信息
	 */
	@ApiModelProperty(value = "转速信息")
	private String rpmInfo;
	/**
	 * 叶片信息
	 */
	@ApiModelProperty(value = "叶片信息")
	private String bladeInfo;
	/**
	 * 轴承id列表
	 */
	@ApiModelProperty(value = "轴承id列表")
	private String bearingId;
	/**
	 * 轴承信息
	 */
	@ApiModelProperty(value = "轴承信息")
	private String bearingInfo;
	/**
	 * 齿轮信息
	 */
	@ApiModelProperty(value = "齿轮信息")
	private String gearInfo;

}
