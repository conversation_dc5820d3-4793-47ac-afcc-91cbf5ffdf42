/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.logic;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.MessageTypeEnum;
import com.snszyk.message.enums.ReceiverTypeEnum;
import com.snszyk.message.enums.YesNoEnum;
import com.snszyk.message.service.logic.MessageLogicService;
import com.snszyk.message.vo.DingTalkMessageVo;
import com.snszyk.message.vo.MessageVo;
import com.snszyk.message.vo.ReceiverInfoVo;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.entity.User;
import com.snszyk.system.service.IRoleService;
import com.snszyk.system.service.IUserSearchService;
import com.snszyk.system.vo.RoleVO;
import com.snszyk.zbusiness.basic.dto.EquipmentDTO;
import com.snszyk.zbusiness.basic.dto.IntelligentDiagnosisResult;
import com.snszyk.zbusiness.basic.dto.SensorDataDTO;
import com.snszyk.zbusiness.basic.dto.WaveDTO;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.enums.AlarmIndexEnum;
import com.snszyk.zbusiness.basic.enums.ModelTypeEnum;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.service.IWaveService;
import com.snszyk.zbusiness.basic.service.logic.EquipmentSpotCheckConfigLogicService;
import com.snszyk.zbusiness.basic.vo.ContinuousStrategyVO;
import com.snszyk.zbusiness.basic.vo.DiscontinuousStrategyVO;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import com.snszyk.zbusiness.ops.dto.AbnormalDTO;
import com.snszyk.zbusiness.ops.entity.Abnormal;
import com.snszyk.zbusiness.ops.entity.AbnormalDetail;
import com.snszyk.zbusiness.ops.entity.AbnormalRecord;
import com.snszyk.zbusiness.ops.entity.AlarmRecord;
import com.snszyk.zbusiness.ops.enums.*;
import com.snszyk.zbusiness.ops.service.IAbnormalDetailService;
import com.snszyk.zbusiness.ops.service.IAbnormalRecordService;
import com.snszyk.zbusiness.ops.service.IAbnormalService;
import com.snszyk.zbusiness.ops.service.IAlarmRecordService;
import com.snszyk.zbusiness.ops.vo.*;
import com.snszyk.zbusiness.ops.wrapper.AbnormalWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备异常信息表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-21
 */
@Slf4j
@AllArgsConstructor
@Service
public class AbnormalLogicService {

	// 连续策略缓存KEY
	private static final String KEY_ABNORMAL_CONTINUOUS_STRATEGY = "config:abnormal:continuous:strategy";
	// 非连续策略缓存KEY
	private static final String KEY_ABNORMAL_DISCONTINUOUS_STRATEGY = "config:abnormal:discontinuous:strategy";
	// 异常原因数组
	private static final String[] KEY_ALARM_HANDLER = {"threshold", "mechanism", "ai"};
	// 报警为2
	private static final Integer ALARM_FLAG = 2;
	// 连续策略
	private static ContinuousStrategyVO continuousStrategy;
	// 非连续策略
	private static DiscontinuousStrategyVO discontinuousStrategy;
	// 波形
	private static WaveDTO waveDTO;
	private final IAlarmRecordService alarmRecordService;
	private final IAbnormalService abnormalService;
	private final IAbnormalDetailService abnormalDetailService;
	private final IAbnormalRecordService abnormalRecordService;
	private final InfluxdbTools influxdbTools;
	private final SzykRedis szykRedis;
	private final RabbitTemplate rabbitTemplate;
	private final IRoleService roleService;
	private final MessageLogicService messageLogicService;
	private final IUserSearchService userSearchService;
	private final IEquipmentService equipmentService;
	private final IWaveService waveService;
	private final EquipmentSpotCheckConfigLogicService equipmentSpotCheckConfigLogicService;

	/**
	 * 初始化配置信息
	 *
	 * @param tenantId
	 * @return void
	 * <AUTHOR>
	 * @date 2024/5/22 15:16
	 */
	public void initConfig(String tenantId) {
		String jsonStr = szykRedis.get(tenantId.concat(":").concat(KEY_ABNORMAL_CONTINUOUS_STRATEGY));
		if (Func.isNotEmpty(jsonStr)) {
			continuousStrategy = JSONUtil.toBean(jsonStr, ContinuousStrategyVO.class);
		}
		jsonStr = szykRedis.get(tenantId.concat(":").concat(KEY_ABNORMAL_DISCONTINUOUS_STRATEGY));
		if (Func.isNotEmpty(jsonStr)) {
			discontinuousStrategy = JSONUtil.toBean(jsonStr, DiscontinuousStrategyVO.class);
		}
	}

	/**
	 * 二次确认
	 *
	 * @param waveId
	 * @param originTime
	 * @return void
	 * <AUTHOR>
	 * @date 2024/5/22 15:20
	 */
	public void alarmDataHandler(String tenantId, Long waveId, Date originTime) {

		waveDTO = waveService.getBy(waveId);
		Abnormal abnormal = abnormalService.getOne(Wrappers.<Abnormal>query().lambda()
			.eq(Abnormal::getEquipmentId, waveDTO.getEquipmentId()).eq(Abnormal::getStatus, AbnormalStatusEnum.IS_FAULT.getCode()));
		if (Func.isNotEmpty(abnormal)) {
			return;
		}
		log.info("二次确认生成异常业务逻辑开始：====================={}，{}", waveId, DateUtil.format(originTime, DateUtil.PATTERN_DATETIME));
		this.initConfig(tenantId);
		Integer thresholdAlarm = null;
		Integer mechanismAlarm = null;
		Integer aiAlarm = null;
		log.info("设置REDIS标志位：=====================波形：{}，时间：{}", waveId, originTime.getTime());
		if (Func.isNotEmpty((Object) szykRedis.get(waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[0]))) {
			thresholdAlarm = Func.toInt(szykRedis.get(waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[0]));
			log.info("二次确认——门限是否报警：====================={}", thresholdAlarm);
		}
		if (Func.isNotEmpty((Object) szykRedis.get(waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[1]))) {
			mechanismAlarm = Func.toInt(szykRedis.get(waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[1]));
			log.info("二次确认——机理是否报警：====================={}", mechanismAlarm);
		}
		if (Func.isNotEmpty((Object) szykRedis.get(waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[2]))) {
			aiAlarm = Func.toInt(szykRedis.get(waveId + StringPool.COLON + originTime.getTime() + StringPool.COLON + KEY_ALARM_HANDLER[2]));
			log.info("二次确认——AI是否报警：====================={}", aiAlarm);
		}
		Integer thresholdWeight = 0;
		Integer mechanismWeight = 0;
		Integer aiWeight = 0;
		List<AbnormalDetailVO> detailList = new ArrayList<>();
		// 连续策略
		AbnormalDetailVO thresholdDetail = new AbnormalDetailVO(AlarmBizTypeEnum.THRESHOLD.getCode(), 1);
		AbnormalDetailVO mechanismDetail = new AbnormalDetailVO(AlarmBizTypeEnum.MECHANISM.getCode(), 1);
		AbnormalDetailVO aiDetail = new AbnormalDetailVO(AlarmBizTypeEnum.INTELLIGENCE.getCode(), 1);
		log.info("二次确认——连续策略配置：====================={}", continuousStrategy);
		if (Func.isNotEmpty(continuousStrategy)) {
			if (Func.equals(Func.toInt(StringPool.ONE), continuousStrategy.getAlarmThreshold().getIsEnabled())) {
				if (thresholdAlarm != null && Func.equals(ALARM_FLAG, thresholdAlarm)) {
					AbnormalRecordVO abnormalRecord = this.continuousThresholdWeight(waveId, originTime);
					if (Func.isNotEmpty(abnormalRecord)) {
						thresholdWeight = abnormalRecord.getWeight();
						thresholdDetail.setAbnormalLevel(abnormalRecord.getAbnormalLevel());
						thresholdDetail.setAbnormalRecordList(Arrays.asList(abnormalRecord));
						log.info("二次确认——连续策略thresholdWeight：====================={}", thresholdWeight);
					}
				}
			}
			if (Func.equals(Func.toInt(StringPool.ONE), continuousStrategy.getMechanismModel().getIsEnabled())) {
				if (mechanismAlarm != null && Func.equals(ALARM_FLAG, mechanismAlarm)) {
					List<AbnormalRecordVO> recordList = this.continuousMechanismWeight(waveId, originTime);
					if (recordList != null && recordList.size() > 0) {
						mechanismWeight = recordList.get(0).getWeight();
						mechanismDetail.setAbnormalRecordList(recordList);
						Integer abnormalLevel = AlarmLevelEnum.NORMAL.getCode();
						for (AbnormalRecordVO abnormalRecord : recordList) {
							if (abnormalRecord.getAbnormalLevel() > abnormalLevel) {
								abnormalLevel = abnormalRecord.getAbnormalLevel();
							}
						}
						mechanismDetail.setAbnormalLevel(abnormalLevel);
						log.info("二次确认——连续策略mechanismWeight：====================={}", mechanismWeight);
					}
				}
			}
			if (Func.equals(Func.toInt(StringPool.ONE), continuousStrategy.getAiModel().getIsEnabled())) {
				if (aiAlarm != null && Func.equals(ALARM_FLAG, aiAlarm)) {
					AbnormalRecordVO abnormalRecord = this.continuousAiWeight(waveId, originTime);
					if (Func.isNotEmpty(abnormalRecord)) {
						aiWeight = abnormalRecord.getWeight();
						aiDetail.setAbnormalLevel(abnormalRecord.getAbnormalLevel());
						aiDetail.setAbnormalRecordList(Arrays.asList(abnormalRecord));
						log.info("二次确认——连续策略aiWeight：====================={}", aiWeight);
					}
				}
			}
			Integer weightSum = thresholdWeight + mechanismWeight + aiWeight;
			// 生成异常明细
			if (weightSum >= continuousStrategy.getWeightSum()) {
				if (thresholdWeight > 0) {
					detailList.add(thresholdDetail);
				}
				if (mechanismWeight > 0) {
					detailList.add(mechanismDetail);
				}
				if (aiWeight > 0) {
					detailList.add(aiDetail);
				}
			}
		}
		thresholdWeight = 0;
		mechanismWeight = 0;
		aiWeight = 0;
		// 非连续策略
		thresholdDetail = new AbnormalDetailVO(AlarmBizTypeEnum.THRESHOLD.getCode(), 2);
		mechanismDetail = new AbnormalDetailVO(AlarmBizTypeEnum.MECHANISM.getCode(), 2);
		aiDetail = new AbnormalDetailVO(AlarmBizTypeEnum.INTELLIGENCE.getCode(), 2);
		log.info("二次确认——非连续策略配置：====================={}", discontinuousStrategy);
		if (Func.isNotEmpty(discontinuousStrategy)) {
			if (Func.equals(Func.toInt(StringPool.ONE), discontinuousStrategy.getAlarmThreshold().getIsEnabled())) {
				if (thresholdAlarm != null && Func.equals(ALARM_FLAG, thresholdAlarm)) {
					AbnormalRecordVO abnormalRecord = this.discontinuousThresholdWeight(waveId, originTime);
					if (Func.isNotEmpty(abnormalRecord)) {
						thresholdWeight = abnormalRecord.getWeight();
						thresholdDetail.setAbnormalLevel(abnormalRecord.getAbnormalLevel());
						thresholdDetail.setAbnormalRecordList(Arrays.asList(abnormalRecord));
						log.info("二次确认——非连续策略thresholdWeight：====================={}", thresholdWeight);
					}
				}
			}
			if (Func.equals(Func.toInt(StringPool.ONE), discontinuousStrategy.getMechanismModel().getIsEnabled())) {
				if (mechanismAlarm != null && Func.equals(ALARM_FLAG, mechanismAlarm)) {
					List<AbnormalRecordVO> recordList = this.discontinuousMechanismWeight(waveId, originTime);
					if (recordList != null && recordList.size() > 0) {
						mechanismWeight = recordList.get(0).getWeight();
						mechanismDetail.setAbnormalRecordList(recordList);
						Integer abnormalLevel = AlarmLevelEnum.NORMAL.getCode();
						for (AbnormalRecordVO abnormalRecord : recordList) {
							if (abnormalRecord.getAbnormalLevel() > abnormalLevel) {
								abnormalLevel = abnormalRecord.getAbnormalLevel();
							}
						}
						mechanismDetail.setAbnormalLevel(abnormalLevel);
						log.info("二次确认——非连续策略mechanismWeight：====================={}", mechanismWeight);
					}
				}
			}
			if (Func.equals(Func.toInt(StringPool.ONE), discontinuousStrategy.getAiModel().getIsEnabled())) {
				if (aiAlarm != null && Func.equals(ALARM_FLAG, aiAlarm)) {
					AbnormalRecordVO abnormalRecord = this.discontinuousAiWeight(waveId, originTime);
					if (Func.isNotEmpty(abnormalRecord)) {
						aiWeight = abnormalRecord.getWeight();
						aiDetail.setAbnormalLevel(abnormalRecord.getAbnormalLevel());
						aiDetail.setAbnormalRecordList(Arrays.asList(abnormalRecord));
						log.info("二次确认——非连续策略aiWeight：====================={}", aiWeight);
					}
				}
			}
			Integer weightSum = thresholdWeight + mechanismWeight + aiWeight;
			// 生成异常明细
			if (weightSum >= discontinuousStrategy.getWeightSum()) {
				if (thresholdWeight > 0) {
					detailList.add(thresholdDetail);
				}
				if (mechanismWeight > 0) {
					detailList.add(mechanismDetail);
				}
				if (aiWeight > 0) {
					detailList.add(aiDetail);
				}
			}
		}
		if (Func.isNotEmpty(detailList)) {
			this.addAbnormal(tenantId, originTime, detailList);
		}
	}

	/**
	 * 连续策略-报警门限（仅有效值）
	 *
	 * @param waveId
	 * @param originTime
	 * @return java.util.Map<java.lang.Integer, java.lang.String>
	 * <AUTHOR>
	 * @date 2024/5/23 19:16
	 */
	private AbnormalRecordVO continuousThresholdWeight(Long waveId, Date originTime) {
		AbnormalRecordVO abnormalRecord = new AbnormalRecordVO(1, AlarmBizTypeEnum.THRESHOLD.getCode(), originTime);
		Integer accumulatedWeight = continuousStrategy.getAlarmThreshold().getAccumulatedWeight();
		Integer continuousTimes = continuousStrategy.getAlarmThreshold().getContinuousTimes();
		Integer maxWeight = continuousStrategy.getAlarmThreshold().getMaxWeight();
		SensorDataVO sensorData = new SensorDataVO(null, waveId, null);
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
		List<JSONObject> list = influxdbTools.queryData(null, originTime.getTime() + 1000L, jsonObject, (query) -> {
			query.addSort("_time", true);
			query.getQuerySQL().append("|> limit(n:" + continuousTimes + ", offset:0)");
		});
		List<SensorDataDTO> sensorDataList = new ArrayList<>();
		list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
		log.info("连续策略——门限计算：=============={}", sensorDataList);
		if (Func.isNotEmpty(sensorDataList)) {
			if (sensorDataList.size() < continuousTimes) {
				return null;
			}
			List<Date> originTimeList = sensorDataList.stream().map(s -> s.getOriginTime()).collect(Collectors.toList());
			List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
				.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmIndex, AlarmIndexEnum.EFFECTIVE_VALUE.getCode())
				.eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.THRESHOLD.getCode()).in(AlarmRecord::getAlarmTime, originTimeList));
			if (Func.isNotEmpty(alarmRecordList)) {
				// 连续6次报警，则异常
				if (Func.equals(continuousTimes, alarmRecordList.size())) {
					Integer weight = accumulatedWeight * continuousTimes > maxWeight ? maxWeight : accumulatedWeight * continuousTimes;
					Integer[] alarmLevelArr = alarmRecordList.stream().map(record -> record.getAlarmLevel()).toArray(Integer[]::new);
					Integer alarmLevel = this.findMedian(alarmLevelArr);
					abnormalRecord.setAbnormalLevel(alarmLevel).setWeight(weight);
					abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(continuousStrategy.getAlarmThreshold()));
					return abnormalRecord;
				}
			} else {
				// 连续6次不报警
				if (Func.equals(continuousTimes, sensorDataList.size())) {
					return null;
				}
			}
		}
		return null;
	}

	/**
	 * 取出现次数最多的数
	 *
	 * @param list
	 * @return java.lang.Integer
	 * <AUTHOR>
	 * @date 2024/5/23 15:56
	 */
	private Integer findMostFrequent(List<Integer> list) {
		Map<Integer, Integer> frequencyMap = new HashMap<>(16);
		int maxFrequency = 0;
		Integer mostFrequentElement = null;
		// 计算每个元素的出现次数
		for (Integer number : list) {
			frequencyMap.put(number, frequencyMap.getOrDefault(number, 0) + 1);
		}
		// 找到出现次数最多的元素
		for (Map.Entry<Integer, Integer> entry : frequencyMap.entrySet()) {
			if (entry.getValue() > maxFrequency) {
				maxFrequency = entry.getValue();
				mostFrequentElement = entry.getKey();
			}
		}
		return mostFrequentElement;
	}

	/**
	 * 取中位数
	 *
	 * @param arr
	 * @return java.lang.Integer
	 * <AUTHOR>
	 * @date 2024/5/23 15:56
	 */
	private Integer findMedian(Integer[] arr) {
		// 对数组进行排序
		Arrays.sort(arr);
		int length = arr.length;
		// 根据数组长度判断中位数的位置
		if (length % 2 == 0) {
			//return (double) (arr[length / 2 - 1] + arr[length / 2]) / 2;
			return arr[length / 2];
		} else {
			return arr[length / 2];
		}
	}

	/**
	 * 非连续策略-报警门限（仅有效值）
	 *
	 * @param waveId
	 * @param originTime
	 * @return java.util.Map<java.lang.Integer, java.lang.String>
	 * <AUTHOR>
	 * @date 2024/5/23 19:16
	 */
	private AbnormalRecordVO discontinuousThresholdWeight(Long waveId, Date originTime) {
		AbnormalRecordVO abnormalRecord = new AbnormalRecordVO(2, AlarmBizTypeEnum.THRESHOLD.getCode(), originTime);
		Integer statisticsDays = discontinuousStrategy.getAlarmThreshold().getStatisticsDays() - 1;
		Integer alarmTimes = discontinuousStrategy.getAlarmThreshold().getAlarmTimes();
		Integer maxWeight = discontinuousStrategy.getAlarmThreshold().getMaxWeight();
		List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
			.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmIndex, AlarmIndexEnum.EFFECTIVE_VALUE.getCode())
			.eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.THRESHOLD.getCode())
			.between(AlarmRecord::getAlarmTime, DateUtil.minusDays(originTime, Func.toLong(statisticsDays)), originTime));
		log.info("非连续策略——门限计算：==============配置数：{}，报警数：{}", alarmTimes, alarmRecordList.size());
		if (Func.isNotEmpty(alarmRecordList)) {
			// 3天内，报警次数大于等于80次
			if (alarmRecordList.size() >= alarmTimes) {
				Integer[] alarmLevelArr = alarmRecordList.stream().map(record -> record.getAlarmLevel()).toArray(Integer[]::new);
				Integer alarmLevel = this.findMedian(alarmLevelArr);
				abnormalRecord.setAbnormalLevel(alarmLevel).setWeight(maxWeight);
				abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(discontinuousStrategy.getAlarmThreshold()));
				return abnormalRecord;
			}
		}
		return null;
	}

	/**
	 * 连续策略-AI模型
	 *
	 * @param waveId
	 * @param originTime
	 * @return java.lang.Integer
	 * <AUTHOR>
	 * @date 2024/5/23 19:16
	 */
	private AbnormalRecordVO continuousAiWeight(Long waveId, Date originTime) {
		AbnormalRecordVO abnormalRecord = new AbnormalRecordVO(1, AlarmBizTypeEnum.INTELLIGENCE.getCode(), originTime);
		Integer accumulatedWeight = continuousStrategy.getAiModel().getAccumulatedWeight();
		Integer continuousTimes = continuousStrategy.getAiModel().getContinuousTimes();
		Integer maxWeight = continuousStrategy.getAiModel().getMaxWeight();
		SensorDataVO sensorData = new SensorDataVO(null, waveId, null);
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
		List<JSONObject> list = influxdbTools.queryData(null, originTime.getTime() + 1000L, jsonObject, (query) -> {
			query.addSort("_time", true);
			query.getQuerySQL().append("|> limit(n:" + continuousTimes + ", offset:0)");
		});
		List<SensorDataDTO> sensorDataList = new ArrayList<>();
		list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
		if (Func.isNotEmpty(sensorDataList)) {
			if (sensorDataList.size() < continuousTimes) {
				return null;
			}
			List<Date> originTimeList = sensorDataList.stream().map(s -> s.getOriginTime()).collect(Collectors.toList());
			List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
				.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.INTELLIGENCE.getCode())
				.in(AlarmRecord::getAlarmTime, originTimeList));
			if (Func.isNotEmpty(alarmRecordList)) {
				// 连续6次报警
				if (Func.equals(continuousTimes, alarmRecordList.size())) {
					Integer weight = accumulatedWeight * continuousTimes;
					abnormalRecord.setAbnormalLevel(AlarmLevelEnum.LEVEL_ONE.getCode()).setWeight(weight > maxWeight ? maxWeight : weight);
					abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(continuousStrategy.getAiModel()));
					return abnormalRecord;
				}
			} else {
				// 连续6次不报警
				if (Func.equals(continuousTimes, sensorDataList.size())) {
					return null;
				}
			}
		}
		return null;
	}

	/**
	 * 非连续策略-AI模型
	 *
	 * @param waveId
	 * @param originTime
	 * @return java.lang.Integer
	 * <AUTHOR>
	 * @date 2024/5/23 19:16
	 */
	private AbnormalRecordVO discontinuousAiWeight(Long waveId, Date originTime) {
		AbnormalRecordVO abnormalRecord = new AbnormalRecordVO(2, AlarmBizTypeEnum.INTELLIGENCE.getCode(), originTime);
		Integer statisticsDays = discontinuousStrategy.getAiModel().getStatisticsDays() - 1;
		Integer alarmTimes = discontinuousStrategy.getAiModel().getAlarmTimes();
		Integer maxWeight = discontinuousStrategy.getAiModel().getMaxWeight();
		List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
			.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.INTELLIGENCE.getCode())
			.between(AlarmRecord::getAlarmTime, DateUtil.minusDays(originTime, Func.toLong(statisticsDays)), originTime));
		if (Func.isNotEmpty(alarmRecordList)) {
			// 3天内，报警次数大于等于80次
			if (alarmRecordList.size() >= alarmTimes) {
				abnormalRecord.setAbnormalLevel(AlarmLevelEnum.LEVEL_ONE.getCode()).setWeight(maxWeight);
				abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(discontinuousStrategy.getAiModel()));
				return abnormalRecord;
			}
		}
		return null;
	}

	/**
	 * 连续策略-机理模型
	 *
	 * @param waveId
	 * @param originTime
	 * @return java.util.Map<java.lang.Integer, java.util.List < java.lang.String>>
	 * <AUTHOR>
	 * @date 2024/5/23 19:16
	 */
	private List<AbnormalRecordVO> continuousMechanismWeight(Long waveId, Date originTime) {
		List<AbnormalRecordVO> recordList = new ArrayList<>();
		Integer accumulatedWeight = continuousStrategy.getMechanismModel().getAccumulatedWeight();
		Integer continuousTimes = continuousStrategy.getMechanismModel().getContinuousTimes();
		Integer maxWeight = continuousStrategy.getMechanismModel().getMaxWeight();
		// 查询采样原始数据表
		SensorDataVO sensorData = new SensorDataVO(null, waveId, null);
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
		List<JSONObject> list = influxdbTools.queryData(null, originTime.getTime() + 1000L, jsonObject, (query) -> {
			query.addSort("_time", true);
			query.getQuerySQL().append("|> limit(n:" + continuousTimes + ", offset:0)");
		});
		List<SensorDataDTO> sensorDataList = new ArrayList<>();
		list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
		log.info("连续策略——机理计算：=============={}", sensorDataList);
		if (Func.isNotEmpty(sensorDataList)) {
			if (sensorDataList.size() < continuousTimes) {
				return null;
			}
			// 按机理模型故障类型进行分别计算
			List<Date> originTimeList = sensorDataList.stream().map(s -> s.getOriginTime()).collect(Collectors.toList());
			AlarmRecord alarmRecord = alarmRecordService.getOne(Wrappers.<AlarmRecord>query().lambda()
				.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
				.eq(AlarmRecord::getAlarmTime, originTime));
			if (Func.isNotEmpty(alarmRecord)) {
				int[] faultTypeArr = Arrays.stream(alarmRecord.getFaultType().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
				log.info("连续策略——机理计算：==============faultTypeArr：{}", faultTypeArr);
				int[] faultGradeArr = Arrays.stream(alarmRecord.getFaultGrade().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
				log.info("连续策略——机理计算：==============faultGradeArr：{}", faultGradeArr);
				for (Integer i = 0; i < faultTypeArr.length; i++) {
					List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
						.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
						.like(AlarmRecord::getFaultType, faultTypeArr[i])
						.in(AlarmRecord::getAlarmTime, originTimeList));
					if (Func.isNotEmpty(alarmRecordList)) {
						if (Func.equals(continuousTimes, alarmRecordList.size())) {
							Integer weight = continuousTimes * accumulatedWeight > maxWeight
								? maxWeight : continuousTimes * accumulatedWeight;
							List<Integer> gradeList = new ArrayList<>();
							for (AlarmRecord record : alarmRecordList) {
								int[] typeArr = Arrays.stream(record.getFaultType().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
								int[] gradeArr = Arrays.stream(record.getFaultGrade().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
								log.info("连续策略——机理计算：==============faultType的位置：{}", findElementPosition(typeArr, faultTypeArr[i]));
								log.info("连续策略——机理计算：==============gradeArr：{}", gradeArr);
								gradeList.add(gradeArr[findElementPosition(typeArr, faultTypeArr[i])]);
							}
							AbnormalRecordVO abnormalRecord = new AbnormalRecordVO(1, AlarmBizTypeEnum.MECHANISM.getCode(), originTime);
							abnormalRecord.setMechanismType(faultTypeArr[i]).setWeight(weight)
								.setAbnormalLevel(this.findMedian(gradeList.stream().toArray(Integer[]::new)));
							abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(continuousStrategy.getMechanismModel()));
							recordList.add(abnormalRecord);
						}
					}
				}
				return recordList;
			} else {
				// 机理未报警
				List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
					.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
					.in(AlarmRecord::getAlarmTime, originTimeList));
				if (Func.isEmpty(alarmRecordList)) {
					return null;
				}
			}
		}
		// !=null，size=0
		return recordList;
	}

	/**
	 * 非连续策略-机理模型
	 *
	 * @param waveId
	 * @param originTime
	 * @return java.util.Map<java.lang.Integer, java.util.List < java.lang.String>>
	 * <AUTHOR>
	 * @date 2024/5/23 19:16
	 */
	private List<AbnormalRecordVO> discontinuousMechanismWeight(Long waveId, Date originTime) {
		List<AbnormalRecordVO> recordList = new ArrayList<>();
		Integer statisticsDays = discontinuousStrategy.getMechanismModel().getStatisticsDays() - 1;
		Integer alarmTimes = discontinuousStrategy.getMechanismModel().getAlarmTimes();
		Integer maxWeight = discontinuousStrategy.getMechanismModel().getMaxWeight();
		AlarmRecord alarmRecord = alarmRecordService.getOne(Wrappers.<AlarmRecord>query().lambda()
			.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
			.eq(AlarmRecord::getAlarmTime, originTime));
		if (Func.isNotEmpty(alarmRecord)) {
			int[] faultTypeArr = Arrays.stream(alarmRecord.getFaultType().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
			log.info("非连续策略——机理计算：==============faultTypeArr：{}", faultTypeArr);
			int[] faultGradeArr = Arrays.stream(alarmRecord.getFaultGrade().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
			log.info("非连续策略——机理计算：==============faultGradeArr：{}", faultGradeArr);
			// 按机理模型故障类型进行分别计算
			Map<Integer, Integer> timesMap = new HashMap<>(16);
			Map<Integer, Integer> gradeMap = new HashMap<>(16);
			for (Integer i = 0; i < faultTypeArr.length; i++) {
				List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
					.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
					.like(AlarmRecord::getFaultType, faultTypeArr[i])
					.between(AlarmRecord::getAlarmTime, DateUtil.minusDays(originTime, Func.toLong(statisticsDays)), originTime));
				timesMap.put(faultTypeArr[i], alarmRecordList.size());
				List<Integer> gradeList = new ArrayList<>();
				for (AlarmRecord record : alarmRecordList) {
					int[] typeArr = Arrays.stream(record.getFaultType().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
					int[] gradeArr = Arrays.stream(record.getFaultGrade().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
					log.info("非连续策略——机理计算：==============faultType的位置：{}", findElementPosition(typeArr, faultTypeArr[i]));
					log.info("非连续策略——机理计算：==============gradeArr：{}", gradeArr);
					gradeList.add(gradeArr[findElementPosition(typeArr, faultTypeArr[i])]);
				}
				gradeMap.put(faultTypeArr[i], this.findMedian(gradeList.stream().toArray(Integer[]::new)));
			}
			log.info("非连续策略——机理计算：==============timesMap：{}", timesMap);
			log.info("非连续策略——机理计算：==============gradeMap：{}", gradeMap);
			if (Func.isNotEmpty(timesMap)) {
				for (Integer key : timesMap.keySet()) {
					if (timesMap.get(key) >= alarmTimes) {
						log.info("非连续策略——机理计算：==============机理类型：{}，配置数：{}，报警数：{}", key, alarmTimes, timesMap.get(key));
						AbnormalRecordVO abnormalRecord = new AbnormalRecordVO(2, AlarmBizTypeEnum.MECHANISM.getCode(), originTime);
						abnormalRecord.setMechanismType(key).setAbnormalLevel(gradeMap.get(key)).setWeight(maxWeight);
						abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(discontinuousStrategy.getMechanismModel()));
						recordList.add(abnormalRecord);
					}
				}
				return recordList;
			}
		} else {
			// 机理未报警
			List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
				.eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
				.between(AlarmRecord::getAlarmTime, DateUtil.minusDays(originTime, Func.toLong(statisticsDays)), originTime));
			if (Func.isNotEmpty(alarmRecordList)) {
				Map<Integer, Integer> map = new HashMap<>(16);
				for (AlarmRecord record : alarmRecordList) {
					int[] arr = Arrays.stream(record.getFaultType().split(StringPool.COMMA)).mapToInt(Integer::parseInt).toArray();
					for (int i = 0; i < arr.length; i++) {
						if (!map.containsKey(arr[i])) {
							map.put(arr[i], 1);
						} else {
							map.put(arr[i], map.get(arr[i]) + 1);
						}
					}
				}
				log.info("非连续策略——机理计算：==============map报警数：{}", map);
				List<Integer> typeList = new ArrayList<>();
				for (Integer type : map.keySet()) {
					if (map.get(type) < alarmTimes) {
						typeList.add(type);
					}
				}
				if (Func.isNotEmpty(typeList)) {
					abnormalRecordService.remove(Wrappers.<AbnormalRecord>query().lambda()
						.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.MECHANISM.getCode())
						.eq(AbnormalRecord::getStrategyType, 2).in(AbnormalRecord::getMechanismType, typeList));
				}
			} else {
				return null;
			}
		}
		// !=null，size=0
		return recordList;
	}

	private int findElementPosition(int[] array, int target) {
		for (int i = 0; i < array.length; i++) {
			if (array[i] == target) {
				return i;
			}
		}
		// Element not found
		return -1;
	}

	/**
	 * 保存异常信息
	 *
	 * @param tenantId
	 * @param originTime
	 * @param list
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/5/23 19:18
	 */
	private boolean addAbnormal(String tenantId, Date originTime, List<AbnormalDetailVO> list) {
		log.info("更新异常信息，detailList：============{}", list);
		Abnormal abnormal = abnormalService.getOne(Wrappers.<Abnormal>query().lambda()
			.eq(Abnormal::getEquipmentId, waveDTO.getEquipmentId())
			.ne(Abnormal::getStatus, AbnormalStatusEnum.CLOSED.getCode()));
		List<AbnormalRecord> abnormalRecordList = null;
		if (Func.isNotEmpty(abnormal)) {
			abnormal.setLastTime(originTime);
			abnormalRecordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
				.eq(AbnormalRecord::getAbnormalId, abnormal.getId())
				.eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.MECHANISM.getCode())
				.ne(AbnormalRecord::getStatus, AbnormalStatusEnum.CLOSED.getCode())
				.orderByAsc(AbnormalRecord::getMechanismType));
		} else {
			abnormal = new Abnormal();
			abnormal.setTenantId(tenantId)
				.setEquipmentId(waveDTO.getEquipmentId())
				.setAbnormalLevel(AlarmLevelEnum.NORMAL.getCode())
				.setStatus(AbnormalStatusEnum.WAIT_HANDLE.getCode())
				.setFirstTime(originTime)
				.setLastTime(originTime)
				.setCreateTime(DateUtil.now());
		}
		abnormalService.saveOrUpdate(abnormal);
		AbnormalVO abnormalVO = AbnormalWrapper.build().entityVO(abnormal);
		if (Func.isNotEmpty(abnormalRecordList)) {
			abnormalVO.setFaultType(abnormalRecordList.stream().map(abnormalRecord ->
				Func.toStr(abnormalRecord.getMechanismType())).collect(Collectors.joining(",")));
		}
		log.info("更新设备异常，租户ID = {}", tenantId);
		return this.addAbnormalDetail(abnormalVO, list);
	}

	/**
	 * 保存异常信息详情和明细
	 *
	 * @param abnormalVO
	 * @param list
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/5/27 14:35
	 */
	private boolean addAbnormalDetail(AbnormalVO abnormalVO, List<AbnormalDetailVO> list) {
		Integer originLevel = abnormalVO.getAbnormalLevel();
		// 异常详情
		List<AbnormalRecordVO> records = new ArrayList<>();
		String abnormalReason = "";
		Map<Integer, List<AbnormalDetailVO>> detailMap = list.stream()
			.collect(Collectors.groupingBy(AbnormalDetailVO::getAbnormalType));
		for (Integer key : detailMap.keySet()) {
			List<AbnormalDetailVO> details = detailMap.get(key);
			for (AbnormalDetailVO vo : details) {
				records.addAll(vo.getAbnormalRecordList());
			}
			abnormalReason += key + StringPool.COMMA;
		}
		AbnormalDetail abnormalDetail = abnormalDetailService.getOne(Wrappers.<AbnormalDetail>query().lambda()
			.eq(AbnormalDetail::getAbnormalId, abnormalVO.getId())
			.eq(AbnormalDetail::getWaveId, waveDTO.getId())
			.eq(AbnormalDetail::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()));
		if (Func.isEmpty(abnormalDetail)) {
			abnormalDetail = new AbnormalDetail();
			abnormalDetail.setAbnormalId(abnormalVO.getId())
				.setMonitorId(waveDTO.getMonitorId())
				.setWaveId(waveDTO.getId())
				.setWaveName(waveDTO.getWaveName())
				.setAbnormalLevel(AlarmLevelEnum.NORMAL.getCode())
				.setStatus(AbnormalStatusEnum.WAIT_HANDLE.getCode());
		}
		abnormalDetail.setAbnormalTime(abnormalVO.getLastTime());
		abnormalDetailService.saveOrUpdate(abnormalDetail);
		Set<Integer> strategyTypeSet = new HashSet<>();
		List<AbnormalRecord> newAbnormalRecordList = records.stream().map(recordVO -> {
			strategyTypeSet.add(recordVO.getStrategyType());
			AbnormalRecord abnormalRecord = Objects.requireNonNull(BeanUtil.copy(recordVO, AbnormalRecord.class));
			abnormalRecord.setAbnormalId(abnormalVO.getId()).setWaveId(waveDTO.getId()).setAbnormalTime(abnormalVO.getLastTime());
			return abnormalRecord;
		}).collect(Collectors.toList());
		abnormalRecordService.remove(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getWaveId, waveDTO.getId())
			.eq(AbnormalRecord::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode())
			.in(AbnormalRecord::getStrategyType, new ArrayList<>(strategyTypeSet)));
		abnormalRecordService.saveBatch(newAbnormalRecordList);
		// 更新波形等级
		List<Integer> levelList = newAbnormalRecordList.stream().map(record ->
			record.getAbnormalLevel()).distinct().collect(Collectors.toList());
		if (abnormalDetail.getAbnormalLevel() < Collections.max(levelList)) {
			abnormalDetail.setAbnormalLevel(Collections.max(levelList));
		}
		abnormalDetailService.updateById(abnormalDetail);
		// 更新设备等级
		List<AbnormalDetail> abnormalDetailList = abnormalDetailService.list(Wrappers.<AbnormalDetail>query().lambda()
			.eq(AbnormalDetail::getAbnormalId, abnormalVO.getId()).eq(AbnormalDetail::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()));
		levelList = abnormalDetailList.stream().map(detail ->
			detail.getAbnormalLevel()).distinct().collect(Collectors.toList());
		if (abnormalVO.getAbnormalLevel() < Collections.max(levelList)) {
			abnormalVO.setAbnormalLevel(Collections.max(levelList));
		}
		abnormalVO.setAbnormalReason(abnormalReason.substring(0, abnormalReason.length() - 1));
		// 机理模型诊断结论和检维修建议
		List<AbnormalRecord> abnormalRecordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getAbnormalId, abnormalVO.getId()).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.MECHANISM.getCode())
			.eq(AbnormalRecord::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()).orderByAsc(AbnormalRecord::getMechanismType));
		String faultType = null;
		if (Func.isNotEmpty(abnormalRecordList)) {
			faultType = abnormalRecordList.stream().map(abnormalRecord ->
				Func.toStr(abnormalRecord.getMechanismType())).distinct().collect(Collectors.joining(","));
			levelList = abnormalRecordList.stream().map(abnormalRecord ->
				abnormalRecord.getAbnormalLevel()).distinct().collect(Collectors.toList());
			ModelAlarmResultVO resultVO = new ModelAlarmResultVO(waveDTO.getMonitorId(), faultType, Collections.max(levelList));
			IntelligentDiagnosisResult diagnosisResult = equipmentSpotCheckConfigLogicService.getIntelligentDiagnosis((JSONObject) JSONObject.toJSON(resultVO));
			if (Func.isNotEmpty(diagnosisResult)) {
				abnormalVO.setConclusion(diagnosisResult.getConclusion())
					.setSuggestion(diagnosisResult.getSuggestion());
			}
		}
		boolean ret = abnormalService.saveOrUpdate(Objects.requireNonNull(BeanUtil.copy(abnormalVO, Abnormal.class)));
		// 发送站内消息
		if (abnormalVO.getAbnormalLevel() > originLevel) {
			abnormalVO.setFaultType(faultType);
			this.sendMessage(abnormalVO);
		} else {
			if (Func.isNotEmpty(abnormalVO.getFaultType())
				&& Func.isNotEmpty(faultType) && !Func.equals(abnormalVO.getFaultType(), faultType)) {
				abnormalVO.setFaultType(faultType);
				this.sendMessage(abnormalVO);
			}
		}
		return ret;
	}

	/**
	 * 发送设备异常站内消息
	 *
	 * @param vo
	 * @return void
	 * <AUTHOR>
	 * @date 2024/5/31 15:16
	 */
	private void sendMessage(AbnormalVO vo) {
		String bizType = MessageBizTypeEnum.EQUIPMENT_ABNORMAL.getCode();
		AbnormalDTO abnormalDTO = Objects.requireNonNull(BeanUtil.copy(vo, AbnormalDTO.class));

		Equipment equipment = equipmentService.getById(vo.getEquipmentId());
		if (Func.isNotEmpty(equipment)) {
			EquipmentDTO equipmentDTO = BeanUtil.copy(equipment, EquipmentDTO.class);
			// 设备路径
			equipmentDTO.setPathName(equipment.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
			// 设备等级
			equipmentDTO.setGradeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_GRADE, equipmentDTO.getGrade()));
			abnormalDTO.setEquipmentInfo(equipmentDTO);
		}
		MessageVo messageVo = new MessageVo();
		messageVo.setAppKey("SiDAs")
			.setSender("SiDAs");
		if (AbnormalStatusEnum.WAIT_HANDLE == AbnormalStatusEnum.getByCode(vo.getStatus())) {
			abnormalDTO.setAbnormalLevelName(AbnormalLevelEnum.getByCode(abnormalDTO.getAbnormalLevel()).getName())
				.setReasonName(Arrays.asList(vo.getFaultType().split(StringPool.COMMA))
					.stream()
					.map(r -> ModelTypeEnum.getByCode(Func.toInt(r)).getName())
					.collect(Collectors.joining(",")));
			messageVo.setTitle(MessageBizTypeEnum.EQUIPMENT_ABNORMAL.getMessage() + "通知")
				.setBizType(bizType);
			log.info(MessageBizTypeEnum.EQUIPMENT_ABNORMAL.getMessage() + "-发送钉钉消息：==================={}", equipment.getCode());
		}
		if (AbnormalStatusEnum.CLOSED == AbnormalStatusEnum.getByCode(vo.getStatus())) {
			bizType = MessageBizTypeEnum.ABNORMAL_CLOSED.getCode();
			messageVo.setTitle(MessageBizTypeEnum.ABNORMAL_CLOSED.getMessage() + "通知").setBizType(bizType);
			log.info(MessageBizTypeEnum.ABNORMAL_CLOSED.getMessage() + "-发送钉钉消息：==================={}", equipment.getCode());
		}
		messageVo.setContent(JSONUtil.toJsonStr(abnormalDTO))
			.setType(MessageTypeEnum.WORK_TODO.getCode())
			.setBizId(String.valueOf(abnormalDTO.getId()))
			.setIsImmediate(YesNoEnum.YES.getCode())
			.setReceiverType(ReceiverTypeEnum.USER.getCode());
		ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
		// 根据租户ID获取角色信息（roleAlias = diagnosis_analyst,service_owner）
		List<RoleVO> roleVOList = roleService.selectByRoleAliases(vo.getTenantId(), Func.toStrList("diagnosis_analyst,service_owner"));
		List<String> userPhones = new ArrayList<>();
		if (Func.isNotEmpty(roleVOList)) {
			String roleIds = roleVOList.stream()
				.map(role -> Func.toStr(role.getId()))
				.distinct().collect(Collectors.joining(","));
			// 根据角色获取用户列表
			List<User> users = userSearchService.listByRole(Func.toLongList(roleIds));
			if (Func.isNotEmpty(users)) {
				List<ReceiverInfoVo.UserVo> userVoList = users.stream()
					.map(user -> {
						ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
						userVo.setId(user.getId());
						userVo.setRealName(user.getRealName());
						return userVo;
					}).collect(Collectors.toList());
				receiverInfoVo.setUserList(userVoList);
				userPhones = users.stream().map(user -> user.getPhone()).collect(Collectors.toList());
			} else {
				log.warn("发送设备异常消息失败：获取用户列表失败！code = {}, msg = {}");
			}
		} else {
			log.warn("发送设备异常消息失败：获取角色信息失败！code = {}, msg = {}");
		}
		messageVo.setReceiverInfoVo(receiverInfoVo);
		messageLogicService.commitMessage(messageVo);
		// 发送钉钉消息
		Tenant tenant = SysCache.getTenant(equipment.getTenantId());
		DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(tenant.getTenantId(),
			tenant.getTenantName(), equipment.getCode(), bizType, messageVo.getContent());
		dingTalkMessage.setUserPhones(userPhones);
		rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
			EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE, dingTalkMessage);
	}

	/**
	 * 自动关闭/更新设备异常
	 *
	 * @param tenantId
	 * @param waveId
	 * @param originTime
	 * @return void
	 * <AUTHOR>
	 * @date 2024/5/27 13:56
	 */
	public void autoCloseAbnormal(String tenantId, Long waveId, Date originTime) {
		this.initConfig(tenantId);
		WaveDTO waveDTO = waveService.getBy(waveId);
		Abnormal abnormal = abnormalService.getOne(Wrappers.<Abnormal>query().lambda()
			.eq(Abnormal::getEquipmentId, waveDTO.getEquipmentId()).eq(Abnormal::getStatus, AbnormalStatusEnum.IS_FAULT.getCode()));
		if (Func.isNotEmpty(abnormal)) {
			return;
		}
		AbnormalRecordVO abnormalRecord;
		List<AbnormalRecordVO> list;
		AbnormalDetail abnormalDetail = abnormalDetailService.getOne(Wrappers.<AbnormalDetail>query().lambda()
			.eq(AbnormalDetail::getWaveId, waveId).eq(AbnormalDetail::getStatus, AlarmStatusEnum.WAIT_HANDLE.getCode()));
		if (abnormalDetail == null) {
			return;
		}
		// 连续
		if (Func.isNotEmpty(continuousStrategy)) {
			// 处理机理情况
			list = this.continuousMechanismWeight(waveId, originTime);
			if (list == null) {
				abnormalRecordService.remove(Wrappers.<AbnormalRecord>query().lambda()
					.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.MECHANISM.getCode())
					.eq(AbnormalRecord::getStrategyType, 1));
			}
			// 处理门限情况
			abnormalRecord = this.continuousThresholdWeight(waveId, originTime);
			if (abnormalRecord == null) {
				abnormalRecordService.remove(Wrappers.<AbnormalRecord>query().lambda()
					.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.THRESHOLD.getCode())
					.eq(AbnormalRecord::getStrategyType, 1));
			}
			// 处理AI情况
			abnormalRecord = this.continuousAiWeight(waveId, originTime);
			if (abnormalRecord == null) {
				abnormalRecordService.remove(Wrappers.<AbnormalRecord>query().lambda()
					.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.INTELLIGENCE.getCode())
					.eq(AbnormalRecord::getStrategyType, 1));
			}
		}
		// 非连续
		if (Func.isNotEmpty(discontinuousStrategy)) {
			// 处理机理情况
			this.discontinuousMechanismWeight(waveId, originTime);
			// 处理门限情况
			abnormalRecord = this.discontinuousThresholdWeight(waveId, originTime);
			if (abnormalRecord == null) {
				abnormalRecordService.remove(Wrappers.<AbnormalRecord>query().lambda()
					.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.THRESHOLD.getCode())
					.eq(AbnormalRecord::getStrategyType, 2));
			}
			// 处理AI情况
			abnormalRecord = this.discontinuousAiWeight(waveId, originTime);
			if (abnormalRecord == null) {
				abnormalRecordService.remove(Wrappers.<AbnormalRecord>query().lambda()
					.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.INTELLIGENCE.getCode())
					.eq(AbnormalRecord::getStrategyType, 2));
			}
		}
		this.updateAbnormal(waveId, abnormalDetail);
	}

	/**
	 * 更新设备异常
	 *
	 * @param waveId
	 * @return void
	 * <AUTHOR>
	 * @date 2024/5/29 13:35
	 */
	private void updateAbnormal(Long waveId, AbnormalDetail abnormalDetail) {
		Abnormal abnormal = abnormalService.getById(abnormalDetail.getAbnormalId());
		Integer thresholdWeight = 0;
		Integer mechanismWeight = 0;
		Integer aiWeight = 0;
		Boolean conClosed = Boolean.FALSE;
		Boolean disClosed = Boolean.FALSE;
		Integer conLevel = 0;
		Integer disLevel = 0;
		// 连续
		List<AbnormalRecord> recordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getStrategyType, 1)
			.eq(AbnormalRecord::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()));
		if (Func.isNotEmpty(recordList)) {
			// 重新计算权重
			for (AbnormalRecord abnormalRecord : recordList) {
				if (AlarmBizTypeEnum.THRESHOLD == AlarmBizTypeEnum.getByCode(abnormalRecord.getAbnormalType())) {
					thresholdWeight = abnormalRecord.getWeight();
				}
				if (AlarmBizTypeEnum.MECHANISM == AlarmBizTypeEnum.getByCode(abnormalRecord.getAbnormalType())) {
					mechanismWeight = abnormalRecord.getWeight();
				}
				if (AlarmBizTypeEnum.INTELLIGENCE == AlarmBizTypeEnum.getByCode(abnormalRecord.getAbnormalType())) {
					aiWeight = abnormalRecord.getWeight();
				}
			}
			if ((thresholdWeight + mechanismWeight + aiWeight) < continuousStrategy.getWeightSum()) {
				abnormalRecordService.update(Wrappers.<AbnormalRecord>update().lambda()
					.set(AbnormalRecord::getStatus, AlarmStatusEnum.CLOSED.getCode()).set(AbnormalRecord::getCloseTime, DateUtil.now())
					.in(AbnormalRecord::getId, (recordList.stream().map(AbnormalRecord::getId).collect(Collectors.toList()))));
				conClosed = Boolean.TRUE;
			} else {
				List<Integer> levelList = recordList.stream().map(AbnormalRecord::getAbnormalLevel).collect(Collectors.toList());
				conLevel = Collections.max(levelList);
			}
		} else {
			conClosed = Boolean.TRUE;
		}
		// 非连续
		recordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getWaveId, waveId).eq(AbnormalRecord::getStrategyType, 2)
			.eq(AbnormalRecord::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()));
		if (Func.isNotEmpty(recordList)) {
			// 重新计算权重
			for (AbnormalRecord record : recordList) {
				if (AlarmBizTypeEnum.THRESHOLD == AlarmBizTypeEnum.getByCode(record.getAbnormalType())) {
					thresholdWeight = record.getWeight();
				}
				if (AlarmBizTypeEnum.MECHANISM == AlarmBizTypeEnum.getByCode(record.getAbnormalType())) {
					mechanismWeight = record.getWeight();
				}
				if (AlarmBizTypeEnum.INTELLIGENCE == AlarmBizTypeEnum.getByCode(record.getAbnormalType())) {
					aiWeight = record.getWeight();
				}
			}
			if ((thresholdWeight + mechanismWeight + aiWeight) < discontinuousStrategy.getWeightSum()) {
				abnormalRecordService.update(Wrappers.<AbnormalRecord>update().lambda()
					.set(AbnormalRecord::getStatus, AlarmStatusEnum.CLOSED.getCode()).set(AbnormalRecord::getCloseTime, DateUtil.now())
					.in(AbnormalRecord::getId, (recordList.stream().map(AbnormalRecord::getId).collect(Collectors.toList()))));
				disClosed = Boolean.TRUE;
			} else {
				List<Integer> levelList = recordList.stream().map(AbnormalRecord::getAbnormalLevel).collect(Collectors.toList());
				disLevel = Collections.max(levelList);
			}
		} else {
			disClosed = Boolean.TRUE;
		}
		//更新部位异常等级，更新设备异常等级
		if (conClosed && disClosed) {
			// 波形上无异常
			abnormalDetail.setStatus(AlarmStatusEnum.CLOSED.getCode()).setCloseTime(DateUtil.now());
			abnormalDetailService.updateById(abnormalDetail);
		} else {
			// 波形上仍存在异常，更新波形上的异常等级
			if (conLevel > disLevel) {
				abnormalDetail.setAbnormalLevel(conLevel);
			} else {
				abnormalDetail.setAbnormalLevel(disLevel);
			}
			// 更新部位上的异常等级
			abnormalDetailService.updateById(abnormalDetail);
		}
		// 更新设备异常主表
		List<AbnormalDetail> list = abnormalDetailService.list(Wrappers.<AbnormalDetail>query().lambda()
			.eq(AbnormalDetail::getAbnormalId, abnormalDetail.getAbnormalId()).eq(AbnormalDetail::getStatus, AlarmStatusEnum.WAIT_HANDLE.getCode()));
		if (Func.isNotEmpty(list)) {
			List<Integer> levelList = list.stream().map(detail -> detail.getAbnormalLevel()).collect(Collectors.toList());
			abnormal.setAbnormalLevel(Collections.max(levelList));
		} else {
			abnormal.setStatus(AlarmStatusEnum.CLOSED.getCode()).setCloseTime(DateUtil.now());
		}
		// 更新异常原因
		List<AbnormalRecord> abnormalRecordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getAbnormalId, abnormal.getId()).eq(AbnormalRecord::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()));
		if (Func.isNotEmpty(abnormalRecordList)) {
			String abnormalReason = abnormalRecordList.stream().map(abnormalRecord ->
				Func.toStr(abnormalRecord.getAbnormalType())).distinct().collect(Collectors.joining(","));
			abnormal.setAbnormalReason(abnormalReason);
		}
		// 更新异常结论和检维修建议
		abnormalRecordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getAbnormalId, abnormal.getId()).eq(AbnormalRecord::getAbnormalType, AlarmBizTypeEnum.MECHANISM.getCode())
			.eq(AbnormalRecord::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()).orderByAsc(AbnormalRecord::getMechanismType));
		if (Func.isNotEmpty(abnormalRecordList)) {
			String faultType = abnormalRecordList.stream().map(abnormalRecord ->
				Func.toStr(abnormalRecord.getMechanismType())).distinct().collect(Collectors.joining(","));
			List<Integer> levelList = abnormalRecordList.stream().map(abnormalRecord ->
				abnormalRecord.getAbnormalLevel()).distinct().collect(Collectors.toList());
			ModelAlarmResultVO resultVO = new ModelAlarmResultVO(waveDTO.getMonitorId(), faultType, Collections.max(levelList));
		}
		abnormalService.updateById(abnormal);
		// 关闭异常：发送钉钉消息
		if (AbnormalStatusEnum.CLOSED == AbnormalStatusEnum.getByCode(abnormal.getStatus())) {
			this.sendMessage(AbnormalWrapper.build().entityVO(abnormal));
		}
	}

	/**
	 * 手动关闭设备异常
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/5/27 13:56
	 */
	public boolean closeAbnormal(AbnormalCloseVO vo) {
		Abnormal abnormal = abnormalService.getById(vo.getId());
		if (abnormal == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		vo.setCloseTime(DateUtil.now()).setCloseUser(AuthUtil.getUserId())
			.setCloseUserName(UserCache.getUser(AuthUtil.getUserId()).getRealName());
		abnormal.setCloseReason(JSONUtil.toJsonStr(vo)).setCloseTime(DateUtil.now())
			.setStatus(AlarmStatusEnum.CLOSED.getCode());
		// 更新详情状态
		List<AbnormalDetail> list = abnormalDetailService.list(Wrappers.<AbnormalDetail>query().lambda()
			.eq(AbnormalDetail::getAbnormalId, abnormal.getId()));
		list.forEach(abnormalDetail -> abnormalDetail.setStatus(AbnormalStatusEnum.CLOSED.getCode()).setCloseTime(DateUtil.now()));
		abnormalDetailService.updateBatchById(list);
		// 更新明细状态
		List<AbnormalRecord> recordList = abnormalRecordService.list(Wrappers.<AbnormalRecord>query().lambda()
			.eq(AbnormalRecord::getAbnormalId, abnormal.getId()));
		recordList.forEach(abnormalRecord -> abnormalRecord.setStatus(AbnormalStatusEnum.CLOSED.getCode()).setCloseTime(DateUtil.now()));
		abnormalRecordService.updateBatchById(recordList);
		boolean ret = abnormalService.updateById(abnormal);
		// 关闭异常：发送钉钉消息
		this.sendMessage(AbnormalWrapper.build().entityVO(abnormal));
		return ret;
	}

}
