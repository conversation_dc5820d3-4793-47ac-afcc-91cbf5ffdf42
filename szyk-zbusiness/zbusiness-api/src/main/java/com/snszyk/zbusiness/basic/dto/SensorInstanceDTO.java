/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 传感器实例表数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorInstanceDTO对象", description = "传感器实例表")
public class SensorInstanceDTO extends SensorInstance {

	/**
	 * 传感器名称
	 */
	@ApiModelProperty(value = "传感器名称")
	private String name;

	/**
	 * 生产厂家（字典：sensor_supplier）
	 */
	@ApiModelProperty(value = "生产厂家（字典：sensor_supplier）")
	private String supplier;

	/**
	 * 生产厂家名称
	 */
	@ApiModelProperty(value = "生产厂家名称")
	private String supplierName;

	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;

	/**
	 * 传感器类型（字典：sensor_category）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器类型（字典：sensor_category）")
	private Integer category;

	/**
	 * 传感器类型名称
	 */
	@ApiModelProperty(value = "传感器类型名称")
	private String categoryName;

	/**
	 * 安装方向名称
	 */
	@ApiModelProperty(value = "安装方向名称")
	private String installDirectionName;

	/**
	 * 轴数（0-非振动；1-单轴；3-三轴）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "轴数（0-非振动；1-单轴；3-三轴）")
	private Integer axisCount;

	/**
	 * 有线无线（0：有线；1：无线）
	 */
	@ApiModelProperty(value = "有线无线（0：有线；1：无线）")
	@JsonSerialize(using = ToStringSerializer.class)
	private Integer isWireless;

	/**
	 * 有线无线
	 */
	@ApiModelProperty(value = "有线无线")
	private String isWirelessName;

	/**
	 * 是否可编辑 - 如果已绑定位号，则不可编辑
	 */
	@ApiModelProperty(value = "是否可编辑")
	private Boolean canEdit;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 测点名称
	 */
	@ApiModelProperty(value = "测点名称")
	private String monitorName;

	/**
	 * 绑定测点的路径名称
	 */
	@ApiModelProperty(value = "绑定测点的路径名称")
	private String pathName;

	/**
	 * 是否在线：0-离线；1-在线
	 */
	@ApiModelProperty(value = "是否在线：0-离线；1-在线")
	private Integer online;

	/**
	 * 是否在线
	 */
	@ApiModelProperty(value = "是否在线")
	private String onlineName;


	public String getOnlineName() {
		return online!=null && online == 1 ? "在线" : "离线";
	}
	/**
	 * 传感器类型参数列表
	 */
	@ApiModelProperty(value = "传感器类型参数列表")
	private List<SensorInstanceParamDTO> sensorInstanceParamList;

	/**
	 * 传感器电量
	 */
	@ApiModelProperty(value = "传感器电量")
	private BigDecimal batteryPower;

}
