package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 厂区位置信息dto
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class PlantLocationDTO {


	@ApiModelProperty(value = "主键")
	private Long id;

	@ApiModelProperty(value = "厂区")
	private String name;

	@ApiModelProperty(value = "位置信息")
	private String longitudeLatitude;

	@ApiModelProperty(value = "设备基本信息")
	private PlantDeviceStatisticsDTO device;
}
