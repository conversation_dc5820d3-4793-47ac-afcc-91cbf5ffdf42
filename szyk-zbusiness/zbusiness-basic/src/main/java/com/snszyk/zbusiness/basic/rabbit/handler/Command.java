package com.snszyk.zbusiness.basic.rabbit.handler;

import java.io.Serializable;

/**
 * 命令处理类
 * <AUTHOR>
 */
public interface Command extends Serializable {

	/**
	 * 在专检诊断页面测点下有波形的数据类型：加速度、速度、位移、温度
	 */
	String VIBRATE_COMMAND = "VIBRATE";

	/**
	 * 状态类型字段：包括传感器在线状态、传感器剩余电量 - 不存数据库，只存最新数据到Redis
	 */
	String STATE_COMMAND = "STATE";

	/**
	 * 应力波类型数据
	 */
	String STRESS_COMMAND = "STRESS";

	/**
	 * 电流类型数据
	 */
	String ELECTRIC_COMMAND = "ELECTRIC";

	/**
	 * 转速类型数据
	 */
	String RPM_COMMAND = "RPM";

	/**
	 * 取得操作命令
	 * @return 命令
	 */
	String getCommand();
}
