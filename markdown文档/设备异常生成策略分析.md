# 设备异常生成策略分析

## 1. 异常生成概述

设备异常生成是AI运维平台的核心功能之一，通过对设备测点数据的实时监控和分析，系统能够自动识别并生成设备异常信息，为运维人员提供决策支持。本文档详细分析了异常生成的策略逻辑、数据流程和关键实现。

### 1.1 异常生成的基本流程

异常生成的基本流程如下：

1. 系统接收设备测点的实时监测数据
2. 根据配置的策略（连续策略或非连续策略）对数据进行分析
3. 当满足异常条件时，生成异常记录
4. 根据异常记录的权重和等级，决定是否生成设备异常
5. 将异常信息推送给相关人员

### 1.2 异常相关的核心实体

异常管理系统主要包含以下核心实体：

- **Abnormal（异常）**：设备级别的异常信息，一个设备可能有多个异常详情
- **AbnormalDetail（异常详情）**：关联到具体的监测点和波形，记录异常发生的位置
- **AbnormalRecord（异常记录）**：记录具体的异常原因和诊断结果

## 2. 异常生成策略

系统支持两种主要的异常生成策略：连续策略和非连续策略。

### 2.1 连续策略

连续策略主要关注短时间内连续发生的异常情况，当设备在连续的几次监测中都出现异常时，系统会生成异常记录。

```java
// 连续策略配置类
@Data
@ApiModel(value = "ContinuousStrategyVO", description = "ContinuousStrategyVO")
public class ContinuousStrategyVO implements Serializable {
    // 报警门限
    @ApiModelProperty(value = "报警门限")
    private AbnormalParamVO alarmThreshold;
    // 机理模型
    @ApiModelProperty(value = "机理模型")
    private AbnormalParamVO mechanismModel;
    // AI模型
    @ApiModelProperty(value = "AI模型")
    private AbnormalParamVO aiModel;
    // 权重和
    @ApiModelProperty(value = "权重和")
    private Integer weightSum;
}
```

连续策略的主要参数：

- **连续超限次数（continuousTimes）**：需要连续多少次超限才触发异常
- **超限累加权重（accumulatedWeight）**：每次超限累加的权重值
- **最大权重（maxWeight）**：权重的最大值

### 2.2 非连续策略

非连续策略关注一段时间内累计的异常情况，即使异常不是连续发生的，只要在指定时间内累计异常次数达到阈值，也会生成异常记录。

```java
// 非连续策略配置类
@Data
@ApiModel(value = "DiscontinuousStrategyVO对象", description = "DiscontinuousStrategyVO对象")
public class DiscontinuousStrategyVO implements Serializable {
    // 报警门限
    @ApiModelProperty(value = "报警门限")
    private AbnormalParamVO alarmThreshold;
    // 机理模型
    @ApiModelProperty(value = "机理模型")
    private AbnormalParamVO mechanismModel;
    // AI模型
    @ApiModelProperty(value = "AI模型")
    private AbnormalParamVO aiModel;
    // 权重和
    @ApiModelProperty(value = "权重和")
    private Integer weightSum;
}
```

非连续策略的主要参数：

- **统计天数（statisticsDays）**：统计异常的时间范围，如最近7天
- **报警次数（alarmTimes）**：在统计时间内需要达到的报警次数
- **最大权重（maxWeight）**：权重的最大值

### 2.3 策略参数配置

策略参数通过`AbnormalParamVO`类进行配置：

```java
@Data
@ApiModel(value = "AbnormalParamVO", description = "AbnormalParamVO")
public class AbnormalParamVO implements Serializable {
    // 最大权重
    @ApiModelProperty(value = "最大权重")
    private Integer maxWeight;
    // 连续策略-超限累加权重
    @ApiModelProperty(value = "连续策略-超限累加权重")
    private Integer accumulatedWeight;
    // 连续策略-连续超限次数
    @ApiModelProperty(value = "连续策略-连续超限次数")
    private Integer continuousTimes;
    // 非连续策略-统计天数
    @ApiModelProperty(value = "非连续策略-统计天数")
    private Integer statisticsDays;
    // 非连续策略-报警次数
    @ApiModelProperty(value = "非连续策略-报警次数")
    private Integer alarmTimes;
    // 是否启用
    @ApiModelProperty(value = "是否启用（0：停用，1：启用）")
    private Integer isEnabled;
}
```

## 3. 异常类型与权重计算

系统支持三种主要的异常类型，每种类型都有自己的权重计算方式。

### 3.1 门限异常（Threshold）

门限异常是指测点数据超过预设的阈值。当数据超过门限时，系统会根据策略计算权重。

```java
// 门限异常权重计算（连续策略）
private AbnormalRecordVO continuousThresholdWeight(Long waveId, Date originTime) {
    // 获取策略参数
    Integer continuousTimes = continuousStrategy.getAlarmThreshold().getContinuousTimes();
    Integer accumulatedWeight = continuousStrategy.getAlarmThreshold().getAccumulatedWeight();
    Integer maxWeight = continuousStrategy.getAlarmThreshold().getMaxWeight();

    // 获取最近的传感器数据
    List<SensorDataVO> sensorDataList = this.getSensorData(waveId, continuousTimes);

    // 检查是否有足够的数据
    if (Func.isNotEmpty(sensorDataList) && sensorDataList.size() >= continuousTimes) {
        // 获取报警记录
        List<Date> originTimeList = sensorDataList.stream().map(s -> s.getOriginTime()).collect(Collectors.toList());
        List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
            .eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.THRESHOLD.getCode())
            .in(AlarmRecord::getAlarmTime, originTimeList));

        // 如果连续报警次数达到阈值
        if (Func.isNotEmpty(alarmRecordList) && alarmRecordList.size() >= continuousTimes) {
            // 计算权重
            Integer weight = accumulatedWeight * continuousTimes;
            weight = weight > maxWeight ? maxWeight : weight;

            // 创建异常记录
            AbnormalRecordVO abnormalRecord = new AbnormalRecordVO();
            abnormalRecord.setAbnormalType(AlarmBizTypeEnum.THRESHOLD.getCode()).setStrategyType(1);

            // 设置异常等级和权重
            Integer[] alarmLevelArr = alarmRecordList.stream().map(record -> record.getAlarmLevel()).toArray(Integer[]::new);
            Integer alarmLevel = this.findMedian(alarmLevelArr);
            abnormalRecord.setAbnormalLevel(alarmLevel).setWeight(weight);
            abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(continuousStrategy.getAlarmThreshold()));
            return abnormalRecord;
        }
    }
    return null;
}
```

### 3.2 机理异常（Mechanism）

机理异常是基于设备运行机理模型的异常，通常与设备的工作原理和物理特性相关。

```java
// 机理异常权重计算（非连续策略）
private List<AbnormalRecordVO> discontinuousMechanismWeight(Long waveId, Date originTime) {
    // 获取策略参数
    Integer statisticsDays = discontinuousStrategy.getMechanismModel().getStatisticsDays();
    Integer alarmTimes = discontinuousStrategy.getMechanismModel().getAlarmTimes();
    Integer maxWeight = discontinuousStrategy.getMechanismModel().getMaxWeight();

    // 查询报警记录
    List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
        .eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.MECHANISM.getCode())
        .between(AlarmRecord::getAlarmTime, DateUtil.minusDays(originTime, Func.toLong(statisticsDays)), originTime));

    // 如果报警次数达到阈值
    if (Func.isNotEmpty(alarmRecordList) && alarmRecordList.size() >= alarmTimes) {
        // 按机理类型分组
        Map<Integer, List<AlarmRecord>> recordMap = alarmRecordList.stream()
            .collect(Collectors.groupingBy(AlarmRecord::getMechanismType));

        // 创建异常记录列表
        List<AbnormalRecordVO> list = new ArrayList<>();
        for (Integer key : recordMap.keySet()) {
            List<AlarmRecord> records = recordMap.get(key);
            if (records.size() >= alarmTimes) {
                // 创建异常记录
                AbnormalRecordVO abnormalRecord = new AbnormalRecordVO();
                abnormalRecord.setAbnormalType(AlarmBizTypeEnum.MECHANISM.getCode()).setStrategyType(2);
                abnormalRecord.setMechanismType(key).setWeight(maxWeight);

                // 设置异常等级
                Integer[] alarmLevelArr = records.stream().map(record -> record.getAlarmLevel()).toArray(Integer[]::new);
                Integer alarmLevel = this.findMedian(alarmLevelArr);
                abnormalRecord.setAbnormalLevel(alarmLevel);
                abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(discontinuousStrategy.getMechanismModel()));
                list.add(abnormalRecord);
            }
        }
        return list;
    }
    return null;
}
```

### 3.3 AI异常（Intelligence）

AI异常是基于人工智能模型分析得出的异常，通常通过机器学习算法对设备数据进行分析。

```java
// AI异常权重计算（非连续策略）
private AbnormalRecordVO discontinuousAiWeight(Long waveId, Date originTime) {
    // 获取策略参数
    Integer statisticsDays = discontinuousStrategy.getAiModel().getStatisticsDays();
    Integer alarmTimes = discontinuousStrategy.getAiModel().getAlarmTimes();
    Integer maxWeight = discontinuousStrategy.getAiModel().getMaxWeight();

    // 创建异常记录
    AbnormalRecordVO abnormalRecord = new AbnormalRecordVO();
    abnormalRecord.setAbnormalType(AlarmBizTypeEnum.INTELLIGENCE.getCode()).setStrategyType(2);

    // 查询报警记录
    List<AlarmRecord> alarmRecordList = alarmRecordService.list(Wrappers.<AlarmRecord>query().lambda()
        .eq(AlarmRecord::getWaveId, waveId).eq(AlarmRecord::getAlarmType, AlarmBizTypeEnum.INTELLIGENCE.getCode())
        .between(AlarmRecord::getAlarmTime, DateUtil.minusDays(originTime, Func.toLong(statisticsDays)), originTime));

    // 如果报警次数达到阈值
    if (Func.isNotEmpty(alarmRecordList) && alarmRecordList.size() >= alarmTimes) {
        abnormalRecord.setAbnormalLevel(AlarmLevelEnum.LEVEL_ONE.getCode()).setWeight(maxWeight);
        abnormalRecord.setStrategyConfig(JSONUtil.toJsonStr(discontinuousStrategy.getAiModel()));
        return abnormalRecord;
    }
    return null;
}
```

## 4. 异常生成流程

### 4.1 异常生成的触发

异常生成通常由以下事件触发：

1. 设备测点数据超过门限
2. 机理模型分析结果异常
3. AI模型分析结果异常

当这些事件发生时，系统会调用`alarmDataHandler`方法进行二次确认，根据策略判断是否需要生成异常。

### 4.2 异常生成的核心方法

异常生成的核心方法是`addAbnormal`，该方法负责创建或更新异常信息：

```java
private boolean addAbnormal(String tenantId, Date originTime, List<AbnormalDetailVO> list) {
    // 查询是否已存在异常
    Abnormal abnormal = abnormalService.getOne(Wrappers.<Abnormal>query().lambda()
        .eq(Abnormal::getEquipmentId, waveDTO.getEquipmentId())
        .ne(Abnormal::getStatus, AbnormalStatusEnum.CLOSED.getCode()));

    // 如果已存在异常，则更新最后异常时间
    if (Func.isNotEmpty(abnormal)) {
        abnormal.setLastTime(originTime);
    } else {
        // 如果不存在，则创建新异常
        abnormal = new Abnormal();
        abnormal.setTenantId(tenantId).setEquipmentId(waveDTO.getEquipmentId())
            .setAbnormalLevel(AlarmLevelEnum.NORMAL.getCode())
            .setStatus(AbnormalStatusEnum.WAIT_HANDLE.getCode())
            .setFirstTime(originTime).setLastTime(originTime)
            .setCreateTime(DateUtil.now());
    }

    // 保存异常信息
    abnormalService.saveOrUpdate(abnormal);

    // 添加异常详情
    return this.addAbnormalDetail(AbnormalWrapper.build().entityVO(abnormal), list);
}
```

### 4.3 异常详情和记录的创建

异常详情和记录的创建由`addAbnormalDetail`方法负责：

```java
private boolean addAbnormalDetail(AbnormalVO abnormalVO, List<AbnormalDetailVO> list) {
    // 记录原始异常等级
    Integer originLevel = abnormalVO.getAbnormalLevel();

    // 处理异常详情
    List<AbnormalRecordVO> records = new ArrayList<>();
    String abnormalReason = "";

    // 按异常类型分组
    Map<Integer, List<AbnormalDetailVO>> detailMap = list.stream()
        .collect(Collectors.groupingBy(AbnormalDetailVO::getAbnormalType));

    // 收集所有异常记录
    for (Integer key : detailMap.keySet()) {
        List<AbnormalDetailVO> details = detailMap.get(key);
        for (AbnormalDetailVO vo : details) {
            records.addAll(vo.getAbnormalRecordList());
        }
        abnormalReason += key + StringPool.COMMA;
    }

    // 查询或创建异常详情
    AbnormalDetail abnormalDetail = abnormalDetailService.getOne(Wrappers.<AbnormalDetail>query().lambda()
        .eq(AbnormalDetail::getAbnormalId, abnormalVO.getId())
        .eq(AbnormalDetail::getWaveId, waveDTO.getId())
        .eq(AbnormalDetail::getStatus, AbnormalStatusEnum.WAIT_HANDLE.getCode()));

    if (Func.isEmpty(abnormalDetail)) {
        abnormalDetail = new AbnormalDetail();
        abnormalDetail.setAbnormalId(abnormalVO.getId())
            .setMonitorId(waveDTO.getMonitorId())
            .setWaveId(waveDTO.getId())
            .setWaveName(waveDTO.getWaveName())
            .setAbnormalLevel(AlarmLevelEnum.NORMAL.getCode())
            .setStatus(AbnormalStatusEnum.WAIT_HANDLE.getCode());
    }

    // 更新异常时间
    abnormalDetail.setAbnormalTime(abnormalVO.getLastTime());
    abnormalDetailService.saveOrUpdate(abnormalDetail);

    // 创建异常记录
    List<AbnormalRecord> newAbnormalRecordList = records.stream().map(recordVO -> {
        AbnormalRecord abnormalRecord = Objects.requireNonNull(BeanUtil.copy(recordVO, AbnormalRecord.class));
        abnormalRecord.setAbnormalId(abnormalVO.getId())
            .setWaveId(waveDTO.getId())
            .setAbnormalTime(abnormalVO.getLastTime());
        return abnormalRecord;
    }).collect(Collectors.toList());

    // 保存异常记录
    abnormalRecordService.saveBatch(newAbnormalRecordList);

    // 更新异常等级和原因
    List<Integer> levelList = newAbnormalRecordList.stream()
        .map(AbnormalRecord::getAbnormalLevel)
        .collect(Collectors.toList());

    abnormalVO.setAbnormalLevel(Collections.max(levelList))
        .setAbnormalReason(abnormalReason.substring(0, abnormalReason.length() - 1));

    // 保存异常信息
    boolean ret = abnormalService.saveOrUpdate(Objects.requireNonNull(BeanUtil.copy(abnormalVO, Abnormal.class)));

    // 发送消息通知
    if (abnormalVO.getAbnormalLevel() > originLevel) {
        this.sendMessage(abnormalVO);
    }

    return ret;
}
```

## 5. 单个测点与多测点异常

### 5.1 单个测点异常

系统支持基于单个测点的异常生成。当一个测点的数据满足异常条件时，系统会为该测点生成异常记录。这种情况下，异常的生成主要依赖于该测点的数据和配置的策略。

单个测点异常的生成流程：

1. 测点数据超过门限或被机理/AI模型判定为异常
2. 系统根据策略（连续或非连续）判断是否需要生成异常
3. 如果满足条件，系统会创建一个AbnormalDetail记录，关联到该测点
4. 同时创建对应的AbnormalRecord记录，记录异常的具体原因
5. 最后创建或更新Abnormal记录，关联到设备

### 5.2 多测点异常

系统也支持基于多个测点的综合分析生成异常。在这种情况下，异常的生成需要考虑多个测点的数据和状态，通常用于更复杂的故障诊断。

多测点异常的生成流程：

1. 多个测点的数据被收集和分析
2. 系统根据策略和多个测点的状态进行综合判断
3. 如果满足条件，系统会为每个相关测点创建AbnormalDetail记录
4. 同时创建对应的AbnormalRecord记录
5. 最后创建或更新一个Abnormal记录，关联到设备

### 5.3 测点与异常的关系

从代码实现来看，系统的设计支持以下关系：

- 一个设备可以有多个测点
- 一个测点可以有多个波形
- 一个异常（Abnormal）关联到一个设备
- 一个异常可以有多个异常详情（AbnormalDetail），每个异常详情关联到一个测点和波形
- 一个异常详情可以有多个异常记录（AbnormalRecord），记录不同类型的异常原因

这种设计使得系统能够灵活地处理单测点异常和多测点异常，同时保持数据的一致性和可追溯性。

## 6. 异常生成流程图

为了更直观地展示设备异常生成的完整流程，下面提供了详细的流程图：

```mermaid
graph TD
    Start[开始] --> DataCollection[接收设备测点数据]
    DataCollection --> InitConfig[初始化策略配置]
    InitConfig --> CheckExistingAbnormal[检查是否已有未关闭的故障]

    CheckExistingAbnormal -->|是| End[结束]
    CheckExistingAbnormal -->|否| StrategyCheck[策略处理]

    StrategyCheck --> ContinuousStrategy[连续策略处理]
    StrategyCheck --> DiscontinuousStrategy[非连续策略处理]

    ContinuousStrategy --> ContinuousThreshold[门限异常检查]
    ContinuousStrategy --> ContinuousMechanism[机理异常检查]
    ContinuousStrategy --> ContinuousAI[AI异常检查]

    ContinuousThreshold --> ContinuousWeightCalc[计算连续策略权重]
    ContinuousMechanism --> ContinuousWeightCalc
    ContinuousAI --> ContinuousWeightCalc

    DiscontinuousStrategy --> DiscontinuousThreshold[门限异常检查]
    DiscontinuousStrategy --> DiscontinuousMechanism[机理异常检查]
    DiscontinuousStrategy --> DiscontinuousAI[AI异常检查]

    DiscontinuousThreshold --> DiscontinuousWeightCalc[计算非连续策略权重]
    DiscontinuousMechanism --> DiscontinuousWeightCalc
    DiscontinuousAI --> DiscontinuousWeightCalc

    ContinuousWeightCalc --> ContinuousWeightCheck[权重和>=阈值?]
    DiscontinuousWeightCalc --> DiscontinuousWeightCheck[权重和>=阈值?]

    ContinuousWeightCheck -->|是| GenerateAbnormalDetails[生成异常详情]
    DiscontinuousWeightCheck -->|是| GenerateAbnormalDetails

    ContinuousWeightCheck -->|否| End
    DiscontinuousWeightCheck -->|否| End

    GenerateAbnormalDetails --> CheckExistingAbnormalRecord[检查是否已有异常记录]

    CheckExistingAbnormalRecord -->|是| UpdateAbnormal[更新异常信息]
    CheckExistingAbnormalRecord -->|否| CreateNewAbnormal[创建新异常]

    UpdateAbnormal --> SaveAbnormal[保存异常信息]
    CreateNewAbnormal --> SaveAbnormal

    SaveAbnormal --> ProcessAbnormalDetails[处理异常详情]
    ProcessAbnormalDetails --> GroupByType[按异常类型分组]
    GroupByType --> CreateAbnormalDetail[创建/更新异常详情]
    CreateAbnormalDetail --> CreateAbnormalRecords[创建异常记录]

    CreateAbnormalRecords --> UpdateAbnormalLevel[更新异常等级]
    UpdateAbnormalLevel --> CompareLevel[异常等级是否提高?]

    CompareLevel -->|是| SendNotification[发送通知]
    CompareLevel -->|否| CheckFaultType[故障类型是否变化?]

    CheckFaultType -->|是| SendNotification
    CheckFaultType -->|否| End

    SendNotification --> End
```

### 6.1 异常生成主流程

1. **数据收集与初始化**
   - 接收设备测点数据
   - 初始化策略配置（连续策略和非连续策略）
   - 检查是否已有未关闭的故障

2. **策略处理**
   - 连续策略处理：
     - 门限异常检查
     - 机理异常检查
     - AI异常检查
     - 计算连续策略权重
     - 判断权重和是否达到阈值

   - 非连续策略处理：
     - 门限异常检查
     - 机理异常检查
     - AI异常检查
     - 计算非连续策略权重
     - 判断权重和是否达到阈值

3. **异常生成**
   - 生成异常详情
   - 检查是否已有异常记录
   - 有则更新异常信息，无则创建新异常
   - 保存异常信息

4. **异常详情处理**
   - 处理异常详情
   - 按异常类型分组
   - 创建/更新异常详情
   - 创建异常记录
   - 更新异常等级

5. **通知处理**
   - 判断异常等级是否提高
   - 判断故障类型是否变化
   - 发送通知

### 6.2 子流程：门限异常检查（连续策略）

```mermaid
graph TD
    CT1[获取连续次数参数] --> CT2[获取最近N次数据]
    CT2 --> CT3[数据量>=连续次数?]
    CT3 -->|是| CT4[查询报警记录]
    CT3 -->|否| CT7[返回null]
    CT4 --> CT5[报警记录>=连续次数?]
    CT5 -->|是| CT6[计算权重并返回]
    CT5 -->|否| CT7
```

### 6.3 子流程：门限异常检查（非连续策略）

```mermaid
graph TD
    DT1[获取统计天数和报警次数] --> DT2[查询时间范围内报警记录]
    DT2 --> DT3[报警记录>=报警次数?]
    DT3 -->|是| DT4[设置最大权重并返回]
    DT3 -->|否| DT5[返回null]
```

### 6.4 子流程：机理异常检查（非连续策略）

```mermaid
graph TD
    DM1[获取统计天数和报警次数] --> DM2[查询时间范围内报警记录]
    DM2 --> DM3[报警记录>=报警次数?]
    DM3 -->|是| DM4[按机理类型分组]
    DM3 -->|否| DM7[返回null]
    DM4 --> DM5[遍历每种机理类型]
    DM5 --> DM6[该类型报警>=报警次数?]
    DM6 -->|是| DM8[创建异常记录]
    DM6 -->|否| DM9[继续下一类型]
    DM8 --> DM10[返回异常记录列表]
```

### 6.5 流程图说明

上述流程图详细展示了设备异常生成的完整过程，包括主流程和各个子流程。通过这个流程图，我们可以清晰地看到异常生成的各个环节和决策点。

特别需要注意的是，在异常生成过程中，系统会同时处理连续策略和非连续策略，并根据不同类型的异常（门限、机理、AI）计算权重。只有当权重和达到阈值时，才会生成异常详情。

对于单测点和多测点异常的处理，流程图也有所体现：

- **单测点异常**：当一个测点的数据满足异常条件时，系统会为该测点生成异常记录，并创建对应的AbnormalDetail和AbnormalRecord
- **多测点异常**：系统会收集多个测点的数据，分别进行异常检查，然后综合判断是否需要生成设备级别的异常

这种设计使得系统能够灵活地处理各种复杂的异常场景，既能识别单个测点的异常，也能基于多个测点的综合分析生成更准确的异常判断。

## 7. 结论

设备异常生成策略是AI运维平台的核心功能之一，通过连续策略和非连续策略，系统能够灵活地处理各种异常情况。异常生成的过程涉及多个实体和复杂的业务逻辑，但总体上遵循"收集数据 -> 分析数据 -> 生成异常 -> 通知用户"的流程。

系统支持基于单个测点和多个测点的异常生成，能够满足不同场景下的需求。通过合理配置策略参数，可以调整异常生成的灵敏度和准确性，提高系统的实用性和可靠性。
