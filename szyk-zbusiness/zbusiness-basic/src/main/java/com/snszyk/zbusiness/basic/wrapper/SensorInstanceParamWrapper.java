/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;
import com.snszyk.zbusiness.basic.vo.SensorInstanceParamVO;

import java.util.Objects;

/**
 * 传感器实例参数表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class SensorInstanceParamWrapper extends BaseEntityWrapper<SensorInstanceParam, SensorInstanceParamVO> {

	public static SensorInstanceParamWrapper build() {
		return new SensorInstanceParamWrapper();
	}

	@Override
	public SensorInstanceParamVO entityVO(SensorInstanceParam sensorInstanceParam) {
		SensorInstanceParamVO sensorInstanceParamVO = Objects.requireNonNull(BeanUtil.copy(sensorInstanceParam, SensorInstanceParamVO.class));

		//User createUser = UserCache.getUser(SensorInstanceParam.getCreateUser());
		//User updateUser = UserCache.getUser(SensorInstanceParam.getUpdateUser());
		//sensorInstanceParamVO.setCreateUserName(createUser.getName());
		//sensorInstanceParamVO.setUpdateUserName(updateUser.getName());

		return sensorInstanceParamVO;
	}

}
