/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.oss.model.SzykFile;
import com.snszyk.core.tool.utils.FileUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.resource.vo.AttachVO;
import lombok.SneakyThrows;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Arrays;

/**
 * 附件表 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_RESOURCE_NAME + "/attach")
@Api(value = "附件", tags = "附件")
public class AttachController extends SzykController {

	private final IAttachService attachService;

	/**
	 * 上传文件格式
	 */
	private static final String[] FILE_FORMAT_ARR = {"jpeg", "jpg", "png","pdf"};
	private static final String[] MODEL_FORMAT_ARR = {"fbx", "FBX"};
	private static final String[] WGT_FORMAT_ARR = {"wgt"};

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入attach")
	public R<Attach> detail(Attach attach) {
		Attach detail = attachService.getOne(Condition.getQueryWrapper(attach));
		return R.data(detail);
	}

	/**
	 * 分页 附件表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入attach")
	public R<IPage<Attach>> list(Attach attach, Query query) {
		IPage<Attach> pages = attachService.page(Condition.getPage(query), Condition.getQueryWrapper(attach));
		return R.data(pages);
	}

	/**
	 * 自定义分页 附件表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入attach")
	public R<IPage<AttachVO>> page(AttachVO attach, Query query) {
		IPage<AttachVO> pages = attachService.selectAttachPage(Condition.getPage(query), attach);
		return R.data(pages);
	}

	/**
	 * 新增 附件表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入attach")
	public R save(@Valid @RequestBody Attach attach) {
		return R.status(attachService.save(attach));
	}

	/**
	 * 修改 附件表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入attach")
	public R update(@Valid @RequestBody Attach attach) {
		return R.status(attachService.updateById(attach));
	}

	/**
	 * 新增或修改 附件表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入attach")
	public R submit(@Valid @RequestBody Attach attach) {
		return R.status(attachService.saveOrUpdate(attach));
	}


	/**
	 * 删除 附件表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(attachService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 下载
	 */
	@GetMapping(value= "/download/{attachId}")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "下载", notes = "传入attachId")
	public void download(@PathVariable(name = "attachId") Long attachId, HttpServletResponse response) {
		attachService.download(attachId, response);
		//return R.success("操作成功");
	}

	/**
	 * 上传普通文件并保存至附件表
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-file-attach")
	public R<SzykFile> putFileAttach(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();
		String fileExtension = FileUtil.getFileExtension(fileName);
		if (!Arrays.asList(FILE_FORMAT_ARR).contains(fileExtension)) {
			throw new ServiceException("仅支持上传以下格式的文件：" + String.join(",", Arrays.asList(FILE_FORMAT_ARR)));
		}
		SzykFile szykFile = attachService.putFile(fileName, EolmConstant.SaveFileUrl.COMMON_FILE, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), szykFile);
		szykFile.setAttachId(attachId);
		return R.data(szykFile);
	}

	/**
	 * 构建附件表
	 *
	 * @param fileName  文件名
	 * @param fileSize  文件大小
	 * @param szykFile 对象存储文件
	 * @return attachId
	 */
	private Long buildAttach(String fileName, Long fileSize, SzykFile szykFile) {
		String fileExtension = FileUtil.getFileExtension(fileName);
		Attach attach = new Attach();
		attach.setDomain(szykFile.getDomain());
		attach.setLink(szykFile.getLink());
		attach.setName(szykFile.getName());
		attach.setOriginalName(szykFile.getOriginalName());
		attach.setAttachSize(fileSize);
		attach.setExtension(fileExtension);
		attachService.save(attach);
		return attach.getId();
	}

	/**
	 * 上传3D模型文件并保存至附件表
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-model-attach")
	public R<SzykFile> putModelAttach(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();
		String fileExtension = FileUtil.getFileExtension(fileName);
		if (!Arrays.asList(MODEL_FORMAT_ARR).contains(fileExtension)) {
			throw new ServiceException("仅支持上传以下格式的文件：" + String.join(",", Arrays.asList(MODEL_FORMAT_ARR)));
		}
		SzykFile szykFile = attachService.putFile(fileName, EolmConstant.SaveFileUrl.THREE_DIMENSION, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), szykFile);
		szykFile.setAttachId(attachId);
		return R.data(szykFile);
	}

	/**
	 * 上传wgt更新包并保存至附件表
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-wgt-attach")
	public R<SzykFile> putWgtAttach(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();
		String fileExtension = FileUtil.getFileExtension(fileName);
		if (!Arrays.asList(WGT_FORMAT_ARR).contains(fileExtension)) {
			throw new ServiceException("仅支持上传.wgt格式的文件！");
		}
		SzykFile szykFile = attachService.putFile(fileName, EolmConstant.SaveFileUrl.APP_WGT, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), szykFile);
		szykFile.setAttachId(attachId);
		return R.data(szykFile);
	}

	/**
	 * 上传普通文件并保存至附件表
	 *
	 * @param file 文件
	 * @return ObjectStat
	 */
	@SneakyThrows
	@PostMapping("/put-all-file")
	public R<SzykFile> putAllFile(@RequestParam MultipartFile file) {
		String fileName = file.getOriginalFilename();
		SzykFile szykFile = attachService.putFile(fileName, EolmConstant.SaveFileUrl.COMMON_FILE, file.getInputStream());
		Long attachId = buildAttach(fileName, file.getSize(), szykFile);
		szykFile.setAttachId(attachId);
		return R.data(szykFile);
	}


}
