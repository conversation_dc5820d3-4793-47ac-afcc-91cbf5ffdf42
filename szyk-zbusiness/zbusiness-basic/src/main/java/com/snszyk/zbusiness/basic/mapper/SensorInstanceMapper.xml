<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.zbusiness.basic.mapper.SensorInstanceMapper">

    <update id="unbindByMonitor">
        UPDATE basic_sensor_instance
        SET equipment_id = NULL, monitor_id = NULL, install_direction = NULL,
            `number` = NULL, phase = NULL, virtual_sensor_id = NULL
        WHERE monitor_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="bindCollectionStation">
        UPDATE basic_sensor_instance
        SET station_id = #{stationId}
        WHERE code IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="unbindCollectionStation">
        UPDATE basic_sensor_instance
        SET station_id = NULL
        WHERE station_id IN
        <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="unbindBySensorInstance">
        UPDATE basic_sensor_instance
        SET equipment_id = NULL, monitor_id = NULL, install_direction = NULL,
            `number` = NULL, phase = NULL, virtual_sensor_id = NULL
        WHERE id = #{sensorInstanceId}
    </update>

    <delete id="unbindByEquipment">
        UPDATE basic_sensor_instance
        SET equipment_id = NULL, monitor_id = NULL, install_direction = NULL,
            `number` = NULL, phase = NULL, virtual_sensor_id = NULL
        WHERE equipment_id = #{equipmentId}
    </delete>

    <select id="page" resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceDTO">
        SELECT i.*, t.*, REPLACE(m.path_name, ',' , '/') as path_name, m.`name` AS monitor_name, e.`name` AS equipment_name
        FROM basic_sensor_instance i
        LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        LEFT JOIN eolm_monitor m ON i.monitor_id = m.id
        LEFT JOIN eolm_equipment e ON i.equipment_id = e.id
        WHERE i.is_deleted = 0
        <if test="vo.nameOrModelOrCode!=null and vo.nameOrModelOrCode!=''">
            AND (t.name LIKE concat('%', #{vo.nameOrModelOrCode},'%')
                     OR t.model LIKE concat('%', #{vo.nameOrModelOrCode},'%')
                     OR i.`code` LIKE concat('%', #{vo.nameOrModelOrCode},'%'))
        </if>
        <if test="vo.isBind != null and vo.isBind==0">
            AND i.monitor_id IS NULL
        </if>
        <if test="vo.isBind != null and vo.isBind==1">
            AND i.monitor_id IS NOT NULL
        </if>
        <if test="vo.isBindStation != null and vo.isBindStation==0">
            AND i.station_id IS NULL
        </if>
        <if test="vo.isBindStation != null and vo.isBindStation==1">
            AND i.station_id IS NOT NULL
        </if>
        <if test="vo.supplier!=null and vo.supplier!=''">
            AND t.`supplier` = #{vo.supplier}
        </if>
        <if test="vo.isWireless!=null">
            AND t.is_wireless = #{vo.isWireless}
        </if>
        <if test="vo.category!=null">
            AND t.category = #{vo.category}
        </if>
        <if test="vo.pathId!=null and vo.pathId!=''">
            AND m.path LIKE concat('%', #{vo.pathId},'%')
        </if>
        ORDER BY i.create_time DESC, i.`code` DESC
    </select>

    <select id="listByMonitorId" resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceDTO">
        SELECT i.*, t.`name`, t.model, t.supplier, t.category, t.axis_count, t.is_wireless,
               m.`name` AS monitor_name, REPLACE(m.path_name,',','/') AS path_name, e.`name` AS equipment_name
        FROM basic_sensor_instance i
        LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        LEFT JOIN eolm_monitor m ON i.monitor_id = m.id
        LEFT JOIN eolm_equipment e ON i.equipment_id = e.id
        WHERE i.is_deleted = 0 AND i.monitor_id = #{monitorId}
    </select>

    <select id="getSameTypeSensorCodeList" resultType="com.snszyk.zbusiness.basic.entity.SensorInstance">
        SELECT *
        FROM basic_sensor_instance
        WHERE is_deleted = 0 AND type_id = (SELECT type_id FROM basic_sensor_instance WHERE `code` = #{code})
            AND equipment_id IN
            <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </select>

    <select id="querySensorInfos" resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceDTO">
        SELECT i.*, t.`name`, t.model, t.supplier
        FROM basic_sensor_instance i
        LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        WHERE i.is_deleted = 0 AND i.monitor_id IN
            <foreach collection="list" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </select>

    <select id="detailByCode" resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceDTO">
        SELECT i.*, t.`name`, t.model, t.supplier, t.axis_count, t.is_wireless,t.category,
               m.`name` AS monitor_name, REPLACE(m.path_name,',','/') AS path_name, e.`name` AS equipment_name
        FROM basic_sensor_instance i
        LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        LEFT JOIN eolm_monitor m ON i.monitor_id = m.id
        LEFT JOIN eolm_equipment e ON i.equipment_id = e.id
        WHERE i.is_deleted = 0 AND i.code = #{code}
    </select>

    <select id="portalPage" resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceDTO">
        SELECT
            i.id,
            i.code,
            i.monitor_id,
            REPLACE(m.path_name,',','/') AS path_name,
            t.`name`,
            t.is_wireless
        FROM
            `basic_sensor_instance` i
        LEFT JOIN basic_sensor_type t ON t.id = i.type_id
        RIGHT JOIN eolm_monitor m ON i.monitor_id = m.id
        WHERE
            i.is_deleted = 0
          AND t.is_deleted = 0
          AND i.monitor_id != ''
        ORDER BY
            i.update_time DESC
    </select>

    <select id="listExport" resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceDTO">
        SELECT
            i.id,
            i.code,
            i.monitor_id,
            REPLACE(m.path_name,',','/') AS path_name,
            t.`name`,
            t.is_wireless
        FROM
            `basic_sensor_instance` i
        LEFT JOIN basic_sensor_type t ON t.id = i.type_id
        RIGHT JOIN eolm_monitor m ON i.monitor_id = m.id
        WHERE
            i.is_deleted = 0
          AND t.is_deleted = 0
          AND i.monitor_id != ''
        ORDER BY
            i.update_time DESC
    </select>

    <select id="listSensorInstanceBindExport"
            resultType="com.snszyk.zbusiness.basic.dto.SensorInstanceBindExcelDTO">
        SELECT REPLACE(m.path_name,',','/') as path_name, e.`name` AS equipment_name,
	        m.`name` AS monitor_name, i.`code` AS sensor_code, t.category, i.install_direction, i.number, i.phase,
	        t.`name` AS sensor_type_name, t.supplier, t.model, i.single_sample_interval, i.wave_sample_interval
        FROM eolm_monitor m
        LEFT JOIN eolm_equipment e on m.equipment_id = e.id
        LEFT JOIN basic_sensor_instance i ON m.id = i.monitor_id
        LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        WHERE m.is_deleted = 0
        ORDER BY m.path_name
    </select>

    <select id="equmentSensorList" resultType="com.snszyk.zbusiness.basic.dto.EqumentSensorListDTO" parameterType="com.snszyk.zbusiness.basic.vo.EqumentSensorListVO">
        SELECT e.name,GROUP_CONCAT(i.code) sensorCode,GROUP_CONCAT(i.id) sensorId,COUNT(*) num
        FROM `eolm_equipment` e JOIN `basic_sensor_instance` i ON e.id=i.equipment_id WHERE
            e.is_deleted=0 AND i.is_deleted=0
        <if test="path!=null and path!=''">
            AND e.path LIKE concat('%', #{path},'%')
        </if>
        GROUP BY e.name,e.id
    </select>

    <delete id="removeAlarmThresholdBySensorCode">
        DELETE FROM eolm_alarm_threshold WHERE sensor_code = #{sensorCode}
    </delete>

    <select id="equipmentSensor" resultType="com.snszyk.zbusiness.basic.dto.EquipmentSensorDTO">
        SELECT a.id,a.code,b.name,c.id paramId FROM `basic_sensor_instance` a JOIN `eolm_monitor` b ON a.monitor_id=b.id
                                                                              LEFT JOIN `basic_sensor_instance_param` c  ON a.id=c.instance_id AND sample_data_type='SPOWER'
        WHERE a.equipment_id=#{equipmentId} AND a.is_deleted=0 AND b.is_deleted=0 ORDER BY a.id
    </select>
</mapper>
