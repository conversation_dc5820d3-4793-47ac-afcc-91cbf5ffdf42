/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.CollectionStationChannel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采集站通道 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CollectionStationChannelVO对象", description = "CollectionStationChannelVO对象")
public class CollectionStationChannelVO extends CollectionStationChannel {

	private static final long serialVersionUID = 1L;

}
