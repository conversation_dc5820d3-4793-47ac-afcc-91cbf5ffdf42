package com.snszyk.zbusiness.basic.rabbit.handler;

import com.snszyk.common.constant.EolmConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


/**
 * 执行读取Rabbitmq
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j//
public class RabbitMQHandler {

	/**
	 * 记录命令及相关业务信息
	 */
	@Resource
	private Map<String, BusinessBean> commandBusiness;

	private final AtomicInteger threadPoolAtomic = new AtomicInteger(1);
	private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 10,
		0L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(),
		(r) -> new Thread(r, "[Thread]-Point-Value-Handler-" + threadPoolAtomic.getAndIncrement()));

	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_POINT_VALUE_EOLM)
	public void handler(MessageBean message) {
		//根据 command取相关业务处理Bean
		//调用Bean中的CommandExec
		//线程调用Bean中的业务处理
		BusinessBean businessBean = commandBusiness.get(message.getCommand());
		if (null == businessBean) {
			return;
		}

		boolean save = false;
		if (null != businessBean.getExec()) {
			save = businessBean.getExec().save(message);
		}

		if (!save || null == businessBean.getHandlers()) {
			return;
		}
		for (CommandBusiness commandBusiness : businessBean.getHandlers()) {
			threadPoolExecutor.execute(() -> commandBusiness.business(message));
		}

	}

}
