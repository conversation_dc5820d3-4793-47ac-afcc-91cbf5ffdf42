package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备异常-趋势预警配置
 *
 * <AUTHOR>
 * @date 2024-05-21 20:07
 */
@Data
@ApiModel(value = "TrendStrategyVO", description = "TrendStrategyVO")
public class TrendStrategyVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 短期趋势-基期
	 */
	@ApiModelProperty(value = "短期趋势-基期")
	private Integer shortTermBase;
	/**
	 * 短期趋势-末期
	 */
	@ApiModelProperty(value = "短期趋势-末期")
	private Integer shortTermLate;
	/**
	 * 是否启用短期趋势
	 */
	@ApiModelProperty(value = "是否启用短期趋势")
	private Integer isShortEnabled;
	/**
	 * 长期趋势-基期
	 */
	@ApiModelProperty(value = "长期趋势-基期")
	private Integer longTermBase;
	/**
	 * 长期趋势-末期
	 */
	@ApiModelProperty(value = "长期趋势-末期")
	private Integer longTermLate;
	/**
	 * 是否启用长期趋势
	 */
	@ApiModelProperty(value = "是否启用长期趋势")
	private Integer isLongEnabled;

}
