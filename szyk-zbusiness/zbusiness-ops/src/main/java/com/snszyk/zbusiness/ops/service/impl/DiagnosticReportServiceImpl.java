/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.ops.dto.DiagnosticReportDTO;
import com.snszyk.zbusiness.ops.entity.DiagnosticReport;
import com.snszyk.zbusiness.ops.mapper.DiagnosticReportMapper;
import com.snszyk.zbusiness.ops.service.IDiagnosticReportService;
import com.snszyk.zbusiness.ops.vo.DiagnosticReportVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 诊断报告表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Service
@AllArgsConstructor
public class DiagnosticReportServiceImpl extends BaseServiceImpl<DiagnosticReportMapper, DiagnosticReport> implements IDiagnosticReportService {

	@Override
	public IPage<DiagnosticReportDTO> page(IPage<DiagnosticReportDTO> page, DiagnosticReportVO diagnosticReport) {
		return page.setRecords(baseMapper.page(page, diagnosticReport));
	}

	@Override
	public DiagnosticReportDTO getByEquipmentId(Long equipmentId) {
		List<DiagnosticReport> list = this.lambdaQuery()
			.eq(DiagnosticReport::getEquipmentId, equipmentId)
			.list();
		return list.size() > 0 ? Objects.requireNonNull(BeanUtil.copy(list.get(0), DiagnosticReportDTO.class)) : null;
	}

}
