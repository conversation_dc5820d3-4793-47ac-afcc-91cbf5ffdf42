/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceParamDTO;
import com.snszyk.zbusiness.basic.dto.SensorTypeOnlineStatDTO;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;
import com.snszyk.zbusiness.basic.entity.SensorType;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.enums.*;
import com.snszyk.zbusiness.basic.service.ISensorInstanceParamService;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.ISensorTypeService;
import com.snszyk.zbusiness.basic.service.IWaveService;
import com.snszyk.zbusiness.basic.vo.SensorInstanceParamVO;
import com.snszyk.zbusiness.basic.vo.SensorInstanceVO;
import com.snszyk.zbusiness.basic.wrapper.SensorInstanceParamWrapper;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.common.enums.DictBizEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 传感器实例 逻辑服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SensorInstanceLogicService {

	private final ISensorInstanceService sensorInstanceService;
	private final ISensorInstanceParamService sensorInstanceParamService;
	private final ISensorTypeService sensorTypeService;
	private final IWaveService waveService;
	private final SzykRedis szykRedis;

	/**
	 * 传感器实例详情
	 *
	 * @param id id
	 */
	public SensorInstanceDTO detail(Long id) {
		// 传感器实例
		SensorInstance sensorInstance = sensorInstanceService.getById(id);
		if (sensorInstance == null) {
			throw new ServiceException("当前传感器已删除，请刷新后重试!");
		}
		SensorInstanceDTO sensorInstanceDTO = Objects.requireNonNull(BeanUtil.copy(sensorInstance, SensorInstanceDTO.class));

		// 获取传感器类型数据
		SensorType sensorType = sensorTypeService.getById(sensorInstanceDTO.getTypeId());
		sensorInstanceDTO.setName(sensorType.getName())
			.setSupplier(sensorType.getSupplier())
			.setSupplierName(DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, sensorType.getSupplier()))
			.setModel(sensorType.getModel())
			.setAxisCount(sensorType.getAxisCount())
			.setIsWireless(sensorType.getIsWireless());

		// 传感器实例参数列表
		List<SensorInstanceParam> sensorInstanceParamList = sensorInstanceParamService.list(Wrappers.<SensorInstanceParam>query()
			.lambda()
			.eq(SensorInstanceParam::getInstanceId, id)
			.orderByAsc(SensorInstanceParam::getCreateTime));
		if (Func.isNotEmpty(sensorInstanceParamList)) {
			sensorInstanceDTO.setSensorInstanceParamList(sensorInstanceParamList.stream()
				.map(param -> {
					SensorInstanceParamDTO paramDTO = Objects.requireNonNull(BeanUtil.copy(param, SensorInstanceParamDTO.class));
					if (Func.isNotEmpty(paramDTO.getVibrationType())) {
						paramDTO.setVibrationTypeName(DictBizCache.getValue(DictBizEnum.VIBRATION_TYPE, paramDTO.getVibrationType()));
					}
					if (Func.isNotEmpty(paramDTO.getSampleDataType())) {
						paramDTO.setSampleDataTypeName(DictBizCache.getValue(DictBizEnum.SAMPLED_DATA_TYPE, paramDTO.getSampleDataType()));
					}
					if (Func.isNotEmpty(paramDTO.getAxialDirection())) {
						paramDTO.setAxialDirectionName(DictBizCache.getValue(DictBizEnum.SENSOR_MEASURE_DIRECTION, paramDTO.getAxialDirection()));
					}
					if (Func.isNotEmpty(paramDTO.getFeature())) {
						paramDTO.setFeatureName(DictBizCache.getValue(DictBizEnum.SENSOR_FEATURE, paramDTO.getFeature()));
					}
					return paramDTO;
				}).collect(Collectors.toList()));
		}
		return sensorInstanceDTO;
	}

	/**
	 * 新增传感器实例 - 支持批量新增
	 *
	 * @param vo vo
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean save(SensorInstanceVO vo) {
		if (vo.getBatchAddCount() != null && vo.getBatchAddCount() > 0) {
			// 校验传感器编码列表长度
			if (Func.isNotEmpty(vo.getCodeList())) {
				if (vo.getCodeList().size() != vo.getBatchAddCount()) {
					throw new ServiceException("传感器编码列表长度与批量新增个数不一致!");
				}

				// 传感器编码唯一性
				if (vo.getCodeList().stream().distinct().count() != vo.getCodeList().size()) {
					throw new ServiceException("传感器编码列表中存在重复的编码!");
				}
			}

			// 批量新增
			for (int i = 0; i < vo.getBatchAddCount(); i++) {
				if (Func.isNotEmpty(vo.getCodeList())) {
					vo.setCode(vo.getCodeList().get(i));
				}
				saveSensorInstance(vo);
			}
		} else {
			// 单个新增
			saveSensorInstance(vo);
		}

		return true;
	}

	/**
	 * 新增传感器实例
	 *
	 * @param vo vo
	 */
	private void saveSensorInstance(SensorInstanceVO vo) {
		// 新增传感器实例
		SensorInstance sensorInstance = BeanUtil.copyProperties(vo, SensorInstance.class);
		assert sensorInstance != null;
		// 如果没有输入厂家提供的传感器编码，则后端生成
		if (Func.isEmpty(sensorInstance.getCode())) {
			throw new ServiceException("传感器编码不能为空!");
		}

		// 校验 code 唯一性
		if (sensorInstanceService.count(Wrappers.<SensorInstance>query().lambda()
			.eq(SensorInstance::getCode, sensorInstance.getCode())) > 0) {
			throw new ServiceException("传感器编码" + sensorInstance.getCode() + "已存在!");
		}

		// 批量添加时，清空上一次插入后id的赋值
		if (sensorInstance.getId() != null) {
			sensorInstance.setId(null);
		}

		boolean saveInstance = sensorInstanceService.save(sensorInstance);
		log.info("新增传感器实例：{}", saveInstance);
		if (!saveInstance) {
			throw new ServiceException("新增传感器实例失败!");
		}

		//新增传感器实例参数
		List<SensorInstanceParam> sensorInstanceParamList = vo.getSensorInstanceParamList().stream().map(param -> {
			SensorInstanceParam sensorInstanceParam = BeanUtil.copyProperties(param, SensorInstanceParam.class);
			assert sensorInstanceParam != null;
			// 批量添加时，清空上一次插入后id的赋值
			if (sensorInstanceParam.getId() != null) {
				sensorInstanceParam.setId(null);
			}
			sensorInstanceParam.setInstanceId(sensorInstance.getId());
			sensorInstanceParam.setCreateTime(DateUtil.now());
			return sensorInstanceParam;
		}).collect(Collectors.toList());
		boolean saveBatch = sensorInstanceParamService.saveBatch(sensorInstanceParamList);
		log.info("新增传感器实例参数列表：{}", saveBatch);
		if (!saveBatch) {
			throw new ServiceException("新增传感器实例参数列表失败!");
		}
	}

	/**
	 * 修改传感器实例
	 *
	 * @param vo vo
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean update(SensorInstanceVO vo) {
		// 校验 code 唯一性
		if (sensorInstanceService.count(Wrappers.<SensorInstance>query().lambda()
			.eq(SensorInstance::getCode, vo.getCode())
			.ne(Func.isNotEmpty(vo.getId()), SensorInstance::getId, vo.getId())) > 0) {
			throw new ServiceException("传感器编码" + vo.getCode() + "已存在!");
		}
		// 更新传感器实例
		SensorInstance sensorInstance = BeanUtil.copyProperties(vo, SensorInstance.class);
		boolean instanceUpdate = sensorInstanceService.updateById(sensorInstance);
		log.info("更新传感器实例：{}", instanceUpdate);
		// 更新传感器实例参数
		List<SensorInstanceParam> oldParamList = sensorInstanceParamService.list(Wrappers.<SensorInstanceParam>query().lambda()
			.eq(SensorInstanceParam::getInstanceId, vo.getId()));
		List<SensorInstanceParamVO> currentParamList = vo.getSensorInstanceParamList();
		// 找出已删除的传感器实例参数 - 删除
		for (SensorInstanceParam oldParam : oldParamList) {
			boolean isExist = currentParamList.stream()
				.filter(param -> Func.isNotEmpty(param.getId()))
				.anyMatch(param -> param.getId().equals(oldParam.getId()));
			if (!isExist) {
				//同步删除门限、波形（注意先后顺序）
				Wave wave = waveService.getOne(Wrappers.<Wave>lambdaQuery()
					.eq(Wave::getUnbind, 0)
					.eq(Wave::getSensorInstanceParamId, oldParam.getId()));
				if (wave != null) {
					// 删除门限
					int removeAlarmThreshold = waveService.removeAlarmThresholdByWave(Collections.singletonList(wave.getId()));
					log.info("删除传感器实例参数的门限：{}, sensorParamId = {}", removeAlarmThreshold, oldParam.getId());

					// 删除波形
					boolean removeWave = waveService.removeById(wave.getId());
					log.info("删除传感器实例参数的波形：{}, sensorParamId = {}", removeWave, oldParam.getId());
				}

				// 删除传感器实例参数
				boolean remove = sensorInstanceParamService.removeById(oldParam.getId());
				log.info("删除传感器实例参数：{}, id = {}", remove, oldParam.getId());
			}
		}
		// 更新或保存新的传感器参数
		List<SensorInstanceParam> newParamList = currentParamList.stream()
			.map(param -> {
				if (Func.isNotEmpty(param.getId())) {
					// 如果绑定了测点后修改了采样点数、采样频率，则同步修改波形
					if (sensorInstance.getMonitorId() != null) {
						oldParamList.stream()
							.filter(oldParam -> param.getId().equals(oldParam.getId()) && param.getVibrationType() == 1)
							.findFirst()
							.ifPresent(paramInDb -> {
								if (!paramInDb.getSamplingPoints().equals(param.getSamplingPoints())
									|| paramInDb.getSamplingFreq().compareTo(param.getSamplingFreq()) != 0) {
									updateVibrationWave(sensorInstance.getTypeId(), param);
								}
							});
					}
					return BeanUtil.copyProperties(param, SensorInstanceParam.class);
				} else {
					SensorInstanceParam sensorInstanceParam = BeanUtil.copyProperties(param, SensorInstanceParam.class);
					assert sensorInstanceParam != null;
					sensorInstanceParam.setInstanceId(vo.getId());
					sensorInstanceParam.setCreateTime(DateUtil.now());
					return sensorInstanceParam;
				}
			}).collect(Collectors.toList());
		boolean paramListUpdate = sensorInstanceParamService.saveOrUpdateBatch(newParamList);
		log.info("更新传感器实例参数：{}", paramListUpdate);
		return instanceUpdate && paramListUpdate;
	}

	/**
	 * 修改了采样点数、采样频率（振动、应力波）后，更新波形名称
	 * @param sensorTypeId 传感器类型id
	 * @param newParam 传感器实例参数
	 */
	private void updateVibrationWave(Long sensorTypeId, SensorInstanceParamVO newParam) {
		// 更新振动波形的名称
		Wave wave = waveService.getOne(Wrappers.<Wave>lambdaQuery()
			.eq(Wave::getUnbind, 0)
			.eq(Wave::getSensorInstanceParamId, newParam.getId()));
		if (wave != null) {
			// 仅温振一体和应力波的传感器
			SensorType sensorType = sensorTypeService.getById(sensorTypeId);
			if (SensorCategoryEnum.TEMP_VIBRATE.getCode().equals(sensorType.getCategory())
				|| SensorCategoryEnum.STRESS_WAVE.getCode().equals(sensorType.getCategory())) {
				String newWaveName = "";
				BigDecimal cutoffFreq = newParam.getSamplingFreq()
					.multiply(new BigDecimal("1000"))
					.divide(new BigDecimal("2.56"), 2, RoundingMode.HALF_UP);
				BigDecimal prefix = BigDecimal.valueOf(newParam.getSamplingPoints())
					.divide(BigDecimal.valueOf(1024), 2, RoundingMode.HALF_UP);
				if (SampledDataTypeEnum.ACCELERATION.getCode().equals(newParam.getSampleDataType())
					|| SampledDataTypeEnum.VELOCITY.getCode().equals(newParam.getSampleDataType())
					|| SampledDataTypeEnum.DISPLACEMENT.getCode().equals(newParam.getSampleDataType())) {
					// 振动波形（加速度、速度、位移） - 2K水平加速度(0.5-1000)
					newWaveName = prefix.stripTrailingZeros().toPlainString()
						+ "k " + MeasureDirectionEnum.getByCode(wave.getMeasureDirection()).getName()
						+ SampledDataTypeEnum.getByCode(newParam.getSampleDataType()).getName()
						+ "(" + sensorType.getInitialFreq().stripTrailingZeros().toPlainString()
						+ "-" + cutoffFreq.stripTrailingZeros().toPlainString() + ")";
				} else if (SampledDataTypeEnum.STRESS.getCode().equals(newParam.getSampleDataType())) {
					// 应力波波形 - 2K01应力波(0.5-1000)
					newWaveName = prefix.stripTrailingZeros().toPlainString() + "k " + wave.getNumber()
						+ SampledDataTypeEnum.getByCode(newParam.getSampleDataType()).getName()
						+ "(" + sensorType.getInitialFreq().stripTrailingZeros().toPlainString()
						+ "-" + cutoffFreq.stripTrailingZeros().toPlainString() + ")";
				}
				wave.setWaveName(newWaveName);
				boolean updateWave = waveService.updateById(wave);
				log.info("传感器实例参数（采样频率、采样点数）更新后，同步更新波形名称：{}", updateWave);
			}
		} else {
			log.warn("updateVibrationWave() - 未找到对应的Wave，sensorParamId = {}", newParam.getId());
		}
	}

	/**
	 * 带校验的删除
	 *
	 * @param ids ids
	 */
	public boolean removeSensorInstance(List<Long> ids) {
		// 判断传感器是否已绑定部位
		List<SensorInstance> list = sensorInstanceService.list(Wrappers.<SensorInstance>query().lambda()
			.in(SensorInstance::getId, ids)
			.isNotNull(SensorInstance::getMonitorId));
		if (Func.isNotEmpty(list)) {
			throw new ServiceException("传感器实例已绑定部位，无法删除!");
		}

		return sensorInstanceService.deleteLogic(ids);
	}


	/**
	 * 分页
	 * @param page 分页
	 * @param vo vo
	 * @return
	 */
	public IPage<SensorInstanceDTO> page(IPage<SensorInstanceDTO> page, SensorInstanceVO vo) {
		return sensorInstanceService.page(page, vo);
	}

	/**
	 * 生成唯一传感器编码
	 * @param ids ids
	 * @return
	 */
	public List<String> generateSensorCode(List<Long> ids) {
		return ids.stream()
			.map(id -> BizCodeUtil.generate(AuthUtil.getTenantId() + "SC", BizCodeUtil.SIMPLE_DATE_FORMAT))
			.collect(Collectors.toList());
	}

	/**
	 * 根据code获取传感器实例详情
	 * @param code 传感器编码
	 * @return
	 */
	public SensorInstanceDTO detailByCode(String code) {
		return sensorInstanceService.detailByCode(code);
	}

	/**
	 * 根据传感器实例id获取传感器参数
	 *
	 * @param id
	 * @return java.util.List<com.snszyk.zbusiness.basic.vo.SensorInstanceParamVO>
	 * <AUTHOR>
	 * @date 2024/2/4 16:31
	 */
	public List<SensorInstanceParamVO> getSensorInstanceParams(Long id) {
		List<SensorInstanceParamVO> resultList = null;
		List<SensorInstanceParam> list = sensorInstanceParamService.list(Wrappers.<SensorInstanceParam>query().lambda()
			.eq(SensorInstanceParam::getInstanceId, id).isNull(SensorInstanceParam::getFeature));
		if (Func.isNotEmpty(list)) {
			resultList = SensorInstanceParamWrapper.build().listVO(list);
			resultList.forEach(paramVO -> {
				List<SensorInstanceParamVO> children = new ArrayList<>();
				if (VibrationTypeEnum.IS_VIBRATION == VibrationTypeEnum.getByCode(paramVO.getVibrationType())) {
					switch (SampledDataTypeEnum.getByCode(paramVO.getSampleDataType())) {
						case ACCELERATION:
						case STRESS:
							children = Arrays.asList(NonVibrationDataEnum.values()).stream().map(nonVibrationData -> {
								SensorInstanceParamVO vo = new SensorInstanceParamVO(nonVibrationData.getCode(),
									nonVibrationData.getName());
								return vo;
							}).collect(Collectors.toList());
							break;
						case VELOCITY:
							SensorInstanceParamVO peakValueVO = new SensorInstanceParamVO(NonVibrationDataEnum.PEAK_VALUE.getCode(),
								NonVibrationDataEnum.PEAK_VALUE.getName());
							children.add(peakValueVO);
							SensorInstanceParamVO peakPeakValueVO = new SensorInstanceParamVO(NonVibrationDataEnum.PEAK_PEAK_VALUE.getCode(),
								NonVibrationDataEnum.PEAK_PEAK_VALUE.getName());
							children.add(peakPeakValueVO);
							break;
						case DISPLACEMENT:
							peakValueVO = new SensorInstanceParamVO(NonVibrationDataEnum.PEAK_PEAK_VALUE.getCode(),
								NonVibrationDataEnum.PEAK_PEAK_VALUE.getName());
							children = Arrays.asList(peakValueVO);
							break;
						case ELECTRIC:
							SensorInstanceParamVO effectiveValueVO = new SensorInstanceParamVO(NonVibrationDataEnum.EFFECTIVE_VALUE.getCode(),
								NonVibrationDataEnum.EFFECTIVE_VALUE.getName());
							children = Arrays.asList(effectiveValueVO);
							break;
						default:
					}
				} else {
					SensorInstanceParamVO vo = Objects.requireNonNull(BeanUtil.copy(paramVO, SensorInstanceParamVO.class));
					vo.setFeature(paramVO.getSampleDataType());
					children = Arrays.asList(vo);
				}
				paramVO.setChildren(children);
			});
		}
		return resultList;
	}

	/**
	 * 传感器数据概览
	 *
	 * @return com.snszyk.zbusiness.basic.dto.SensorTypeOnlineStatDTO
	 * <AUTHOR>
	 * @date 2024/2/18 15:56
	 */
	public SensorTypeOnlineStatDTO onlineStat() {
		Integer typeCount = sensorTypeService.count();
		Integer instanceCount = sensorInstanceService.count();
		SensorTypeOnlineStatDTO dto = new SensorTypeOnlineStatDTO(typeCount, instanceCount);
		Set<String> sensorOnlineKeys = szykRedis.keys(StringPool.ASTERISK + SampledDataTypeEnum.SENSOR_ONLINE.getCode());
		// 无线传感器实例在线数量
		Integer onlineCount = sensorOnlineKeys.stream().filter(key -> !key.contains("dictBiz")).collect(Collectors.summingInt(key -> 1));
		// 无线传感器实例总数
		dto.setOnlineRate(new BigDecimal(onlineCount * 100 / instanceCount).setScale(0, RoundingMode.HALF_UP));
		Set<String> sensorPowerKeys = szykRedis.keys(StringPool.ASTERISK + SampledDataTypeEnum.SENSOR_POWER.getCode());
		// 低电量传感器实例数量
		Integer lowPowerCount = 0;
		for (String key : sensorPowerKeys) {
			if (!key.contains("dictBiz") && Func.toDouble(szykRedis.get(key)) <= Double.valueOf(StringPool.ONE + StringPool.ZERO)) {
				lowPowerCount++;
			}
		}
		dto.setLowPowerCount(lowPowerCount);
		return dto;
	}

}
