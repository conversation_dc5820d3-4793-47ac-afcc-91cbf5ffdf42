<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.DisplayPositionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="displayPositionResultMap" type="com.snszyk.zbusiness.basic.entity.DisplayPosition">
        <result column="id" property="id"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="sensor_code" property="sensorCode"/>
        <result column="sensor_param" property="position"/>
        <result column="category" property="category"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <delete id="removeByEquipment">
        delete from eolm_display_position where equipment_id = #{equipmentId}
    </delete>

    <delete id="removeByMonitor">
        delete from eolm_display_position where monitor_id = #{monitorId}
    </delete>

    <delete id="removeBySensorCode">
        delete from eolm_display_position where sensor_code = #{sensorCode}
        <if test="category!=null">
            and category = #{category}
        </if>
    </delete>

</mapper>
