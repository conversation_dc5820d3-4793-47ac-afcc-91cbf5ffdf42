/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.basic.dto.SensorTypeDTO;
import com.snszyk.zbusiness.basic.entity.SensorType;
import com.snszyk.zbusiness.basic.vo.SensorTypeVO;

/**
 * 传感器类型表 服务类
 *
 * <AUTHOR>
 */
public interface ISensorTypeService extends BaseService<SensorType> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<SensorTypeDTO> page(IPage<SensorTypeDTO> page, SensorTypeVO vo);

}
