/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 波形标注表实体类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@Accessors(chain = true)
@TableName("eolm_wave_mark")
@ApiModel(value = "WaveMark对象", description = "波形标注表")
public class WaveMark implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;
	/**
	 * 波形id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "波形id")
	private Long waveId;
	/**
	 * 转速
	 */
	@ApiModelProperty(value = "转速")
	private BigDecimal speed;
	/**
	 * 齿数
	 */
	@ApiModelProperty(value = "齿数")
	private Integer teethNumber;
	/**
	 * 齿轮啮合频率
	 */
	@ApiModelProperty(value = "齿轮啮合频率")
	private BigDecimal meshingFreq;
	/**
	 * 轴承id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "轴承id")
	private Long bearingId;
	/**
	 * 特征频率
	 */
	@ApiModelProperty(value = "特征频率")
	private String characteristicFreq;
	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Integer sort;
	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	private Date createTime;


}
