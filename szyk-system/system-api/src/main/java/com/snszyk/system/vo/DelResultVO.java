package com.snszyk.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "DelResultVO对象", description = "DelResultVO对象")
public class DelResultVO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "删除成功数量")
	private Integer successNumber;

	@ApiModelProperty(value = "删除失败数量")
	private Integer failureNumber;

	@ApiModelProperty(value = "删除失败信息列表")
	private List<DelDetailVO> detailVOList;

	public DelResultVO() {
		this.successNumber = 0;
		this.failureNumber = 0;
		this.detailVOList = new ArrayList<>();
	}
}
