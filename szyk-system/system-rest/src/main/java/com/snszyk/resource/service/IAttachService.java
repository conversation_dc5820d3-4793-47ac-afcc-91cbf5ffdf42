/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.core.oss.model.SzykFile;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.vo.AttachVO;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;

/**
 * 附件表 服务类
 *
 * <AUTHOR>
 */
public interface IAttachService extends BaseService<Attach> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param attach
	 * @return
	 */
	IPage<AttachVO> selectAttachPage(IPage<AttachVO> page, AttachVO attach);

	/**
	 * 下载文件弹窗选择下载位置
	 *
	 * <AUTHOR>
	 * @date 2022/06/17 14:13
	 * @param attachId
	 * @param response
	 * @return void
	 */
	void download(Long attachId, HttpServletResponse response);
	/**
	 * 上传文件并保存至服务器
	 *
	 * @param fileName
	 * @param fileType
	 * @param inputStream
	 * @return
	 */
	SzykFile putFile(String fileName, String fileType, InputStream inputStream);
}
