<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.ai.mapper.StandardDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="standardDictResultMap" type="com.snszyk.zbusiness.ai.entity.StandardDict">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="name" property="name"/>
        <result column="device_category" property="deviceCategory"/>
        <result column="sample_data_type" property="sampleDataType"/>
        <result column="quota_type" property="quotaType"/>
        <result column="apply_equipment" property="applyEquipment"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="first_threshold_lower" property="firstThresholdLower"/>
        <result column="first_threshold_upper" property="firstThresholdUpper"/>
        <result column="second_threshold_lower" property="secondThresholdLower"/>
        <result column="second_threshold_upper" property="secondThresholdUpper"/>
        <result column="third_threshold_lower" property="thirdThresholdLower"/>
        <result column="third_threshold_upper" property="thirdThresholdUpper"/>
        <result column="fourth_threshold_lower" property="fourthThresholdLower"/>
        <result column="fourth_threshold_upper" property="fourthThresholdUpper"/>
        <result column="power_upper" property="powerUpper"/>
        <result column="power_lower" property="powerLower"/>
        <result column="type" property="type"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="standardDictVoResultMap" type="com.snszyk.zbusiness.ai.vo.StandardDictVO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="device_category" property="deviceCategory"/>
        <result column="quota_type" property="quotaType"/>
        <result column="quota_name" property="quotaName"/>
        <result column="alarm_type" property="alarmType"/>
        <result column="power_upper" property="powerUpper"/>
        <result column="power_lower" property="powerLower"/>
        <result column="type" property="type"/>
        <result column="sort" property="sort"/>
    </resultMap>


    <select id="page" resultMap="standardDictResultMap">
        select * from eolm_standard_dict where is_deleted = 0 and type = #{standardDict.type}
        <if test="standardDict.vibrationType == 0 or standardDict.vibrationType == 2">
            and sample_data_type = #{standardDict.sampleDataType}
        </if>
        <if test="standardDict.vibrationType == 1 and standardDict.type == 2">
            and sample_data_type in ('ACCELERATION','VELOCITY','DISPLACEMENT')
        </if>
        <if test="standardDict.deviceCategory!=null">
            and device_category = #{standardDict.deviceCategory}
        </if>
        <if test="standardDict.applyEquipment!=null">
            and apply_equipment = #{standardDict.applyEquipment}
        </if>
        order by create_time desc
    </select>

    <select id="validatePower" resultType="java.lang.Integer">
        select count(1)
        from eolm_standard_dict
        where is_deleted = 0 and type = 1
          and device_category = #{deviceCategory}
          and tenant_id = #{tenantId}
          and quota_type = #{quotaType}
          and (
                (power_lower &gt;= #{powerLower} and power_lower &lt; #{powerUpper}) or
                (power_lower &lt;= #{powerLower} and power_upper &gt;= #{powerLower})
            )
          <if test="id != null">
              and id != #{id}
          </if>
    </select>

</mapper>
