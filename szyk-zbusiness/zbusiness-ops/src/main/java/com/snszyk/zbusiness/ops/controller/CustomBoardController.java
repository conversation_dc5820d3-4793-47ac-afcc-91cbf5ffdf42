package com.snszyk.zbusiness.ops.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.ops.dto.CustomBoardDTO;
import com.snszyk.zbusiness.ops.service.CustomBoardLogicService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/customboard")
@Api(value = "客户看板", tags = "客户看板接口")
public class CustomBoardController {

    private final CustomBoardLogicService customBoardLogicService;

    /**
     * 分页
     */
    @GetMapping("/page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "租户id", paramType = "query", dataType = "string")
    })
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "分页", notes = "传入vo")
    public R<IPage<CustomBoardDTO>> page(String tenantId, Query query) {
        return R.data(customBoardLogicService.page(tenantId, query));
    }
}
