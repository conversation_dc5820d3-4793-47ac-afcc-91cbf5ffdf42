/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.ai.dto.AiEquipmentAlarmDto;
import com.snszyk.zbusiness.ai.dto.AiEquipmentDto;
import com.snszyk.zbusiness.ai.dto.AiMonitorDto;
import com.snszyk.zbusiness.ai.dto.AiWaveDto;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningMonitorVO;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningStatVO;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
public interface EquipmentMapper extends BaseMapper<Equipment> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param equipment
	 * @return
	 */
	List<EquipmentDTO> page(IPage page, @Param("equipment") EquipmentVO equipment);

	/**
	 * 批量物理删除
	 *
	 * @param ids
	 * @return
	 */
	Integer removeByIds(@Param("list") List<Long> ids);

	/**
	 * 设备运行情况统计
	 * @param page page
	 * @param vo vo
	 * @param tenantId 租户
	 * @return
	 */
    List<EquipmentRunningStatExcelDTO> equipmentRunningPage(IPage<EquipmentRunningStatExcelDTO> page,
															@Param("vo") EquipmentRunningStatVO vo,
															@Param("tenantId") String tenantId);

	/**
	 * 设备统计（总数、运行数、停机数）
	 * @param tenantId 租户
	 * @return
	 */
	@InterceptorIgnore(tenantLine = "1")
    EquipmentStatDTO equipmentStat(@Param("tenantId") String tenantId);

	/**
	 * 获取指定设备同category（同tenantId）的设备id
	 * @param equipmentId 设备id
	 * @return
	 */
    List<Long> getIdListOfSameCategory(@Param("equipmentId") Long equipmentId);

	/**
	 * 获取最大sort值
	 * @return
	 */
    Integer getMaxSort();

	/**
	 * 重置设备的needSpotCheck
	 * @param equipmentIdList 设备id列表
	 * @return
	 */
	int resetEquipmentNeedSpotCheck(@Param("list") List<Long> equipmentIdList);

	/**
	 * 查询设备的累计报警数
	 * @param equipmentId 设备id
	 * @return
	 */
    Integer alarmTimes(@Param("equipmentId") Long equipmentId);

	/**
	 * 查询设备的累计故障数
	 * @param equipmentId 设备id
	 * @return
	 */
	Integer faultTimes(@Param("equipmentId") Long equipmentId);

	/**
	 * 根据monitorId查询设备
	 * @param monitorId monitorId
	 * @return
	 */
    Equipment getByMonitorId(@Param("monitorId") Long monitorId);


	/**
	 * 异常设备统计
	 * @return
	 */
	AbnormalEquipmentTotal abnormalEquipmentTotal();

	/**
	 * 重点关注设备
	 * @return
	 */
	List<ImportantEquipment> importantEquipment(@Param("equipmentId") Long equipmentId);

	/**
	 * 设备故障类型统计
	 * @return
	 */
	List<EquipmentTypeTotal> equipmentTypeTotal(@Param("equipmentId") Long equipmentId);


	/**
	 * 设备频发次数统计
	 * @return
	 */
	List<EquipmentFrequency> equipmentFrequency();

	/**
	 * 异常状态分布统计
	 * @return
	 */
	List<AbnormalScatter> abnormalScatter(@Param("equipmentId") Long equipmentId);

	List<EquipmentRunningMonitorDTO> equipmentRunningMonitor(IPage<EquipmentRunningMonitorDTO> page,@Param("equipment") EquipmentRunningMonitorVO equipment);

	/**
	 * 设备异常信息列表
	 * @param equipmentId
	 * @return
	 */
	List<EquipmentMonitorAbnormalDTO> equipmentMonitorAbnormal(@Param("equipmentId") Long equipmentId);

	/**
	 * 查询设备台帐
	 * @param page
	 * @param equipment
	 * @return
	 */
	List<EquipmentDTO>  equipmentLedger(IPage<EquipmentDTO> page,@Param("equipment") EquipmentVO equipment);
	AiEquipmentDto getEquipmentInfo(@Param("equipmentId") Long equipmentId);

	AiEquipmentDto getEquipmentByMonitorId(@Param("monitorId") Long monitorId);

	List<AiMonitorDto> getMonitorList(@Param("equipmentId") Long equipmentId);

	List<AiWaveDto> getWaveList(@Param("monitorId") Long monitorId);

	List<AiWaveDto> waveList(@Param("monitorId") Long monitorId);

	AiEquipmentAlarmDto equipmentAlarm(@Param("equipmentId") Long equipmentId);

}
