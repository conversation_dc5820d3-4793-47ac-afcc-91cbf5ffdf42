/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 报警统计数据
 *
 * <AUTHOR>
 * @since 2023-06-06
 **/
@Data
@ApiModel(value = "AlarmStatisticDTO对象", description = "AlarmStatisticDTO对象")
public class AlarmStatisticsDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "区域id")
	private Long regionId;

	@ApiModelProperty(value = "组织全称")
	private String orgFullName;

	@ApiModelProperty(value = "组织名称")
	private String orgName;

	@ApiModelProperty(value = "组织路径")
	private String orgPath;

	@ApiModelProperty(value = "设备数")
	private Integer deviceCount;

	@ApiModelProperty(value = "总报警数")
	private Integer alarmCount;

	@ApiModelProperty(value = "是否有子集")
	private Integer hasChildren;

	@ApiModelProperty(value = "已成缺陷")
	private Integer isFaultCount;

	@ApiModelProperty(value = "报警等级数据集合")
	private Map<Integer, Integer> alarmLevelCount;

	@ApiModelProperty(value = "报警状态数据集合")
	private Map<Integer, Integer> alarmStateCount;

	@ApiModelProperty(value = "接报警超时率")
	private BigDecimal alarmTimeoutCount;

	@ApiModelProperty(value = "处理率")
	private BigDecimal handleRate;

	@ApiModelProperty(value = "闭环率")
	private BigDecimal completionRate;

	@ApiModelProperty(value = "区域层级")
	private Integer regionLevel;

	@ApiModelProperty(value = "统计图数据")
	private AlarmStatisticsChartDTO alarmStatisticsChart;

}
