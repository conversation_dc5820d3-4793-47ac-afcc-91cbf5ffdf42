/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.AiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI模型表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AiModelDTO extends AiModel {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;

	/**
	 * 设备路径名称
	 */
	@ApiModelProperty(value = "设备路径名称")
	private String equipmentPathName;

}
