/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备测点参数-齿轮信息视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Data
@ApiModel(value = "ParamGearVO对象", description = "设备测点参数-齿轮信息")
public class ParamBearingVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 轴承id
	 */
	@ApiModelProperty(value = "轴承id")
	private Long id;
	/**
	 * 轴承名称
	 */
	@ApiModelProperty(value = "轴承名称")
	private String bearingName;
	/**
	 * 轴承型号
	 */
	@ApiModelProperty(value = "轴承型号")
	private String bearingModel;

}
