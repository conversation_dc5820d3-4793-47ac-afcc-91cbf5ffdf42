package com.snszyk.message.dto;

import com.snszyk.message.entity.MessagePush;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息推送dto
 *
 * <AUTHOR> Baomingming
 * @create 2022/8/1
 */
@Data
@ApiModel
@EqualsAndHashCode(callSuper = true)
public class MessagePushDto extends MessagePush {

	/**
	 * 消息标题
	 */
	@ApiModelProperty("消息标题")
	private String title;

	/**
	 * 消息内容
	 */
	@ApiModelProperty("消息内容")
	private String content;
	/**
	 * 发送人
	 */
	@ApiModelProperty("发送人")
	private String sender;

	/**
	 * 发送人姓名
	 */
	@ApiModelProperty("发送人姓名")
	private String senderName;
}
