# 诊断报告消息发送功能说明

## 1. 功能概述

诊断报告消息发送功能用于在诊断报告发布时，自动向相关用户发送消息通知。消息内容包括报告类型、报告名称、数据时间范围、发布时间、发布人等信息，以及报告的附件链接。

## 2. 实现原理

### 2.1 消息发送流程

```mermaid
flowchart TD
    A[诊断报告发布] --> B[更新报告状态]
    B --> C{更新成功?}
    C -->|是| D[获取报告详情]
    C -->|否| E[返回发布失败]
    D --> F[构建消息内容]
    F --> G[设置接收人信息]
    G --> H[发送消息]
    H --> I{发送成功?}
    I -->|是| J[记录发送成功日志]
    I -->|否| K[记录发送失败日志]
    J --> L[返回发布成功]
    K --> L
```

### 2.2 消息内容结构

消息内容以JSON格式存储，包含以下字段：

| 字段名 | 类型 | 说明 |
| ----- | ---- | ---- |
| reportId | Long | 报告ID |
| title | String | 报告标题 |
| type | Integer | 报告类型编码 |
| typeName | String | 报告类型名称 |
| dataStartDate | String | 数据开始日期，格式：yyyy-MM-dd |
| dataEndDate | String | 数据结束日期，格式：yyyy-MM-dd |
| publishTime | String | 发布时间，格式：yyyy-MM-dd HH:mm:ss |
| publisher | String | 发布人姓名 |
| attachId | Long | 附件ID（如果有） |

## 3. 代码结构

### 3.1 核心类

- **DiagnosisReportMessageService**: 诊断报告消息服务，负责构建和发送消息
- **AiopsDiagnosisReportServiceImpl**: 诊断报告服务实现类，在发布报告时调用消息服务
- **AiopsDiagnosisReportLogicService**: 诊断报告逻辑服务，处理业务逻辑

### 3.2 关键方法

- **DiagnosisReportMessageService.sendDiagnosisReportMessage**: 发送诊断报告消息
- **DiagnosisReportMessageService.buildMessageContent**: 构建消息内容
- **DiagnosisReportMessageService.setReceiverInfo**: 设置接收人信息
- **AiopsDiagnosisReportServiceImpl.publish**: 发布报告并发送消息

## 4. 使用方法

### 4.1 发布报告

通过调用`AiopsDiagnosisReportLogicService.publish(Long id)`方法发布报告，系统会自动发送消息通知。

```java
// 示例代码
@Autowired
private AiopsDiagnosisReportLogicService reportLogicService;

// 发布报告
boolean result = reportLogicService.publish(reportId);
```

### 4.2 自定义接收人

目前系统默认将消息发送给当前用户。如需自定义接收人，可以修改`DiagnosisReportMessageService.setReceiverInfo`方法，从消息设置中获取配置的接收人信息。

## 5. 测试方法

### 5.1 单元测试

使用`DiagnosisReportMessageServiceTest`类进行单元测试，测试消息发送功能是否正常。

### 5.2 集成测试

1. 创建一个新的诊断报告
2. 发布该报告
3. 检查消息列表，确认是否收到通知

## 6. 注意事项

- 消息发送失败不会影响报告发布状态，系统会记录错误日志
- 消息内容中的日期格式为：yyyy-MM-dd，时间格式为：yyyy-MM-dd HH:mm:ss
- 如果报告有附件，消息会包含附件链接
- 消息类型为系统消息（SYSTEM）
- 消息业务类型为报告管理（REPORT_MANAGEMENT）
