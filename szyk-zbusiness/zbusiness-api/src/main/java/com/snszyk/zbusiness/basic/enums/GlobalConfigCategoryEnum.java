package com.snszyk.zbusiness.basic.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设置参数类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum GlobalConfigCategoryEnum {

	/**
	 * 报警周期
	 */
	ALARM_PERIOD("ALARM_PERIOD", "报警周期"),

	/**
	 * 润滑策略
	 */
	LUBRICATE_STRATEGY("LUBRICATE_STRATEGY", "润滑策略"),

	/**
	 * 设备异常策略
	 */
	ABNORMAL_STRATEGY("ABNORMAL_STRATEGY", "设备异常策略"),
	;
	private String code;
	private String msg;

	public static GlobalConfigCategoryEnum getByCode(String code){
		for (GlobalConfigCategoryEnum value : GlobalConfigCategoryEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}
}
