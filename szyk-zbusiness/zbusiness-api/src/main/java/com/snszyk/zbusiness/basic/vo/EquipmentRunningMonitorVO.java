package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备运行状态统计vo
 * <AUTHOR>
 */
@Data
@ApiModel(value = "设备运行监控", description = "设备运行监控")
public class EquipmentRunningMonitorVO implements Serializable {

	private static final long serialVersionUID = 1L;
	//分类
	private String category;
	//报警等级
	private Integer alarmLevel;
	//异常等级
	private Integer failLevel;
	//厂区
	private String path;
	//是否重点关注
	private Integer important;

	//设备ID
	private Long id;

}
