/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.basic.dto.TreeDTO;
import com.snszyk.zbusiness.basic.entity.BasicTree;
import com.snszyk.zbusiness.basic.vo.BasicTreeVO;

import java.util.List;
import java.util.Map;

/**
 * 基础树表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
public interface BasicTreeMapper extends BaseMapper<BasicTree> {

	/**
	 * 懒加载基础树列表
	 *
	 * @param tenantId
	 * @param parentId
	 * @param param
	 * @return
	 */
	List<BasicTreeVO> lazyList(String tenantId, Long parentId, Map<String, Object> param);

	/**
	 * 获取树形节点
	 *
	 * @param tenantId
	 * @return
	 */
	List<BasicTreeVO> tree(String tenantId);

	/**
	 * 懒加载获取树形节点
	 *
	 * @param tenantId
	 * @param parentId
	 * @return
	 */
	List<BasicTreeVO> lazyTree(String tenantId, Long parentId);

	/**
	 * 获取节点名
	 *
	 * @param ids
	 * @return
	 */
	List<String> getNodeNames(Long[] ids);

	/**
	 * 地点下拉树
	 *
	 * @param tenantId
	 * @param parentId
	 * @return
	 */
	List<BasicTreeVO> selectDeviceTree(String tenantId, Long parentId);

	/**
	 * 驾驶舱设备树
	 *
	 * @param hasTop
	 * @param parentId
	 * @return
	 */
	List<BasicTreeVO> cockpitDeviceTree(Integer hasTop, Long parentId);

	List<TreeDTO> areaList();
}
