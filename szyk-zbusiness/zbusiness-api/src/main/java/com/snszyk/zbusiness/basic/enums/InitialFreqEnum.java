/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 起始频率枚举类
 *
 * <AUTHOR>
 * @date 2023/08/15 15:56
 **/
@Getter
@AllArgsConstructor
public enum InitialFreqEnum {

	/**
	 * 0.5
	 */
	NON_VIBRATION(0, "0.5"),

	/**
	 * 1.0
	 */
	IS_VIBRATION(1, "1.0");

	final Integer code;
	final String value;

	public static InitialFreqEnum getByCode(Integer code){
		for (InitialFreqEnum value : InitialFreqEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
