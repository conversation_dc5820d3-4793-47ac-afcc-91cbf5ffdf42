/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.mp.support.SzykPage;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.DisplayPosition;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.enums.DimensionCategoryEnum;
import com.snszyk.zbusiness.basic.service.IAiModelService;
import com.snszyk.zbusiness.basic.service.IDisplayPositionService;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.service.logic.EquipmentLogicService;
import com.snszyk.zbusiness.basic.vo.*;
import com.snszyk.zbusiness.basic.wrapper.DisplayPositionWrapper;
import com.snszyk.zbusiness.basic.wrapper.EquipmentWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;


/**
 * 设备信息表 控制器
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/equipment")
@Api(value = "设备信息表", tags = "设备信息表接口")
public class EquipmentController extends SzykController {

	private final IEquipmentService equipmentService;
	private final EquipmentLogicService equipmentLogicService;
	private final IDisplayPositionService displayPositionService;
	private final IAiModelService aiModelService;

	/**
	 * 详情
	 */
	@GetMapping("{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<EquipmentDTO> detail(@PathVariable Long id) {
		return R.data(equipmentLogicService.detail(id));
	}

	/**
	 * 列表 设备信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入equipment")
	public R<IPage<EquipmentVO>> list(Equipment equipment, Query query) {
		IPage<Equipment> pages = equipmentService.page(Condition.getPage(query), Condition.getQueryWrapper(equipment));
		return R.data(EquipmentWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 设备信息表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deviceId", value = "位置id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "keywords", value = "设备编号或名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入equipment")
	public R<IPage<EquipmentDTO>> page(@ApiIgnore EquipmentVO equipment, Query query) {
		IPage<EquipmentDTO> pages = equipmentLogicService.page(Condition.getPage(query), equipment);
		return R.data(pages);
	}

	/**
	 * 新增 设备
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入equipment")
	public R save(@Valid @RequestBody Equipment equipment) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(equipmentService.save(equipment));
	}

	/**
	 * 修改 设备
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入equipment")
	public R update(@Valid @RequestBody Equipment equipment) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(equipmentService.updateById(equipment));
	}

	/**
	 * 新增或修改 设备信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入equipment")
	public R<Long> submit(@Valid @RequestBody EquipmentVO equipment) {
		CacheUtil.clear(SYS_CACHE);
		return R.data(equipmentLogicService.submit(equipment));
	}

	/**
	 * 删除 设备信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(equipmentLogicService.removeByIds(Func.toLongList(ids)));
	}

	/**
	 * 校验并删除 设备信息表
	 */
	@PostMapping("/check-remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "校验并删除", notes = "传入ids")
	public R<DelResultVO> checkAndRemove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		DelResultVO delResultVO = equipmentService.checkAndRemoveEquipment(Func.toLongList(ids));
		R<DelResultVO> result = new R<>();
		result.setCode(ResultCode.SUCCESS.getCode());
		result.setData(delResultVO);
		result.setSuccess(delResultVO.getFailureNumber() == 0);
		return result;
	}

	/**
	 * 门户-设备列表
	 */
	@GetMapping("/portalPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deviceId", value = "点位id", required = true, paramType = "query", dataType = "Long")
	})
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "门户-设备列表", notes = "传入equipment")
	public R<IPage<EquipmentDTO>> portalPage(@ApiIgnore EquipmentVO equipment, Query query) {
		IPage<EquipmentDTO> pages = equipmentLogicService.portalPage(Condition.getPage(query), equipment);
		return R.data(pages);
	}

	/**
	 * 设备台账分页 设备信息表
	 */
	@GetMapping("/equipmentAccountPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deviceId", value = "点位id", readOnly = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "isFault", value = "故障状态（字典：device_status）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "alarmLevel", value = "报警等级（字典：alarm_level）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "grade", value = "设备等级（字典：equipment_grade）", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "设备台账分页", notes = "传入equipment")
	public R<IPage<EquipmentDTO>> equipmentAccountPage(@ApiIgnore EquipmentVO equipment, Query query) {
		IPage<EquipmentDTO> pages = equipmentLogicService.page(Condition.getPage(query), equipment);
		return R.data(pages);
	}

	/**
	 * 虚拟展示位置详情 虚拟展示位置表
	 */
	@GetMapping("/positionDetail")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "3D展示位置详情", notes = "传入equipmentId")
	public R<DisplayPositionVO> positionDetail(Long equipmentId) {
		DisplayPosition detail = displayPositionService.getOne(Wrappers.<DisplayPosition>query().lambda()
			.eq(DisplayPosition::getEquipmentId, equipmentId).eq(DisplayPosition::getCategory, DimensionCategoryEnum.THREE_DIMENSION.getCode()));
		if(Func.isEmpty(detail)){
			return R.data(null);
		}
		return R.data(DisplayPositionWrapper.build().entityVO(detail));
	}

	/**
	 * 保存虚拟展示位置 虚拟展示位置表
	 */
	@PostMapping("/savePosition")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "保存3D展示位置", notes = "传入displayPosition")
	public R savePosition(@Valid @RequestBody DisplayPositionVO displayPosition) {
		DisplayPosition entity = displayPositionService.getOne(Wrappers.<DisplayPosition>query().lambda()
			.eq(DisplayPosition::getEquipmentId, displayPosition.getEquipmentId())
			.eq(DisplayPosition::getCategory, DimensionCategoryEnum.THREE_DIMENSION.getCode()));
		if(Func.isEmpty(entity)){
			entity = Objects.requireNonNull(BeanUtil.copy(displayPosition, DisplayPosition.class));
		}
		entity.setPosition(displayPosition.getPosition()).setCategory(DimensionCategoryEnum.THREE_DIMENSION.getCode())
			.setCreateUser(AuthUtil.getUserId()).setCreateTime(DateUtil.now());
		return R.status(displayPositionService.saveOrUpdate(entity));
	}

	/**
	 * 2D展示位置详情 虚拟展示位置表
	 */
	@GetMapping("/planarPositionDetail")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "2D展示位置详情", notes = "传入equipmentId")
	public R<List<DisplayPositionVO>> planarPositionDetail(String equipmentId) {
		List<DisplayPosition> list = displayPositionService.list(Wrappers.<DisplayPosition>query().lambda()
			.eq(DisplayPosition::getEquipmentId, equipmentId).eq(DisplayPosition::getCategory, DimensionCategoryEnum.TWO_DIMENSION.getCode()));
		if(Func.isEmpty(list)){
			return R.data(null);
		}
		return R.data(DisplayPositionWrapper.build().listVO(list));
	}

	/**
	 * 保存2D展示位置 虚拟展示位置表
	 */
	@PostMapping("/savePlanarPosition")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "保存2D展示位置", notes = "传入planarPosition")
	public R submitPlanarPosition(@Valid @RequestBody PlanarPositionVO planarPosition) {
		return R.status(displayPositionService.submit(planarPosition));
	}

	/**
	 * 设备配置AI模型列表 设备信息表
	 */
	@GetMapping("/equipmentAiModelList")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "设备配置AI模型列表", notes = "传入equipmentId")
	public R<List<AiModelVO>> equipmentAiModelList(@ApiParam(value = "设备id", required = true) @RequestParam Long equipmentId) {
		return R.data(aiModelService.equipmentAiModelList(equipmentId));
	}

	/**
	 * 绑定AI模型 设备信息表
	 */
	@PostMapping("/bindAiModel")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "绑定AI模型", notes = "传入id、aiModelIds")
	public R bindSensor(@ApiParam(value = "设备id", required = true) @RequestParam Long equipmentId,
						@ApiParam(value = "AI模型主键集合", required = true) @RequestParam String aiModelIds) {
		return R.status(equipmentService.bindAiModel(equipmentId, aiModelIds));
	}

	/**
	 * 绑定AI模型 设备信息表
	 */
	@PostMapping("/unbindAiModel")
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "解绑AI模型", notes = "传入equipmentId、aiModelId")
	public R unbindSensor(@ApiParam(value = "设备id", required = true) @RequestParam Long equipmentId,
						  @ApiParam(value = "AI模型id", required = true) @RequestParam Long aiModelId) {
		return R.status(equipmentService.unbindAiModel(equipmentId, aiModelId));
	}

	/**
	 * 门户设备详情运行情况
	 */
	@GetMapping("/equipmentRunningStatistics")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "id", value = "设备id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "门户设备详情运行情况", notes = "传入id")
	public R<EquipmentStatisticsDTO> equipmentRunningStatistics(@ApiIgnore EquipmentVO vo) {
		return R.data(equipmentLogicService.equipmentRunningStatistics(vo));
	}

	/**
	 * 门户设备详情统计
	 */
	@GetMapping("/equipmentStatistics")
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "门户设备详情统计", notes = "传入id")
	public R<EquipmentStatisticsDTO> equipmentStatistics(Long id) {
		return R.data(equipmentLogicService.equipmentStatistics(id));
	}

	/**
	 * 获取基础数据（设备、测点、传感器）
	 * @param needSpotCheck 是否需要点检的设备
	 * @return
	 */
	@GetMapping("/getBasicData")
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "获取基础数据（设备、测点、传感器）", notes = "可根据needSpotCheck过滤")
	public R<BasicDataDTO> getBasicData(@RequestParam(value = "needSpotCheck", required = false, defaultValue = "0") Integer needSpotCheck) {
		return R.data(equipmentLogicService.getBasicData(needSpotCheck));
	}

	/**
	 * 设备绑定rfid
	 */
	@PostMapping("/bindRfid")
	@ApiOperationSupport(order = 21)
	@ApiOperation(value = "绑定rfid", notes = "传入id、rfid")
	public R<Boolean> bindRfid(@RequestBody BindRfidVO bindRfidVO) {
		return R.status(equipmentService.bindRfid(bindRfidVO));
	}

	/**
	 * 详情
	 */
	@GetMapping("/detailByRfid")
	@ApiOperationSupport(order = 22)
	@ApiOperation(value = "详情", notes = "传入rfid")
	public R<EquipmentDTO> detailByRfid(@RequestParam String rfid) {
		return R.data(equipmentService.detailByRfid(rfid));
	}

	/**
	 * 累计报警、故障数
	 */
	@GetMapping("/abnormal-times/{id}")
	@ApiOperationSupport(order = 23)
	@ApiOperation(value = "累计报警、故障数", notes = "传入id")
	public R<AbnormalTimesDTO> abnormalTimes(@PathVariable Long id) {
		return R.data(equipmentService.abnormalTimes(id));
	}

	/**
	 * 设备运行情况统计
	 */
	@GetMapping("/equipment-running-page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "pathId", value = "设备/组织路径id", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 24)
	@ApiOperation(value = "设备运行情况统计", notes = "传入设备/组织路径id")
	public R<SzykPage<EquipmentRunningStatExcelDTO>> equipmentRunningPage(@ApiIgnore EquipmentRunningStatVO vo, Query query, SzykUser szykUser) {
		SzykPage<EquipmentRunningStatExcelDTO> list = equipmentLogicService.equipmentRunningPage(query, vo,
			(szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY : szykUser.getTenantId()));
		return R.data(list);
	}

	/**
	 * 设备运行情况统计-导出
	 */
	@GetMapping("/export-equipment-running")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "pathId", value = "设备/组织路径id", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 25)
	@ApiOperation(value = "设备运行情况统计-导出", notes = "传入设备/组织路径id")
	public void exportEquipmentRunning(@ApiIgnore EquipmentRunningStatVO vo, SzykUser szykUser, HttpServletResponse response) {
		equipmentLogicService.exportEquipmentRunning(vo, (szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY : szykUser.getTenantId()), response);
	}

	/**
	 * 设备统计（总数、运行数、停机数）
	 * @param szykUser user
	 * @return
	 */
	@GetMapping("/equipment_stat")
	@ApiOperationSupport(order = 26)
	@ApiOperation(value = "设备统计（总数、运行数、停机数）", notes = "设备统计（总数、运行数、停机数）")
	public R<EquipmentStatDTO> equipmentStat(SzykUser szykUser) {
		EquipmentStatDTO result = equipmentLogicService.equipmentStat((szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY : szykUser.getTenantId()));
		return R.data(result);
	}

}
