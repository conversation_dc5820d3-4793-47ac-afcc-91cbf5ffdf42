/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.tool.utils.*;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.service.*;
import com.snszyk.zbusiness.basic.vo.BasicTreeVO;
import com.snszyk.zbusiness.basic.vo.DeviceCollectionStationVO;
import com.snszyk.zbusiness.basic.vo.DeviceVO;
import com.snszyk.zbusiness.basic.vo.SwitchSortVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备树表 逻辑服务实现类
 *
 * <AUTHOR>
 * @date 2022/10/10 09:16
 **/
@AllArgsConstructor
@Service
@Slf4j
public class DeviceLogicService {

	private final IDeviceService deviceService;
	private final IEquipmentService equipmentService;
	private final IMonitorService monitorService;
	private final IBasicTreeService basicTreeService;
	private final IAttachService attachService;
	private final IDeviceCollectionStationService deviceCollectionStationService;
	private final ICollectionStationService collectionStationService;
	private final RabbitTemplate rabbitTemplate;

	/**
	 * 详情
	 *
	 * @param id
	 * @return com.snszyk.zbusiness.basic.dto.DeviceDTO
	 * <AUTHOR>
	 * @date 2022/10/10 15:19
	 */
	public DeviceDTO detail(Long id) {
		Device device = deviceService.getById(id);
		if (device == null) {
			throw new ServiceException("当前节点已删除，请刷新后重试!");
		}
		DeviceDTO detailDTO = Objects.requireNonNull(BeanUtil.copy(device, DeviceDTO.class));
		// 父级
		if (device.getParentId() != Func.toLong(StringPool.ZERO)) {
			Device deviceParent = deviceService.getById(device.getParentId());
			DeviceDTO parentDetail = Objects.requireNonNull(BeanUtil.copy(deviceParent, DeviceDTO.class));
			List<Attach> attachList = attachService.listByIds(Arrays.asList(deviceParent.getImage()));
			if (ObjectUtil.isNotEmpty(attachList)) {
				parentDetail.setImageList(attachList);
			}
			detailDTO.setParent(parentDetail);
		}
		// 设备总数
		List<Long> deviceIds = new ArrayList<>();
		deviceIds.add(id);
		List<Device> deviceChild = deviceService.getDeviceChild(device.getId());
		if (Func.isNotEmpty(deviceChild)) {
			deviceIds.addAll(deviceChild.stream().map(Device::getId).collect(Collectors.toList()));
		}
		int eqSum = equipmentService.count(Wrappers.<Equipment>query().lambda().in(Equipment::getDeviceId, deviceIds));
		detailDTO.setEqSum(eqSum);
		// 附件信息
		List<Attach> imageList = null;
		if (Func.isNotEmpty(device.getImage())) {
			List<Attach> attachList = attachService.listByIds(Arrays.asList(device.getImage()));
			if (Func.isNotEmpty(attachList)) {
				imageList = attachList;
			}
		}
		detailDTO.setImageList(imageList);
		//绑定的采集站列表 - 通过模型图绑定
		if (device.getImage() != null) {
			List<DeviceCollectionStationDTO> collectionStationList = deviceCollectionStationService.listCollectionStation(device.getId());
			log.info("获取厂区绑定的采集站列表，size = {}", collectionStationList != null ? collectionStationList.size() : 0);
			if (CollectionUtil.isNotEmpty(collectionStationList)) {
				collectionStationList.forEach(dto -> dto.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, dto.getOnline())));
			}
			detailDTO.setCollectionStationList(collectionStationList);
		}
		return detailDTO;
	}

	/**
	 * 新增或修改
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2022/10/11 18:59
	 */
	public boolean submit(DeviceVO vo) {
		if (vo.getParentId() != null && vo.getParentId() > 0) {
			Device parent = deviceService.getById(vo.getParentId());
			if (parent == null) {
				throw new ServiceException("所选上级位置已删除，请刷新后重试!");
			}
		}
		Device device = Objects.requireNonNull(BeanUtil.copy(vo, Device.class));

		// 校验同级是否同名
		if (deviceService.isNameExist(device)) {
			throw new ServiceException("同级名称已存在！");
		}

		// 校验编码唯一性
		if (deviceService.isCodeDuplicate(device)) {
			throw new ServiceException("位置编码已存在！！");
		}

		if (Func.isNotEmpty(vo.getId())) {
			Device deviceInDb = deviceService.getById(vo.getId());
			// 编辑时，先验证当前位置是否已被删除
			if (deviceInDb == null) {
				throw new ServiceException("当前位置已删除，请刷新后重试!");
			}

			//修改地点名称 & pathName
			if (!deviceInDb.getName().equals(device.getName())) {
				// 更新pathName中的地点名称
				if (deviceInDb.getPathName().contains(StringPool.COMMA)) {
					device.setPathName(deviceInDb.getPathName().substring(0, deviceInDb.getPathName().lastIndexOf(StringPool.COMMA))
						+ StringPool.COMMA
						+ device.getName());
				} else {
					device.setPathName(device.getName());
				}

				// 发送名称更新MQ
				BasicTreeNameUpdateDTO dto = new BasicTreeNameUpdateDTO().setId(device.getId())
					.setNewName(device.getName())
					.setNewPathName(device.getPathName())
					.setType(BasicTreeNameUpdateDTO.TYPE_DEVICE);
				rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_BASIC_TREE_NAME_UPDATE, null, dto);
				log.info("发送地点名称更新MQ成功，dto = {}", dto);
			}
		} else {
			device.setSort(deviceService.getMaxSort() + 1);
		}
		if (Func.isBlank(Func.toStr(device.getImage()))) {
			device.setImage(null);
			//删除厂区绑定的采集站
			int deleteCount = deviceCollectionStationService.deleteByDeviceId(device.getId());
			log.info("删除厂区模型图后，同时删除绑定的采集站，删除数 = {}", deleteCount);
		}
		boolean saveOrUpdate = deviceService.saveOrUpdate(device);
		log.info("更新or新增地点 - {}", saveOrUpdate);

		// 更新基础树结构
		BasicTreeVO basicTreeVO = new BasicTreeVO().toBasicTreeVO(device);
		basicTreeVO.setParentId(vo.getParentId()).setNodeLevel(vo.getLevel());
		String path = basicTreeService.submit(basicTreeVO);
		if(Func.isEmpty(vo.getId())){
			String pathName = basicTreeService.getById(device.getId()).getPathName();
			deviceService.update(Wrappers.<Device>update().lambda()
				.eq(Device::getId, device.getId()).set(Device::getPath, path).set(Device::getPathName, pathName));
		}
		return saveOrUpdate;
	}

	/**
	 * 门户-点定位
	 *
	 * @param id
	 * @return
	 */
	public DeviceCoordinateDTO deviceView(Long id) {
		Device device = deviceService.getById(id);
		if (device == null) {
			throw new ServiceException("当前节点已删除，请刷新后重试!");
		}
		Attach attachResult = attachService.getById(device.getImage());
		if (attachResult == null) {
			throw new ServiceException("暂无模型图");
		}
		List<DeviceCoordinateSubDTO> list = deviceService.deviceView(id);
		return new DeviceCoordinateDTO(device.getId(), device.getCode(), device.getName(), attachResult, list);
	}

	/**
	 * 绑定厂区和采集站
	 * @param vo vo
	 * @return
	 */
	public DeviceCollectionStationDTO bindCollectionStation(DeviceCollectionStationVO vo) {
		// 校验是否已绑定
		DeviceCollectionStation existedBind = deviceCollectionStationService.getOne(new QueryWrapper<DeviceCollectionStation>()
			.lambda()
			.eq(DeviceCollectionStation::getDeviceId, vo.getDeviceId())
			.eq(DeviceCollectionStation::getStationId, vo.getStationId())
			.eq(BaseEntity::getIsDeleted, 0));
		if (existedBind != null) {
			throw new ServiceException("当前采集站已绑定到此厂区！");
		}

		// 添加绑定
		DeviceCollectionStation entity = BeanUtil.copyProperties(vo, DeviceCollectionStation.class);
		boolean save = deviceCollectionStationService.save(entity);
		log.info("绑定厂区和采集站：{}, entity = {}", save, entity);
		if (save) {
			DeviceCollectionStationDTO dto = BeanUtil.copyProperties(entity, DeviceCollectionStationDTO.class);
			assert dto != null;
			dto.setStationName(collectionStationService.getById(entity.getStationId()).getName());
			return dto;
		}

		return null;
	}

	/**
	 * 删除厂区绑定的单个采集站
	 * @param id 绑定id
	 * @return
	 */
	public boolean removeCollectionStation(String id) {
		return deviceCollectionStationService.removeById(id);
	}

	/**
	 * 删除厂区绑定的所有采集站
	 * @param deviceId 厂区id
	 * @return
	 */
	public boolean removeAllCollectionStation(String deviceId) {
		int deleteCount = deviceCollectionStationService.deleteByDeviceId(Long.parseLong(deviceId));
		log.info("删除厂区绑定的所有采集站，删除数 = {}", deleteCount);
		return true;
	}

	/**
	 * 交换2个对象的sort值
	 * @param vo vo
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public boolean switchSort(SwitchSortVO vo) {
		//参数校验
		if (vo.getSrcId() == null || vo.getSrcSort() == null || vo.getDestId() == null || vo.getDestSort() == null) {
			throw new ServiceException("参数不能空！");
		}

		//更新sort值
		switch (vo.getCategory()) {
			case 0:
				// device
				boolean updateSrcDevice = deviceService.update(new UpdateWrapper<Device>()
					.lambda()
					.set(Device::getSort, vo.getDestSort())
					.eq(BaseEntity::getId, vo.getSrcId()));
				boolean updateDestDevice = deviceService.update(new UpdateWrapper<Device>()
					.lambda()
					.set(Device::getSort, vo.getSrcSort())
					.eq(BaseEntity::getId, vo.getDestId()));
				log.info("更新Device的排序：updateSrcDevice = {}, updateDestDevice = {}", updateSrcDevice, updateDestDevice);
				break;
			case 1:
				// equipment
				boolean updateSrcEquipment = equipmentService.update(new UpdateWrapper<Equipment>()
					.lambda()
					.set(Equipment::getSort, vo.getDestSort())
					.eq(BaseEntity::getId, vo.getSrcId()));
				boolean updateDestEquipment = equipmentService.update(new UpdateWrapper<Equipment>()
					.lambda()
					.set(Equipment::getSort, vo.getSrcSort())
					.eq(BaseEntity::getId, vo.getDestId()));
				log.info("更新Equipment的排序：updateSrcEquipment = {}, updateDestEquipment = {}", updateSrcEquipment, updateDestEquipment);
				break;
			case 2:
				// monitor
				boolean updateSrcMonitor = monitorService.update(new UpdateWrapper<Monitor>()
					.lambda()
					.set(Monitor::getSort, vo.getDestSort())
					.eq(BaseEntity::getId, vo.getSrcId()));
				boolean updateDestMonitor = monitorService.update(new UpdateWrapper<Monitor>()
					.lambda()
					.set(Monitor::getSort, vo.getSrcSort())
					.eq(BaseEntity::getId, vo.getDestId()));
				log.info("更新Monitor的排序：updateSrcMonitor = {}, updateDestMonitor = {}", updateSrcMonitor, updateDestMonitor);
				break;
			default:
				throw new ServiceException("非法的category值" + vo.getCategory() + "！仅支持0、1、2。");
		}

		//同步更新到BasicTree表中
		boolean basicTreeSrcUpdate = basicTreeService.update(new UpdateWrapper<BasicTree>()
			.lambda()
			.set(BasicTree::getSort, vo.getDestSort())
			.eq(BasicTree::getId, vo.getSrcId()));
		boolean basicTreeDestUpdate = basicTreeService.update(new UpdateWrapper<BasicTree>()
			.lambda()
			.set(BasicTree::getSort, vo.getSrcSort())
			.eq(BasicTree::getId, vo.getDestId()));
		log.info("更新BasicTree的排序：basicTreeSrcUpdate = {}, basicTreeDestUpdate = {}", basicTreeSrcUpdate, basicTreeDestUpdate);

		return true;
	}
}
