/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.MechanismModelDTO;
import com.snszyk.zbusiness.basic.dto.MonitorModelDTO;
import com.snszyk.zbusiness.basic.dto.SystemConfigStatDTO;
import com.snszyk.zbusiness.basic.entity.MechanismModel;
import com.snszyk.zbusiness.basic.entity.MonitorModel;
import com.snszyk.zbusiness.basic.enums.NonVibrationDataEnum;
import com.snszyk.zbusiness.basic.service.IMechanismModelService;
import com.snszyk.zbusiness.basic.service.IMonitorModelService;
import com.snszyk.zbusiness.basic.vo.MechanismModelVO;
import com.snszyk.zbusiness.basic.vo.MonitorModelVO;
import com.snszyk.zbusiness.basic.wrapper.MechanismModelWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 机理模型 控制器
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/mechanismModel")
@Api(value = "机理模型", tags = "机理模型接口")
public class MechanismModelController extends SzykController {

	private final IMechanismModelService mechanismModelService;
	private final IMonitorModelService monitorModelService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入mechanismModel")
	public R<MechanismModelDTO> detail(Long id) {
		return R.data(mechanismModelService.detail(id));
	}

	/**
	 * 列表 机理模型库
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入mechanismModel")
	public R<IPage<MechanismModelVO>> list(MechanismModel mechanismModel, Query query) {
		IPage<MechanismModel> pages = mechanismModelService.page(Condition.getPage(query), Condition.getQueryWrapper(mechanismModel));
		return R.data(MechanismModelWrapper.build().pageVO(pages));
	}

	/**
	 * 分页 机理模型
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "applyEquipment", value = "应用设备类型（字典：device_type）", paramType = "query", dataType = "integer"),
		@ApiImplicitParam(name = "applyPower", value = "应用功率范围（字典：power_range）", paramType = "query", dataType = "integer"),
		@ApiImplicitParam(name = "type", value = "机理类型（字典：model_type）", paramType = "query", dataType = "integer"),
		@ApiImplicitParam(name = "status", value = "状态（0：启用，1：禁用，2：全部）", required = true, paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "applyData", value = "应用数据类型（字典：apply_data_type）", required = true, paramType = "query", dataType = "integer")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入mechanismModel")
	public R<IPage<MechanismModelDTO>> page(@ApiIgnore MechanismModelVO mechanismModel, Query query) {
		IPage<MechanismModelDTO> pages = mechanismModelService.page(Condition.getPage(query), mechanismModel);
		return R.data(pages);
	}

	/**
	 * 新增或修改 机理模型
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增或修改", notes = "传入mechanismModel")
	public R submit(@Valid @RequestBody MechanismModelVO mechanismModel) {
		return R.status(mechanismModelService.submit(mechanismModel));
	}

	/**
	 * 删除 机理模型
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		monitorModelService.remove(Wrappers.<MonitorModel>query().lambda().in(MonitorModel::getModelId, ids));
		return R.status(mechanismModelService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 启用/禁用 机理模型
	 */
	@PostMapping("/activate")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "启用/禁用", notes = "传入id")
	public R activate(@ApiParam(value = "主键", required = true) @RequestParam Long id,
					  @ApiParam(value = "状态（0：启用，1：禁用）", required = true) @RequestParam Integer status) {
		List<MonitorModel> list = monitorModelService.list(Wrappers.<MonitorModel>query().lambda().eq(MonitorModel::getModelId, id));
		if(Func.isNotEmpty(list)){
			throw new ServiceException("当前机理模型已应用于设备部位，无法禁用！");
		}
		// 停用所有当前模型应用的部位
		//if(Func.toInt(StringPool.ONE) == status){
		//	monitorModelService.update(Wrappers.<MonitorModel>update().lambda()
		//		.set(MonitorModel::getStatus, status).eq(MonitorModel::getModelId, id));
		//}
		return R.status(mechanismModelService.update(Wrappers.<MechanismModel>update().lambda()
			.set(MechanismModel::getStatus, status).eq(MechanismModel::getId, id)));
	}

	/**
	 * 应用机理模型 机理模型
	 */
	@PostMapping("/distribute")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "应用机理模型", notes = "传入id、monitorIds")
	public R<Integer> distribute(@ApiParam(value = "主键", required = true) @RequestParam Long id,
									@ApiParam(value = "部位主键列表", required = true) @RequestParam String monitorIds,
								 	@ApiParam(value = "状态（0：取消应用，1：应用）", required = true) @RequestParam Integer applyStatus) {
		return R.data(mechanismModelService.distribute(id, Func.toLongList(monitorIds), applyStatus));
	}

	/**
	 * 个性化配置部位机理模型 机理模型
	 */
	@PostMapping("/configMonitorModel")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "个性化配置部位机理模型", notes = "传入mechanismModel")
	public R configMonitorModel(@Valid @RequestBody MechanismModelVO mechanismModel) {
		return R.data(mechanismModelService.configMonitorModel(mechanismModel));
	}

	/**
	 * 个性化配置部位机理模型 机理模型
	 */
	@GetMapping("/monitorModelDetail")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "部位机理模型详情", notes = "传入monitorId、modelId")
	public R<MechanismModelDTO> monitorModelDetail(@ApiParam(value = "部位id", required = true) @RequestParam Long monitorId,
													@ApiParam(value = "模型id", required = true) @RequestParam Long modelId) {
		return R.data(mechanismModelService.monitorModelDetail(monitorId, modelId));
	}

	/**
	 * 启用部位上的机理模型 机理模型
	 */
	@PostMapping("/enable")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "启用部位上的机理模型", notes = "传入id、monitorId")
	public R enable(@ApiParam(value = "机理模型主键", required = true) @RequestParam Long id,
								 @ApiParam(value = "部位主键", required = true) @RequestParam Long monitorId) {
		return R.status(monitorModelService.update(Wrappers.<MonitorModel>update().lambda()
			.set(MonitorModel::getStatus, 0).eq(MonitorModel::getMonitorId, monitorId).eq(MonitorModel::getModelId, id)));
	}

	/**
	 * 停用部位上的机理模型 机理模型
	 */
	@PostMapping("/disable")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "停用部位上的机理模型", notes = "传入id、monitorId")
	public R disable(@ApiParam(value = "机理模型主键", required = true) @RequestParam Long id,
								 @ApiParam(value = "部位主键", required = true) @RequestParam Long monitorId) {
		return R.status(monitorModelService.update(Wrappers.<MonitorModel>update().lambda()
			.set(MonitorModel::getStatus, 1).eq(MonitorModel::getMonitorId, monitorId).eq(MonitorModel::getModelId, id)));
	}

	/**
	 * 门户-机理配置分页 机理模型表
	 */
	@GetMapping("/monitorModelPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树结构id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "keywords", value = "部位名称或编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "门户-机理配置分页", notes = "传入monitorModel")
	public R<IPage<MonitorModelDTO>> monitorModelPage(@ApiIgnore MonitorModelVO monitorModel, Query query) {
		IPage<MonitorModelDTO> pages = monitorModelService.page(Condition.getPage(query), monitorModel);
		return R.data(pages);
	}

	/**
	 * 时域指标列表 机理模型
	 */
	@GetMapping("/timeDomainModel")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "时域指标列表", notes = "传入")
	public R<List<MechanismModel>> timeDomainModel() {
		List<MechanismModel> list = Arrays.stream(NonVibrationDataEnum.values()).map(data -> {
			MechanismModel model = new MechanismModel();
			model.setCode(Func.toStr(data.getCode())).setName(data.getName());
			return model;
		}).collect(Collectors.toList());
		return R.data(list);
	}

	/**
	 * 机理模型列表 机理模型
	 */
	@GetMapping("/statusCount")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "状态数据统计", notes = "传入")
	public R<Map<Integer, Integer>> statusCount(@ApiParam(value = "应用数据类型（1：振动，2：应力波）", required = true) @RequestParam Integer applyData) {
		Map<Integer, Integer> statusCount = new HashMap<>(16);
		statusCount.put(0, mechanismModelService.count(Wrappers.<MechanismModel>query().lambda()
			.eq(MechanismModel::getApplyData, applyData).eq(MechanismModel::getStatus, 0)));
		statusCount.put(1, mechanismModelService.count(Wrappers.<MechanismModel>query().lambda()
			.eq(MechanismModel::getApplyData, applyData).eq(MechanismModel::getStatus, 1)));
		statusCount.put(2, mechanismModelService.count(Wrappers.<MechanismModel>query().lambda()
			.eq(MechanismModel::getApplyData, applyData)));
		return R.data(statusCount);
	}

	/**
	 * 门户端-系统配置概览
	 */
	@GetMapping("/systemConfigStat")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "门户端-系统配置概览", notes = "传入")
	public R<SystemConfigStatDTO> systemConfigStat() {
		return R.data(mechanismModelService.systemConfigStat());
	}

}
