/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.snszyk.zbusiness.ops.entity.AlarmRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 报警记录表视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AlarmRecordVO对象", description = "报警记录表")
public class AlarmRecordVO extends AlarmRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 报警指标名称
	 */
	@ApiModelProperty(value = "报警指标名称")
	private String alarmIndexName;

	/**
	 * 报警等级名称
	 */
	@ApiModelProperty(value = "报警等级名称")
	private String alarmLevelName;

	/**
	 * 报警值
	 */
	@ApiModelProperty(value = "报警值")
	private String alarmValue;

	/**
	 * 报警数据采集时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "报警数据采集时间")
	private Date originTime;

}
