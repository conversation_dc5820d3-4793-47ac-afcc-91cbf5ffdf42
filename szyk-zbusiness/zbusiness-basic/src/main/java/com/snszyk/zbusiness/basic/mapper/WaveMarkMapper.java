/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.zbusiness.basic.dto.BearingCharacteristicFreqDTO;
import com.snszyk.zbusiness.basic.entity.WaveMark;
import com.snszyk.zbusiness.basic.vo.WaveMarkVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 波形标注表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
public interface WaveMarkMapper extends BaseMapper<WaveMark> {

	/**
	 * 物理删除
	 *
	 * @param waveMark
	 * @return
	 */
	Integer removeByParams(@Param("waveMark") WaveMarkVO waveMark);

	/**
	 * 获取波形关联的轴承特征频率
	 * @param waveId 波形id
	 * @return
	 */
    List<BearingCharacteristicFreqDTO> getCharacteristicFreqList(@Param("waveId") Long waveId);
}
