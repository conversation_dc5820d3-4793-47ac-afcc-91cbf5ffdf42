package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 系统配置概览统计对象
 * <AUTHOR>
 */
@Data
@ApiModel("系统配置概览统计对象")
public class SystemConfigStatDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 机理模型数量
	 */
	@ApiModelProperty("机理模型总数")
	private Integer mechanismModelCount;

	/**
	 * 机理模型已应用数量
	 */
	@ApiModelProperty("机理模型已应用数量")
	private Integer mechanismModelApplyCount;

	/**
	 * AI模型总数
	 */
	@ApiModelProperty("AI模型总数")
	private Integer aiModelCount;

	/**
	 * AI模型已应用数量
	 */
	@ApiModelProperty("AI模型已应用数量")
	private Integer aiModelApplyCount;

	public SystemConfigStatDTO(){

	}

	public SystemConfigStatDTO(Integer mechanismModelCount, Integer aiModelCount){
		this.mechanismModelCount = mechanismModelCount;
		this.aiModelCount = aiModelCount;
	}

}
