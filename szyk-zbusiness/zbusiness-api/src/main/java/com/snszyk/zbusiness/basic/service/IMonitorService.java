/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.zbusiness.basic.dto.DetectionRangeDTO;
import com.snszyk.zbusiness.basic.dto.MonitorDTO;
import com.snszyk.zbusiness.basic.dto.MonitorThresholdDTO;
import com.snszyk.zbusiness.basic.dto.WaveDTO;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.vo.MonitorVO;
import com.snszyk.zbusiness.basic.vo.WaveVO;

import java.util.List;

/**
 * 设备测点表 服务类
 *
 * <AUTHOR>
 * @since 2022-10-13
 */
public interface IMonitorService extends BaseService<Monitor> {

	/**
	 * 分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<MonitorDTO> page(IPage<MonitorDTO> page, MonitorVO vo);

	/**
	 * 驾驶舱-监测范围统计
	 *
	 * @param id
	 * @return
	 */
	DetectionRangeDTO detectionRange(Long id);

	/**
	 * 根据传感器编码查询测点
	 *
	 * @param sensorCode
	 * @return
	 */
	MonitorThresholdDTO queryBySensorCode(String sensorCode);

	/**
	 * 新增测点时获取当前最大sort值
	 *
	 * @return
	 */
	Integer getMaxSort();

	/**
	 * 机理模型应用部位分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<MonitorDTO> modelMonitorPage(IPage<MonitorDTO> page, MonitorVO vo);

	/**
	 * 标准门限应用波形分页
	 *
	 * @param page
	 * @param vo
	 * @return
	 */
	IPage<WaveDTO> thresholdWavePage(IPage<WaveDTO> page, WaveVO vo);

	/**
	 * 创建传感器数据表
	 *
	 * @param monitorId 测点id
	 * @return
	 */
	int createSensorDataTable(Long monitorId);

	/**
	 * 测点对应波形
	 *
	 * @param monitorId 测点id
	 * @return
	 */
	List<WaveDTO> waveList(String tenantId, Long monitorId);

	MonitorDTO getByIdIncludeEquipment(Long monitorId);
}
