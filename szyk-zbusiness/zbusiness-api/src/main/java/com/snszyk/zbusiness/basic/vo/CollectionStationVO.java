/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.CollectionStation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 采集站 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CollectionStationVO对象", description = "CollectionStationVO对象")
public class CollectionStationVO extends CollectionStation {

	private static final long serialVersionUID = 1L;

	/**
	 * 租户id
	 */
	@ApiModelProperty(value = "租户ID")
	private String tenantId;
	/**
	 * 在线离线
	 */
	@ApiModelProperty(value = "在线离线")
	private String onlineName;

	/**
	 * 有线无线
	 */
	@ApiModelProperty(value = "有线无线")
	private String isWirelessName;

	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createUserName;

	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateUserName;

	/**
	 * 通道列表
	 */
	@ApiModelProperty("通道列表")
	private List<CollectionStationChannelVO> channelList;

}
