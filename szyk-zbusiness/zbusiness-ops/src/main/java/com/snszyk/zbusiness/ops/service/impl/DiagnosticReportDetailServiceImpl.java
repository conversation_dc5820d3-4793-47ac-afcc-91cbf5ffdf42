/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.zbusiness.ops.entity.DiagnosticReportDetail;
import com.snszyk.zbusiness.ops.mapper.DiagnosticReportDetailMapper;
import com.snszyk.zbusiness.ops.service.IDiagnosticReportDetailService;
import org.springframework.stereotype.Service;

/**
 * 诊断报告明细表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Service
public class DiagnosticReportDetailServiceImpl extends ServiceImpl<DiagnosticReportDetailMapper, DiagnosticReportDetail> implements IDiagnosticReportDetailService {

	@Override
	public boolean removeByReportId(Long reportId) {
		return baseMapper.removeByReportId(reportId) >= 0;
	}

}
