/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.system.entity.Region;
import com.snszyk.system.excel.RegionExcel;
import com.snszyk.system.vo.RegionVO;

import java.util.List;
import java.util.Map;

/**
 * 行政区划表 服务类
 *
 * <AUTHOR>
 */
public interface IRegionService extends IService<Region> {

	/**
	 * 提交
	 *
	 * @param region
	 * @return
	 */
	boolean submit(Region region);

	/**
	 * 删除
	 *
	 * @param id
	 * @return
	 */
	boolean removeRegion(String id);

	/**
	 * 懒加载列表
	 *
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<RegionVO> lazyList(String parentCode, Map<String, Object> param);

	/**
	 * 懒加载列表
	 *
	 * @param parentCode
	 * @param param
	 * @return
	 */
	List<RegionVO> lazyTree(String parentCode, Map<String, Object> param);

	/**
	 * 导入区划数据
	 *
	 * @param data
	 * @param isCovered
	 * @return
	 */
	void importRegion(List<RegionExcel> data, Boolean isCovered);

	/**
	 * 导出区划数据
	 *
	 * @param queryWrapper
	 * @return
	 */
	List<RegionExcel> exportRegion(Wrapper<Region> queryWrapper);


	/**
	 * 获取树，加载全部
	 * */
	List<RegionVO> getTree(Map<String, Object> param);
}
