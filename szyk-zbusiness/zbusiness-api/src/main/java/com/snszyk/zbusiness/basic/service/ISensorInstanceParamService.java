/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.dto.SensorInstanceParamDTO;
import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;

/**
 * 传感器实例参数表 服务类
 *
 * <AUTHOR>
 */
public interface ISensorInstanceParamService extends IService<SensorInstanceParam> {


	/**
	 * 查询状态类型的传感器实例参数
	 *
	 * @param sensorCode     传感器编码
	 * @param sampleDataType 采样数据类型 - 状态类型
	 * @return
	 */
	SensorInstanceParamDTO getStateParam(String sensorCode, String sampleDataType);
}
