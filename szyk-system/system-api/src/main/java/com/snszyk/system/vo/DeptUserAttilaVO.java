/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tool.node.INode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "DeptUserAttilaVO对象", description = "DeptUserAttilaVO对象")
public class DeptUserAttilaVO implements INode<DeptUserAttilaVO> {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long userId;

	/**
	 * 父节点ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long parentId;

	/**
	 * 子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<DeptUserAttilaVO> children;

	/**
	 * 是否有子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Boolean hasChildren;

	@Override
	public List<DeptUserAttilaVO> getChildren() {
		if (this.children == null) {
			this.children = new ArrayList<>();
		}
		return this.children;
	}

	/**
	 * 类型 部门/角色/用户
	 */
	@ApiModelProperty(value = "类型 1部门/2角色/3用户")
	private Integer dataType;

	/**
	 * 部门/角色/用户名字
	 * */
	@ApiModelProperty(value = "部门/角色/用户名字")
	private String label;

	/**
	 *  上级部门名字
	 * */
	@ApiModelProperty(value = "上级部门名字")
	private String parentName;

	/**
	 * 员工头像
	 */
	@ApiModelProperty(value = "员工头像")
	private String avatar;

}
