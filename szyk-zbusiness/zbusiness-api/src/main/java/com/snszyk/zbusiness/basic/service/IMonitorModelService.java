/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.dto.MonitorModelDTO;
import com.snszyk.zbusiness.basic.entity.MonitorModel;
import com.snszyk.zbusiness.basic.vo.MonitorModelVO;

/**
 * 部位机理模型表 服务类
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
public interface IMonitorModelService extends IService<MonitorModel> {

	/**
	 * 机理模型分页
	 *
	 * @param page
	 * @param monitorModel
	 * @return
	 */
	IPage<MonitorModelDTO> page(IPage<MonitorModelDTO> page, MonitorModelVO monitorModel);

	/**
	 * 部位机理模型分页
	 *
	 * @param page
	 * @param monitorModel
	 * @return
	 */
	IPage<MonitorModelDTO> monitorModelPage(IPage<MonitorModelDTO> page, MonitorModelVO monitorModel);

}
