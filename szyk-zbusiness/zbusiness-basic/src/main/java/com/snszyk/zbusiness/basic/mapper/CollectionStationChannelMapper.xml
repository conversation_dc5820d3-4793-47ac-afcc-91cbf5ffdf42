<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.zbusiness.basic.mapper.CollectionStationChannelMapper">

    <select id="selectChannelList" resultType="com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO">
        SELECT
            csc.id,
            csc.station_id,
            csc.`name`,
            csc.`online`,
            si.`code` AS sensor_code,
            sip.id AS spower_sensor_instance_param_id,
            REPLACE ( CONCAT( m.path_name, ',', st.`name` ), ',', '/' ) AS path_name,
            st.`name` AS sensor_name,
            st.is_wireless
        FROM
            eolm_collection_station_channel csc
        LEFT JOIN basic_sensor_instance si ON csc.sensor_code = si.CODE AND si.is_deleted = 0
        LEFT JOIN basic_sensor_instance_param sip ON si.id = sip.instance_id AND sip.sample_data_type = 'SPOWER'
        LEFT JOIN eolm_monitor m ON si.monitor_id = m.id
        LEFT JOIN basic_sensor_type st ON si.type_id = st.id
        WHERE
            csc.station_id = #{stationId}
    </select>
</mapper>
