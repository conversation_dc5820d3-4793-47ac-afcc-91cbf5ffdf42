/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.entity.MechanismModelParam;
import com.snszyk.zbusiness.basic.vo.MechanismModelParamVO;

import java.util.List;

/**
 * 机理模型参数表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-05
 */
public interface MechanismModelParamMapper extends BaseMapper<MechanismModelParam> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param mechanismModelParam
	 * @return
	 */
	List<MechanismModelParamVO> selectMechanismModelParamPage(IPage page, MechanismModelParamVO mechanismModelParam);

}
