/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.ops.entity.AlarmDetail;
import com.snszyk.zbusiness.ops.vo.AlarmDetailVO;

import java.util.Objects;

/**
 * 报警管理明细表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
public class AlarmDetailWrapper extends BaseEntityWrapper<AlarmDetail, AlarmDetailVO> {

	public static AlarmDetailWrapper build() {
		return new AlarmDetailWrapper();
 	}

	@Override
	public AlarmDetailVO entityVO(AlarmDetail alarmDetail) {
		AlarmDetailVO alarmDetailVO = Objects.requireNonNull(BeanUtil.copy(alarmDetail, AlarmDetailVO.class));

		//User createUser = UserCache.getUser(alarmDetail.getCreateUser());
		//User updateUser = UserCache.getUser(alarmDetail.getUpdateUser());
		//alarmDetailVO.setCreateUserName(createUser.getName());
		//alarmDetailVO.setUpdateUserName(updateUser.getName());

		return alarmDetailVO;
	}

}
