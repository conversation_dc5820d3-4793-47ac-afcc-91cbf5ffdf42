/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.snszyk.core.tool.node.TreeNode;
import com.snszyk.system.entity.Dept;
import com.snszyk.system.vo.DeptUserAttilaVO;
import com.snszyk.system.vo.DeptVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface DeptMapper extends BaseMapper<Dept> {

	/**
	 * 懒加载部门列表
	 *
	 * @param tenantId
	 * @param parentId
	 * @param param
	 * @return
	 */
	List<DeptVO> lazyList(String tenantId, Long parentId, Map<String, Object> param);

	/**
	 * 获取树形节点
	 *
	 * @param tenantId
	 * @return
	 */
	List<DeptVO> tree(String tenantId);

	List<TreeNode> treeDept(String tenantId);

	/**
	 * 懒加载获取树形节点
	 *
	 * @param tenantId
	 * @param parentId
	 * @return
	 */
	List<DeptVO> lazyTree(String tenantId, Long parentId);

	/**
	 * 获取部门名
	 *
	 * @param ids
	 * @return
	 */
	List<String> getDeptNames(Long[] ids);

	/**
	 * 所有部门下部门<DeptMultiTreeVO></>
	 *
	 * @param orgId orgId
	 * @return List<DeptMultiTreeVO>
	 */
	List<DeptUserAttilaVO> allTreeDepts(@Param("orgId") Long orgId);

	/**
	 * 所有部门下用户<DeptMultiTreeVO></>
	 *
	 * @param deptId deptIdList
	 * @return List<DeptMultiTreeVO>
	 */
	List<DeptUserAttilaVO> allTreeUsers(@Param("deptId") List<Long> deptId, @Param("orgId") Long orgId);

	List<DeptUserAttilaVO> selectUserByDeptIds(@Param("deptIdList") List<Long> deptIdList, @Param("orgId") Long orgId, @Param("userIdList") List<Long> userIdList);

	/**
	 * 根据部门ID集合查询所有子部门
	 * @param deptIdList
	 * @param orgId
	 * @return
	 */
	List<Dept> selectIdByAncestors(@Param("deptIdList") List<Long> deptIdList, @Param("orgId") Long orgId);

	/**
	 * 获取所有部门列表
	 * @param tenantId 租户
	 * @return
	 */
	List<DeptVO> allDeptList(@Param("tenantId") String tenantId);

	List<DeptVO> lazyTreeByParent(@Param("tenantId") String tenantId, @Param("parentId") Long parentId);
}
