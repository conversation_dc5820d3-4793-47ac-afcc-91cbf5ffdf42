/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.zbusiness.basic.entity.DisplayPosition;
import com.snszyk.zbusiness.basic.vo.PlanarPositionVO;

/**
 * 虚拟展示位置表 服务类
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public interface IDisplayPositionService extends IService<DisplayPosition> {

	/**
	 * 保存2D图展示框位置信息
	 *
	 * @param vo
	 * @return
	 */
	boolean submit(PlanarPositionVO vo);

	/**
	 * 根据设备id删除
	 *
	 * @param equipmentId
	 * @return
	 */
	boolean removeByEquipment(Long equipmentId);

	/**
	 * 根据测点id删除
	 *
	 * @param monitorId
	 * @return
	 */
	boolean removeByMonitor(Long monitorId);

	/**
	 * 根据传感器编码删除
	 *
	 * @param sensorCode
	 * @return
	 */
	boolean removeBySensorCode(String sensorCode);

}
