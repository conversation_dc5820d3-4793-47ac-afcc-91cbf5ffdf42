/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.snszyk.zbusiness.ops.entity.DiagnosticReportDetail;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 诊断报告明细表视图实体类
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DiagnosticReportDetailVO对象", description = "诊断报告明细表")
public class DiagnosticReportDetailVO extends DiagnosticReportDetail {
	private static final long serialVersionUID = 1L;

}
