/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 监测数据
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Data
@ApiModel(value = "MonitorDataDTO对象", description = "MonitorDataDTO")
public class MonitorDataDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "id")
	private Long id;

	@ApiModelProperty(value = "时间")
	private Long time;

	@ApiModelProperty(value = "采样值")
	private BigDecimal val;

	@ApiModelProperty(value = "指标")
	private Integer quota;

}
