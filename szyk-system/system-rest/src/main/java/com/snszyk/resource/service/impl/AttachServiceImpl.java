/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.common.config.SzykAttachConfig;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.oss.model.SzykFile;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.mapper.AttachMapper;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.resource.vo.AttachVO;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.UUID;

/**
 * 附件表 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class AttachServiceImpl extends BaseServiceImpl<AttachMapper, Attach> implements IAttachService {
	@Resource
	private SzykAttachConfig szykAttachConfig;

	/**
	 * 上传文件路径
	 */
	private String rootPath;
	@PostConstruct
	public void initField(){
		this.rootPath = szykAttachConfig.szykAttachProperties().getPath();
	}
	@Override
	public IPage<AttachVO> selectAttachPage(IPage<AttachVO> page, AttachVO attach) {
		return page.setRecords(baseMapper.selectAttachPage(page, attach));
	}

	/**
	 * 下载文件
	 * @param attachId 附件id
	 * @param response res
	 */
	@Override
	public void download(Long attachId, HttpServletResponse response) {
		Attach attach = baseMapper.selectById(attachId);
		if (attach == null) {
			throw new ServiceException(ResultCode.FAILURE);
		}
		//Oss oss = ossBuilder.getOss(AuthUtil.getTenantId(),"minio");
		//GetObjectArgs.Builder builder = GetObjectArgs.builder();
		//builder.bucket(AuthUtil.getTenantId() + "-" + oss.getBucketName());
		//builder.object(attach.getName());
		//GetObjectArgs args = builder.build();
		try {
			InputStream inputStream = new FileInputStream(attach.getLink());
			//设置文件名
			response.addHeader("Content-Type", "application/octet-stream");
			response.setCharacterEncoding("UTF-8");
			response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(attach.getOriginalName(), "UTF-8"));
			IOUtils.copy(inputStream, response.getOutputStream());
		} catch (Exception e) {
			log.error("文件下载异常");
			e.printStackTrace();
		}
	}

	@Override
	public SzykFile putFile(String fileName, String fileType, InputStream inputStream) {
		String filePath = this.rootPath + fileType;
		try {
			//文件后缀扩展名
			String fileExtensionName = null;
			if (fileName != null) {
				fileExtensionName = fileName.substring(fileName.lastIndexOf("."));
			}
			String currentFilePath = DateUtil.format(DateUtil.now(), "yyyyMMdd");
			File dirPath = new File(filePath + File.separator + currentFilePath);
			if (!dirPath.exists()) {
				dirPath.mkdirs();
			}
			//文件新名称
			String fileNewName = UUID.randomUUID() + fileExtensionName;
			//相对路径
			String relativePath = currentFilePath + File.separator + fileNewName;
			//绝对路径
			String absolutePath = filePath + File.separator + relativePath;
			//保存文件
			try (InputStream is = inputStream; FileOutputStream fos = new FileOutputStream(absolutePath)) {
				byte[] bs = new byte[512];
				int n;
				while ((n = is.read(bs)) != -1) {
					fos.write(bs, 0, n);
				}
			}
			SzykFile file = new SzykFile();
			file.setOriginalName(fileName);
			file.setName(fileNewName);
			file.setDomain(fileType + File.separator + relativePath);
			file.setLink(absolutePath);
			return file;
		} catch (Exception e) {
			e.printStackTrace();
		}
		throw new ServiceException("上传失败!");
	}
}
