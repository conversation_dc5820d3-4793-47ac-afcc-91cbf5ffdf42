# 1 架构

## 1.1 工程结构

```
├── szyk-single                            # 单应用
│   ├── doc                                  # 文档
│   │   ├── script												     # 脚本
│   │   │   ├── docker										       # doker镜像
│   │   │   └── fatjar										       # jar包
│   │   ├── sql										           # sql
│   │   │   ├── db										 			   # 表结构说明
│   │   │   ├── mysql										       # mysql-DDL
│   │   │   ├── oracle										 		 # oracle-DDL
│   │   │   ├── postgresql										 # postgre-DDL
│   │   │   ├── sqlserver										   # sqlserver-DDL
│   │   │   └── update												 # update语句
│   ├── szyk-admin                     			 # 启动模块
│   ├── szyk-common                          # 公共模块
│   ├── szyk-quartz                          # 定时任务
│   ├── szyk-system                          # 系统模块
│   │   │   ├── >>auth										 	   # 鉴权包
│   │   │   ├── >>desk										 	   # 通知包
│   │   │   ├── >>develop										 	 # 代码生成包
│   │   │   ├── >>flow										 	   # 工作流包
│   │   │   ├── >>resource										 # 资源包
│   │   │   └── >>system										 	 # 基础功能包
│   ├── szyk-zbiz                        		 # 业务模块
│   │   ├── zbiz-common								   	 		 # 业务对外暴露（Vo、Dto、IService）
│   │   ├── zbiz-module1								   		 # 业务模块1
│   │   └── zbiz-module2								  		 # 业务模块2
│   ├── .gitignore                				   # ignore
│   ├── LICENSE                						   # license
│   ├── pom.xml                						   # maven
│   └── README.md          						       # readme
```

## 1.2 概述

单应用架构适合小型项目，信息化初期阶段，采用单应用架构，具有开发方便、测试方便、部署方便、运行简单的优点。

## 1.3 模块化设计

模块化区分进行结构的优化，使其结构清晰，方便代码的复用，利于维护也方便后续做拆分。部署的时候即多模块合并打包，也可以单模块打包，可以根据实际情况灵活应用

### 1.3.1 父模块

只做包的依赖和模块化管理

引入核心包依赖

```
<!-- 父工程 -->
<dependencyManagement>
	<dependencies>
		<dependency>
			<groupId>com.snszyk.platform</groupId>
			<artifactId>szyk-bom</artifactId>
			<version>${szyk.project.version}</version>
			<type>pom</type>
			<scope>import</scope>
		</dependency>
	</dependencies>
</dependencyManagement>
```
### 1.3.2 子模块

引入核心包

```
<!-- 子工程 -->
<dependencies>
	 <dependency>
   	<groupId>com.snszyk</groupId>
    <artifactId>szyk-starter-excel</artifactId>
  </dependency>
</dependencies>
```

- szyk-admin 启动、配置模块
- szyk-common 公共基础模块
- szyk-quartz 定时任务
- szyk-system 系统基础模块
- szyk-zbiz 业务模块
    - zbiz-common 业务对外暴露（Vo、Dto、IService）
    - zbiz-module1 业务模块1
    - zbiz-module2 业务模块2



## 1.4 分层设计

MVC模式，springMVC基于MVC 设计模式的轻量级 Web 开发框架

Vo、Po（entity）、Dto三层模式，根本原因不同层需要不同数据内容和格式，此外，可以防止数据暴露，dto可以为条件构造器RPC数据传输提供序列化支持。

v-view：视图对象

p-persistent：持久化对象

Dt-dataTransfer：数据传输对象

## 1.5 缓存设计

缓存是在架构设计中很重的考虑因素，尤其对一些高并发、高性能的项目上，缓存的使用能极大的提高系统性能。

- 加速读写，减少请求响应时间
- 降低后端负载
- 大量写合并为批量写



主要对于高频使用、低频更新的数据做缓存：

- 共享共用数据（共享）
- 增删改频率小（增删改少）
- 频繁访问的数据（查询多）
- 对实时性要求高的数据（特殊需求）

系统缓存：

用户、数据字典、业务数据字典、参数、行政区划

系统（菜单、部门、岗位、角色、租户）



其它缓存：

用户角色对应的菜单权限

验证码、错误登录锁定

数据权限缓存

备注：

- 系统暂时未做分级缓存，如一级缓存直写内存，建立内存索引，二级缓存redis等

- 未考虑缓存击穿、雪崩、击穿等解决方案，高并发需求下再做考虑

  


