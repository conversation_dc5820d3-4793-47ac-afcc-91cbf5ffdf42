package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 设备运行状态统计dto - 按地点
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "设备运行状态统计dto - 按地点", description = "设备运行状态统计dto - 按地点")
public class EquipmentRunningStateDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 地点列表
	 */
	@ApiModelProperty(value = "地点列表")
	private List<String> addressList;

	/**
	 * 运行状态列表
	 */
	@ApiModelProperty(value = "运行状态列表")
	private List<Integer> runningStateList;

	/**
	 * 停止状态列表
	 */
	@ApiModelProperty(value = "停止状态列表")
	private List<Integer> stopStateList;

}
