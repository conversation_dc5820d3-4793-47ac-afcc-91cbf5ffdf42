/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.*;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.ai.dto.AiEquipmentAlarmDto;
import com.snszyk.zbusiness.ai.dto.AiEquipmentDto;
import com.snszyk.zbusiness.ai.dto.AiMonitorDto;
import com.snszyk.zbusiness.ai.dto.AiWaveDto;
import com.snszyk.zbusiness.basic.config.HealthIndexConfig;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.entity.EquipmentAi;
import com.snszyk.zbusiness.basic.mapper.EquipmentMapper;
import com.snszyk.zbusiness.basic.service.IEquipmentAiService;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.vo.BindRfidVO;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningMonitorVO;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningStatVO;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@AllArgsConstructor
@Service
public class EquipmentServiceImpl extends BaseServiceImpl<EquipmentMapper, Equipment> implements IEquipmentService {

	private final IEquipmentAiService equipmentAiService;

	@Override
	public IPage<EquipmentDTO> page(IPage<EquipmentDTO> page, EquipmentVO vo) {
		return page.setRecords(baseMapper.page(page, vo));
	}

	@Override
	public DelResultVO checkAndRemoveEquipment(List<Long> ids) {
		return null;
	}

	@Override
	public boolean isCodeDuplicate(Equipment equipment) {
		return lambdaQuery().eq(Equipment::getCode, equipment.getCode())
			.eq(Equipment::getTenantId, AuthUtil.getTenantId())
			.ne(Func.isNotEmpty(equipment.getId()), Equipment::getId, equipment.getId())
			.count() > 0;
	}

	@Override
	public IPage<EquipmentRunningStatExcelDTO> equipmentRunningPage(IPage<EquipmentRunningStatExcelDTO> page, EquipmentRunningStatVO vo, String tenantId) {
		List<EquipmentRunningStatExcelDTO> records = baseMapper.equipmentRunningPage(page, vo, tenantId);
		if (CollectionUtil.isNotEmpty(records)) {
			records.forEach(dto -> {
				//编码：只保留设备的编码
				if (StringUtil.isNotBlank(dto.getCode()) && dto.getCode().contains(StringPool.COLON)) {
					dto.setCode(dto.getCode().substring(dto.getCode().lastIndexOf(StringPool.COLON) + 1));
				}
				//运行率、运行时长、停机时长
				if (dto.getOriginRuntime() != null) {
					long lastTimeInterval = DateUtil.now().getTime() - dto.getOriginRuntime().getTime();
					if (dto.getIsRunning() == 1) {
						dto.setRunningTime(dto.getRunningTime().add(BigDecimal.valueOf(lastTimeInterval)));
					} else {
						dto.setShutdownTime(dto.getShutdownTime().add(BigDecimal.valueOf(lastTimeInterval)));
					}
				}
				BigDecimal totalTime = dto.getRunningTime().add(dto.getShutdownTime());
				if (totalTime.compareTo(BigDecimal.ZERO) > 0) {
					dto.setRunningPercentage(dto.getRunningTime()
						.multiply(BigDecimal.valueOf(100))
						.divide(totalTime, 2, RoundingMode.HALF_UP));
				} else {
					dto.setRunningPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
				}
				dto.setRunningTime(dto.getRunningTime()
					.divide(BigDecimal.valueOf(1000 * 60 * 60), 2, RoundingMode.HALF_UP));
				dto.setShutdownTime(dto.getShutdownTime()
					.divide(BigDecimal.valueOf(1000 * 60 * 60), 2, RoundingMode.HALF_UP));
			});
		}
		return page.setRecords(records);
	}

	@Override
	public EquipmentStatDTO equipmentStat(String tenantId) {
		EquipmentStatDTO stat = baseMapper.equipmentStat(tenantId);
		if (stat.getTotalCount() > 0) {
			stat.setRunningPercent(BigDecimal.valueOf(stat.getRunningCount())
				.divide(BigDecimal.valueOf(stat.getTotalCount()), 4, RoundingMode.HALF_UP));
			stat.setShutdownPercent(BigDecimal.ONE.subtract(stat.getRunningPercent()));
		} else {
			stat.setRunningPercent(BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP));
			stat.setShutdownPercent(BigDecimal.ZERO.setScale(4, RoundingMode.HALF_UP));
		}
		return stat;
	}

	@Override
	public boolean bindAiModel(Long id, String aiModelIds) {
		List<Long> aiModelIdList = Func.toLongList(aiModelIds);
		List<EquipmentAi> list = new ArrayList<>();
		equipmentAiService.removeByEquipment(id);
		// TODO AI模型相关暂时注释，下期（V2.5.0）待与包明明、产品商量后再处理！
		//List<DaqConfig> daqConfigList = daqConfigService.list(Wrappers.<DaqConfig>query().lambda().eq(DaqConfig::getEquipmentId, id).eq(DaqConfig::getIsInvalid, 0));
		//daqConfigList.forEach(daqConfig -> list.addAll(aiModelIdList.stream().map(aiModelId -> {
		//	EquipmentAi equipmentAi = Objects.requireNonNull(BeanUtil.copy(daqConfig, EquipmentAi.class));
		//	equipmentAi.setAiModelId(aiModelId).setCreateTime(DateUtil.now()).setId(null);
		//	return equipmentAi;
		//}).collect(Collectors.toList())));
		return equipmentAiService.saveBatch(list);
	}

	@Override
	public boolean unbindAiModel(Long id, Long aiModelId) {
		return equipmentAiService.removeEquipmentAi(id, aiModelId);
	}

	@Override
	public List<Long> getIdListOfSameCategory(Long equipmentId) {
		return getBaseMapper().getIdListOfSameCategory(equipmentId);
	}

	@Override
	public Integer getMaxSort() {
		Integer maxSort = baseMapper.getMaxSort();
		return maxSort == null ? 0 : maxSort;
	}

	@Override
	public int resetEquipmentNeedSpotCheck(List<Long> equipmentIdList) {
		return baseMapper.resetEquipmentNeedSpotCheck(equipmentIdList);
	}

	@Override
	public boolean bindRfid(BindRfidVO vo) {
		//获取设备、更新rfid字段
		Equipment equipment = baseMapper.selectById(vo.getEquipmentId());
		if (equipment == null) {
			throw new ServiceException("设备不存在");
		}

		//校验重复绑定
		if (StringUtil.isNotBlank(vo.getRfid())) {
			Equipment equipmentInDB = baseMapper.selectOne(new QueryWrapper<Equipment>().lambda()
				.eq(Equipment::getRfid, vo.getRfid())
				.eq(BaseEntity::getIsDeleted, 0));
			if (equipmentInDB != null) {
				throw new ServiceException("该rfid已被绑定");
			}
		}

		equipment.setRfid(vo.getRfid());
		return baseMapper.updateById(equipment) > 0;
	}

	@Override
	public EquipmentDTO detailByRfid(String rfid) {
		Equipment equipment = baseMapper.selectOne(new QueryWrapper<Equipment>().lambda()
			.eq(Equipment::getRfid, rfid)
			.eq(BaseEntity::getIsDeleted, 0));
		return BeanUtil.copyProperties(equipment, EquipmentDTO.class);
	}

	@Override
	public AbnormalTimesDTO abnormalTimes(Long equipmentId) {
		return new AbnormalTimesDTO()
			.setAlarmTimes(baseMapper.alarmTimes(equipmentId))
			.setFaultTimes(baseMapper.faultTimes(equipmentId));
	}

	@Override
	public Equipment getByMonitorId(Long monitorId) {
		return baseMapper.getByMonitorId(monitorId);
	}

	@Override
	public AbnormalEquipmentTotal abnormalEquipmentTotal() {
		return baseMapper.abnormalEquipmentTotal();
	}

	@Override
	public List<ImportantEquipment> importantEquipment(Long id) {
		List<ImportantEquipment> list = baseMapper.importantEquipment(id);
		list.forEach(dto -> {
			String name = DictBizCache.getValue("abnormal_level", dto.getAbnormalLevel());
			dto.setAbnormalLevelName(name);
		});
		return list;
	}

	@Override
	public List<EquipmentTypeTotal> equipmentTypeTotal(Long equipmentId) {
		return baseMapper.equipmentTypeTotal(equipmentId);
	}

	@Override
	public List<EquipmentFrequency> equipmentFrequency() {
		return baseMapper.equipmentFrequency();
	}

	@Override
	public List<AbnormalScatter> abnormalScatter(Long id) {
		return baseMapper.abnormalScatter(id);
	}

	@Override
	public List<EquipmentRunningMonitorDTO> equipmentRunningMonitor(IPage<EquipmentRunningMonitorDTO> page, EquipmentRunningMonitorVO equipment) {
		List<EquipmentRunningMonitorDTO> list = baseMapper.equipmentRunningMonitor(page, equipment);
		list.forEach(dto -> {
			dto.setHealthIndex(HealthIndexConfig.getHealthIndex(dto.getId(), dto.getLifeExpectancy(), dto.getStartDateOfUse(), dto.getAlarmLevel()));
			Attach attach = attachService.getById(dto.getImage());
			String name = DictBizCache.getValue("abnormal_level", dto.getAbnormalLevel());
			dto.setAbnormalLevelName(name);
			dto.setAttach(attach);
		});
		return list;
	}

	@Override
	public List<EquipmentMonitorAbnormalDTO> equipmentMonitorAbnormal(Long equipmentId) {
		return baseMapper.equipmentMonitorAbnormal(equipmentId);
	}

	@Override
	public List<EquipmentDTO> equipmentLedger(IPage<EquipmentDTO> page, EquipmentVO equipment) {
		List<EquipmentDTO> list = baseMapper.equipmentLedger(page, equipment);
		list.forEach(equipmentDTO -> {
			equipmentDTO.setGradeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_GRADE, equipmentDTO.getGrade()));
			equipmentDTO.setPathName(equipmentDTO.getPathName().replaceAll(",", "/"));
			String name = DictBizCache.getValue("abnormal_level", equipmentDTO.getAbnormalLevel());
			equipmentDTO.setAbnormalLevelName(name);
		});

		return list;
	}

	@Override
	public Integer count(String tenantId, String path) {
		return this.lambdaQuery()
			.eq(ObjectUtil.isNotEmpty(tenantId), Equipment::getTenantId, tenantId)
			.like(ObjectUtil.isNotEmpty(path), Equipment::getPath, path)
			.count();
	}

	@Override
	public EquipmentStatDTO equipmentStatistics() {
		EquipmentStatDTO statDTO = new EquipmentStatDTO().init();
		// 查询设备信息
		List<EquipmentDTO> equipmentDTOSList = this.baseMapper.equipmentLedger(new Page<>(1, -1L), new EquipmentVO());
		if (ObjectUtil.isNotEmpty(equipmentDTOSList)) {
			// 总数
			statDTO.setTotalCount(equipmentDTOSList.size());
			// 正常数量
			long normalCount = equipmentDTOSList.stream()
				.filter(dto -> SzykConstant.DB_ADMIN_NON_LOCKED == dto.getAbnormalLevel())
				.count();
			statDTO.setNormalCount((int) normalCount);
			// 异常数量
			statDTO.setAbNormalCount(statDTO.getTotalCount() - statDTO.getNormalCount());
			// 计算异常率
			statDTO.setAbNormalPercent(BigDecimal.valueOf(statDTO.getAbNormalCount()).divide(BigDecimal.valueOf(statDTO.getTotalCount()), 4, RoundingMode.HALF_UP));
			// 计算正常率
			statDTO.setNormalPercent(BigDecimal.valueOf(statDTO.getNormalCount()).divide(BigDecimal.valueOf(statDTO.getTotalCount()), 4, RoundingMode.HALF_UP));
		}
		return statDTO;
	}

	@Override
	public AiEquipmentDto getEquipmentInfo(Long equipmentId) {
		return baseMapper.getEquipmentInfo(equipmentId);
	}

	@Override
	public AiEquipmentDto getEquipmentByMonitorId(Long monitorId) {
		return baseMapper.getEquipmentByMonitorId(monitorId);
	}

	@Override
	public List<AiMonitorDto> getMonitorList(Long equipmentId) {
		return baseMapper.getMonitorList(equipmentId);
	}

	@Override
	public List<AiWaveDto> getWaveList(Long monitorId) {
		return baseMapper.getWaveList(monitorId);
	}

	@Override
	public List<AiWaveDto> waveList(Long monitorId) {
		return baseMapper.waveList(monitorId);
	}

	@Override
	public AiEquipmentAlarmDto equipmentAlarm(Long equipmentId) {
		return baseMapper.equipmentAlarm(equipmentId);
	}

	/**
	 * 获取异常数量
	 *
	 * @param equipmentIdList
	 * @return
	 */
	private Long getAbnormalCount(List<Long> equipmentIdList) {
		if (ObjectUtil.isEmpty(equipmentIdList)) {
			return 0L;
		}
		return 0L;
//		List<AbnormalDTO> abnormalDTOList = abnormalService.listBy(equipmentIdList, 0);
//		if (ObjectUtil.isEmpty(abnormalDTOList)) {
//			return 0L;
//		}
//		return abnormalDTOList.stream()
//			.map(AbnormalDTO::getEquipmentId)
//			.distinct()
//			.count();
	}

	@Resource
	private IAttachService attachService;
}
