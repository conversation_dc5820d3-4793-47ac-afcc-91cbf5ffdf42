/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.config.SidasBizConfig;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.enums.*;
import com.snszyk.zbusiness.basic.mapper.*;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.IWaveService;
import com.snszyk.zbusiness.basic.vo.EqumentSensorListVO;
import com.snszyk.zbusiness.basic.vo.SensorInstanceParamVO;
import com.snszyk.zbusiness.basic.vo.SensorInstanceVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 传感器实例表 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SensorInstanceServiceImpl extends BaseServiceImpl<SensorInstanceMapper, SensorInstance>
	implements ISensorInstanceService {

	private final SensorTypeMapper sensorTypeMapper;
	private final SensorTypeParamMapper sensorTypeParamMapper;
	private final SensorInstanceParamMapper sensorInstanceParamMapper;
	private final MonitorMapper monitorMapper;
	private final IWaveService waveService;
	private final RabbitTemplate rabbitTemplate;
	private final SidasBizConfig sidasBizConfig;
	private final SzykRedis szykRedis;

	// 手动控制每个测点的传感器实例导入
	private final PlatformTransactionManager platformTransactionManager;
	private final TransactionDefinition transactionDefinition;

	@Override
	public IPage<SensorInstanceDTO> page(IPage<SensorInstanceDTO> page, SensorInstanceVO vo) {
		List<SensorInstanceDTO> records = baseMapper.page(page, vo);

		// 获取传感器实例的参数信息
		if (Func.isNotEmpty(records)) {
			List<SensorInstanceParam> sensorInstanceParamList = sensorInstanceParamMapper
				.selectList(Wrappers.<SensorInstanceParam>lambdaQuery()
					.in(SensorInstanceParam::getInstanceId, records.stream()
						.map(SensorInstanceDTO::getId)
						.collect(Collectors.toList())));

			if (Func.isNotEmpty(sensorInstanceParamList)) {
				records.forEach(record -> {
					// 传感器实例参数列表
					record.setSensorInstanceParamList(sensorInstanceParamList.stream()
						.filter(param -> param.getInstanceId().equals(record.getId()))
						.map(param -> BeanUtil.copyProperties(param, SensorInstanceParamDTO.class))
						.collect(Collectors.toList()));
					Object obj = szykRedis
						.get(record.getCode() + StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode());
					String online = String.valueOf(obj);
					if (online != null) {
						// 在线
						try {
							record.setOnline(Integer.parseInt(online));
						} catch (Exception e) {

						}

					}
					// record.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE,
					// record.getOnline()));

					// 是否无线
					record.setIsWirelessName(DictBizCache.getValue(DictBizEnum.SENSOR_TYPE, record.getIsWireless()));

					// 安装方向
					if (Func.isNotEmpty(record.getInstallDirection())) {
						record.setInstallDirectionName(DictBizCache.getValue(DictBizEnum.SENSOR_MEASURE_DIRECTION,
							record.getInstallDirection()));
					}

					// 传感器生产厂家
					if (Func.isNotEmpty(record.getSupplier())) {
						record.setSupplierName(
							DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, record.getSupplier()));
					}

					// 传感器类型名称
					if (Func.isNotEmpty(record.getCategory())) {
						record.setCategoryName(
							DictBizCache.getValue(DictBizEnum.SENSOR_CATEGORY, record.getCategory()));
					}
				});
			}
		}

		return page.setRecords(records);
	}

	@Override
	public List<SensorInstanceDTO> listByMonitorId(Long monitorId) {
		return baseMapper.listByMonitorId(monitorId);
	}

	@Override
	public List<SensorInstance> getSameTypeSensorCodeList(String sensorCode, List<Long> equipmentIdList) {
		return baseMapper.getSameTypeSensorCodeList(sensorCode, equipmentIdList);
	}

	@Override
	public int unbindByEquipment(Long equipmentId) {
		return baseMapper.unbindByEquipment(equipmentId);
	}

	@Override
	public int unbindByMonitor(List<Long> monitorIdList) {
		return baseMapper.unbindByMonitor(monitorIdList);
	}

	@Override
	public List<SensorInstanceDTO> querySensorInfos(List<Long> monitorIdList) {
		List<SensorInstanceDTO> list = baseMapper.querySensorInfos(monitorIdList);
		if (Func.isNotEmpty(list)) {
			list.forEach(dto -> {
				dto.setSupplierName(DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, dto.getSupplier()));
				// 查询实例参数
				SensorInstanceParam powerParam = sensorInstanceParamMapper.selectOne(Wrappers
					.<SensorInstanceParam>query().lambda()
					.eq(SensorInstanceParam::getInstanceId, dto.getId())
					.eq(SensorInstanceParam::getSampleDataType, SampledDataTypeEnum.SENSOR_POWER.getCode()));
				// 传感器电量
				if (Func.isNotEmpty(powerParam)) {
					String power = szykRedis.get(dto.getCode() + StringPool.COLON + powerParam.getId()
						+ StringPool.COLON + powerParam.getSampleDataType());
					if (Func.isNotEmpty(power)) {
						BigDecimal batteryPower = BigDecimal.valueOf(Func.toDouble(szykRedis.get(dto.getCode()
							+ StringPool.COLON + powerParam.getId() + StringPool.COLON
							+ powerParam.getSampleDataType())));
						dto.setBatteryPower(batteryPower);
					}
				}

				// 传感器在线状态
				dto.setOnline(szykRedis.get(dto.getCode()
					+ StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode()) != null ? 1 : 0);
				dto.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, dto.getOnline()));
			});
		}
		return list;
	}

	@Override
	public int bindCollectionStation(List<String> sensorCodeList, Long stationId) {
		return baseMapper.bindCollectionStation(sensorCodeList, stationId);
	}

	@Override
	public int unbindCollectionStation(List<Long> stationId) {
		return baseMapper.unbindCollectionStation(stationId);
	}

	@Override
	public SensorInstanceDTO detailByCode(String code) {
		return baseMapper.detailByCode(code);
	}

	@Override
	public int unbindBySensorInstance(Long sensorInstanceId) {
		return baseMapper.unbindBySensorInstance(sensorInstanceId);
	}

	@Override
	public IPage<SensorInstanceDTO> portalPage(IPage<SensorInstanceDTO> page, SensorInstanceVO vo) {
		List<SensorInstanceDTO> records = baseMapper.portalPage(page, vo);
		if (Func.isNotEmpty(records)) {
			records.forEach(record -> {
				// 有线无线
				record.setIsWirelessName(DictBizCache.getValue(DictBizEnum.SENSOR_TYPE, record.getIsWireless()));
				SensorInstanceParam powerParam = sensorInstanceParamMapper.selectOne(Wrappers
					.<SensorInstanceParam>query().lambda()
					.eq(SensorInstanceParam::getInstanceId, record.getId())
					.eq(SensorInstanceParam::getSampleDataType, SampledDataTypeEnum.SENSOR_POWER.getCode()));
				// 传感器电量
				if (Func.isNotEmpty(powerParam)) {
					String power = szykRedis.get(record.getCode() + StringPool.COLON + powerParam.getId()
						+ StringPool.COLON + powerParam.getSampleDataType());
					if (Func.isNotEmpty(power)) {
						BigDecimal batteryPower = BigDecimal.valueOf(Func.toDouble(szykRedis.get(record.getCode()
							+ StringPool.COLON + powerParam.getId() + StringPool.COLON
							+ powerParam.getSampleDataType())));
						record.setBatteryPower(batteryPower);
					}
				}

				// 传感器在线状态
				record.setOnline(szykRedis.get(record.getCode()
					+ StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode()) != null ? 1 : 0);
				record.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, record.getOnline()));
			});
			// Collections.sort(records,
			// Comparator.comparing(SensorInstanceDTO::getOnline));
		}
		return page.setRecords(records);
	}

	@Override
	public void exportExcel(SensorInstanceVO vo, HttpServletResponse response) {
		List<SensorInstanceDTO> list = baseMapper.listExport();
		List<SensorInstanceExcelDTO> resultList = list.stream().map(sensorInstance -> {
			SensorInstanceExcelDTO excelDTO = Objects
				.requireNonNull(BeanUtil.copy(sensorInstance, SensorInstanceExcelDTO.class));
			excelDTO.setIsWirelessName(DictBizCache.getValue(DictBizEnum.SENSOR_TYPE, sensorInstance.getIsWireless()));
			SensorInstanceParam powerParam = sensorInstanceParamMapper
				.selectOne(Wrappers.<SensorInstanceParam>query().lambda()
					.eq(SensorInstanceParam::getInstanceId, sensorInstance.getId())
					.eq(SensorInstanceParam::getSampleDataType, SampledDataTypeEnum.SENSOR_POWER.getCode()));
			// 传感器电量
			if (Func.isNotEmpty(powerParam)) {
				String power = szykRedis.get(sensorInstance.getCode() + StringPool.COLON + powerParam.getId()
					+ StringPool.COLON + powerParam.getSampleDataType());
				if (Func.isNotEmpty(power)) {
					BigDecimal batteryPower = BigDecimal.valueOf(Func.toDouble(szykRedis.get(sensorInstance.getCode()
						+ StringPool.COLON + powerParam.getId() + StringPool.COLON
						+ powerParam.getSampleDataType())));
					excelDTO.setBatteryPower(batteryPower + StringPool.PERCENT);
				}
			}
			// SensorInstanceParam onlineParam =
			// sensorInstanceParamMapper.selectOne(Wrappers.<SensorInstanceParam>query().lambda()
			// .eq(SensorInstanceParam::getInstanceId, sensorInstance.getId())
			// .eq(SensorInstanceParam::getSampleDataType,
			// SampledDataTypeEnum.SENSOR_ONLINE.getCode()));
			// 传感器在线状态
			sensorInstance.setOnline(szykRedis.get(
				sensorInstance.getCode() + StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode()) != null
				? 1
				: 0);
			excelDTO.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, sensorInstance.getOnline()));
			// 最近更新时间
			// String updateTime =
			// szykRedis.get(EolmConstant.Cache.SENSOR_LATEST_DATA_TIME_PREFIX +
			// sensorInstance.getCode());
			// if (Func.isNotEmpty(updateTime)) {
			// excelDTO.setUpdateTime(DateUtil.parse(updateTime,
			// DateUtil.PATTERN_DATETIME));
			// }
			return excelDTO;
		}).collect(Collectors.toList());
		try {
			response.addHeader("Content-Type", "application/octet-stream");
			response.setCharacterEncoding("UTF-8");
			response.addHeader("Content-Disposition",
				"attachment;filename=" + URLEncoder.encode("传感器工作状态.xlsx", "UTF-8"));
			EasyExcel.write(response.getOutputStream(), SensorInstanceExcelDTO.class)
				.autoCloseStream(Boolean.FALSE)
				.sheet("传感器工作状态")
				.doWrite(resultList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public boolean bindSensorInstance(Long monitorId, List<SensorInstanceVO> sensorInstanceList) {
		// 获取测点
		Monitor monitor = monitorMapper.selectById(monitorId);
		if (monitor == null) {
			throw new ServiceException("当前部位已删除，请刷新后重试!");
		}
		// 绑定传感器实例
		if (Func.isNotEmpty(sensorInstanceList)) {
			// 根据category做不同校验
			checkSensorList(sensorInstanceList);

			// 测点绑定传感器实例：monitorId、equipmentId、installDirection、number、phase
			List<SensorInstance> instanceList = sensorInstanceList.stream().map(vo -> {
				SensorInstance sensorInstance = BeanUtil.copy(vo, SensorInstance.class);
				assert sensorInstance != null;
				sensorInstance.setMonitorId(monitorId)
					.setEquipmentId(monitor.getEquipmentId());
				return sensorInstance;
			}).collect(Collectors.toList());
			boolean updateBatch = this.saveOrUpdateBatch(instanceList);
			log.info("更新传感器实例：{}", updateBatch);

			// 只保留新增的 -> 生成波形 & 门限
			List<SensorInstanceVO> addedSensorInstanceList = sensorInstanceList.stream()
				.filter(sensorInstanceVO -> Func.isEmpty(sensorInstanceVO.getMonitorId()))
				.collect(Collectors.toList());
			if (Func.isEmpty(addedSensorInstanceList)) {
				log.info("暂无新增绑定的传感器实例，无需生成波形和门限！");
				return true;
			}

			// 生成波形
			Map<Long, Long> waveMap = generateWave(monitorId, addedSensorInstanceList);

			// 生成报警门限
			generateThreshold(monitorId, addedSensorInstanceList, waveMap);
		} else {
			throw new ServiceException("传感器实例列表不能为空!");
		}
		return true;
	}

	@Override
	public boolean removeAlarmThresholdBySensorCode(String sensorCode) {
		return baseMapper.removeAlarmThresholdBySensorCode(sensorCode) >= 0;
	}

	@Override
	public List<EqumentSensorListDTO> equmentSensorList(EqumentSensorListVO vo) {
		List<EqumentSensorListDTO> list = this.baseMapper.equmentSensorList(vo);
		list.forEach(action -> action.configOnline(szykRedis));
		return list;
	}

	@Override
	public List<EquipmentSensorDTO> equipmentSensor(Long equipmentId) {
		List<EquipmentSensorDTO> list = this.baseMapper.equipmentSensor(equipmentId);
		return list;
	}

	/**
	 * 生成报警门限
	 *
	 * @param monitorId          测点id
	 * @param sensorInstanceList 新绑定的传感器实例列表
	 */
	private void generateThreshold(Long monitorId, List<SensorInstanceVO> sensorInstanceList, Map<Long, Long> waveMap) {
		log.info("初始化报警门限：{}", sensorInstanceList);
		sensorInstanceList.forEach(sensorInstanceVO -> {
			sensorInstanceVO.setMonitorId(monitorId);
			List<SensorInstanceParamVO> paramList = sensorInstanceVO.getSensorInstanceParamList();
			paramList.forEach(paramVO -> {
				paramVO.setWaveId(waveMap.get(paramVO.getId()));
				boolean flag = Arrays.stream(RealNonVibrationDataEnum.values())
					.anyMatch(t -> Func.equals(t.getCode(), paramVO.getSampleDataType()));
				if (!flag) {
					if (VibrationDataTypeEnum.STRESS != VibrationDataTypeEnum.getByCode(paramVO.getSampleDataType())
						&& VibrationDataTypeEnum.ELECTRIC != VibrationDataTypeEnum
						.getByCode(paramVO.getSampleDataType())) {
						paramVO.setMeasureDirection(this.getMeasureDirection(sensorInstanceVO.getAxisCount(),
							sensorInstanceVO.getInstallDirection(), paramVO.getAxialDirection()));
					}
				}
			});
		});
		rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_ALARM_THRESHOLD,
			EolmConstant.Rabbit.ROUTING_ALARM_THRESHOLD_ADD, sensorInstanceList);
	}

	/**
	 * 生成波形：温振一体、应力波、电流、转速
	 *
	 * @param monitorId          测点id
	 * @param sensorInstanceList 新绑定的传感器实例列表
	 */
	private Map<Long, Long> generateWave(Long monitorId, List<SensorInstanceVO> sensorInstanceList) {
		List<Wave> waveList = new ArrayList<>();
		Map<Long, Long> waveMap = new HashMap<>(16);
		// 1、温振一体传感器
		List<SensorInstanceVO> tempVibrateSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.TEMP_VIBRATE.getCode().equals(s.getCategory()))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(tempVibrateSensorList)) {
			tempVibrateSensorList.forEach(tempVibrateSensor -> {
				// 振动 + 温度参数列表
				List<SensorInstanceParamVO> waveParamList = tempVibrateSensor.getSensorInstanceParamList().stream()
					.filter(paramVO -> paramVO.getVibrationType() == 1
						|| SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode()
						.equals(paramVO.getSampleDataType()))
					.collect(Collectors.toList());
				if (Func.isNotEmpty(waveParamList)) {
					SensorType sensorType = sensorTypeMapper.selectById(tempVibrateSensor.getTypeId());
					waveParamList.forEach(paramVO -> {
						Integer measureDirection = getMeasureDirection(tempVibrateSensor.getAxisCount(),
							tempVibrateSensor.getInstallDirection(), paramVO.getAxialDirection());
						Wave wave = new Wave().setMonitorId(monitorId).setSensorCode(tempVibrateSensor.getCode())
							.setSampleDataType(paramVO.getSampleDataType())
							.setSensorInstanceParamId(paramVO.getId())
							.setMeasureDirection(measureDirection).setCreateTime(DateUtil.now())
							.setHaltLine(
								BigDecimal.valueOf(sidasBizConfig.sidasBizProperties().getRunningStateMin()))
							.setWaveName(getTempVibrateWaveName(measureDirection, sensorType, tempVibrateSensor,
								paramVO));
						waveList.add(wave);
					});
				}
			});
		} else {
			log.info("暂无温振一体传感器，无需生成温振一体波形！");
		}
		// 2、应力波传感器
		List<SensorInstanceVO> stressWaveSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.STRESS_WAVE.getCode().equals(s.getCategory()))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(stressWaveSensorList)) {
			stressWaveSensorList.forEach(stressSensor -> {
				List<SensorInstanceParamVO> waveParamList = stressSensor.getSensorInstanceParamList().stream()
					.filter(paramVO -> paramVO.getVibrationType() == 1
						|| SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode()
						.equals(paramVO.getSampleDataType()))
					.collect(Collectors.toList());
				if (Func.isNotEmpty(waveParamList)) {
					SensorType sensorType = sensorTypeMapper.selectById(stressSensor.getTypeId());
					waveParamList.forEach(paramVO -> {
						Wave wave = new Wave().setMonitorId(monitorId).setSensorCode(stressSensor.getCode())
							.setSampleDataType(paramVO.getSampleDataType())
							.setSensorInstanceParamId(paramVO.getId())
							.setMeasureDirection(stressSensor.getInstallDirection())
							.setNumber(stressSensor.getNumber())
							.setWaveName(getStressWaveName(sensorType, stressSensor, paramVO))
							.setCreateTime(DateUtil.now())
							.setHaltLine(
								BigDecimal.valueOf(sidasBizConfig.sidasBizProperties().getRunningStateMin()));
						waveList.add(wave);
					});
				}
			});
		} else {
			log.info("暂无应力波传感器，无需生成应力波形！");
		}
		// 3、电流传感器
		List<SensorInstanceVO> electricSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.ELECTRIC.getCode().equals(s.getCategory()))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(electricSensorList)) {
			electricSensorList.forEach(electricSensor -> {
				List<SensorInstanceParamVO> electricParamList = electricSensor.getSensorInstanceParamList().stream()
					.filter(paramVO -> SampledDataTypeEnum.ELECTRIC.getCode().equals(paramVO.getSampleDataType())
						&& paramVO.getVibrationType() == 1)
					.collect(Collectors.toList());
				if (Func.isNotEmpty(electricParamList)) {
					SensorInstanceParamVO electricParam = electricParamList.get(0);
					Wave wave = new Wave().setMonitorId(monitorId).setSensorCode(electricSensor.getCode())
						.setSampleDataType(electricParam.getSampleDataType())
						.setSensorInstanceParamId(electricParam.getId())
						.setWaveName(SampledDataTypeEnum.ELECTRIC.getName() + StringPool.DASH
							+ electricSensor.getPhase())
						.setPhase(electricSensor.getPhase()).setCreateTime(DateUtil.now())
						.setHaltLine(BigDecimal.valueOf(sidasBizConfig.sidasBizProperties().getRunningStateMin()));
					waveList.add(wave);
				}
			});
		} else {
			log.info("暂无电流传感器，无需生成电流波形！");
		}
		// 4、转速传感器
		List<SensorInstanceVO> rpmSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.RPM.getCode().equals(s.getCategory()))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(rpmSensorList)) {
			SensorInstanceVO rpmSensor = rpmSensorList.get(0);
			List<SensorInstanceParamVO> rpmParamList = rpmSensor.getSensorInstanceParamList().stream()
				.filter(paramVO -> SampledDataTypeEnum.RPM.getCode().equals(paramVO.getSampleDataType()))
				.collect(Collectors.toList());
			if (Func.isNotEmpty(rpmParamList)) {
				SensorInstanceParamVO rpmParam = rpmParamList.get(0);
				Wave wave = new Wave().setMonitorId(monitorId).setSensorCode(rpmSensor.getCode())
					.setSampleDataType(rpmParam.getSampleDataType()).setSensorInstanceParamId(rpmParam.getId())
					.setWaveName(SampledDataTypeEnum.RPM.getName()).setCreateTime(DateUtil.now())
					.setHaltLine(BigDecimal.valueOf(sidasBizConfig.sidasBizProperties().getRunningStateMin()));
				waveList.add(wave);
			}
		} else {
			log.info("暂无转速传感器，无需生成转速波形！");
		}
		// 生成波形
		if (Func.isNotEmpty(waveList)) {
			boolean saveWaveBatch = waveService.saveBatch(waveList);
			waveList.forEach(wave -> {
				waveMap.put(wave.getSensorInstanceParamId(), wave.getId());
				// 停机线缓存Redis（只针对加速度波形）
				if (SampledDataTypeEnum.ACCELERATION == SampledDataTypeEnum.getByCode(wave.getSampleDataType())) {
					String haltLineKey = String.format(HALT_LINE, wave.getSensorCode(), StringPool.COLON);
					szykRedis.set(haltLineKey, wave.getHaltLine());
				}
			});
			log.info("测点({})绑定传感器，生成了{}条波形：{}", monitorId, waveList.size(), saveWaveBatch);
		} else {
			log.info("测点({})绑定传感器，暂无需生成波形！", monitorId);
		}
		return waveMap;
	}

	/**
	 * 获取应力波波形名称：应力波、温度
	 *
	 * @param sensorType   传感器类型
	 * @param stressSensor 传感器实例
	 * @param paramVO      实例参数
	 * @return
	 */
	private String getStressWaveName(SensorType sensorType, SensorInstanceVO stressSensor,
									 SensorInstanceParamVO paramVO) {
		if (SampledDataTypeEnum.STRESS.getCode().equals(paramVO.getSampleDataType())) {
			// 应力波波形 - 2K01应力波(0.5-1000)
			BigDecimal cutoffFreq = paramVO.getSamplingFreq()
				.multiply(new BigDecimal("1000"))
				.divide(new BigDecimal("2.56"), 2, RoundingMode.HALF_UP);
			BigDecimal prefix = BigDecimal.valueOf(paramVO.getSamplingPoints())
				.divide(BigDecimal.valueOf(1024), 2, RoundingMode.HALF_UP);
			return prefix.stripTrailingZeros().toPlainString() + "k " + stressSensor.getNumber()
				+ SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()).getName()
				+ "(" + sensorType.getInitialFreq().stripTrailingZeros().toPlainString()
				+ "-" + cutoffFreq.stripTrailingZeros().toPlainString() + ")";
		} else if (SampledDataTypeEnum.ACCELERATION.getCode().equals(paramVO.getSampleDataType())
			|| SampledDataTypeEnum.VELOCITY.getCode().equals(paramVO.getSampleDataType())
			|| SampledDataTypeEnum.DISPLACEMENT.getCode().equals(paramVO.getSampleDataType())) {
			// 应力波传感器振动波形（加速度、速度、位移） - 2K01水平加速度(0.5-1000)
			BigDecimal cutoffFreq = paramVO.getSamplingFreq()
				.multiply(new BigDecimal("1000"))
				.divide(new BigDecimal("2.56"), 2, RoundingMode.HALF_UP);
			BigDecimal prefix = BigDecimal.valueOf(paramVO.getSamplingPoints())
				.divide(BigDecimal.valueOf(1024), 2, RoundingMode.HALF_UP);
			return prefix.stripTrailingZeros().toPlainString()
				+ "k " + stressSensor.getNumber()
				+ MeasureDirectionEnum.getByCode(stressSensor.getInstallDirection()).getName()
				+ SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()).getName()
				+ "(" + sensorType.getInitialFreq().stripTrailingZeros().toPlainString()
				+ "-" + cutoffFreq.stripTrailingZeros().toPlainString() + ")";
		} else {
			// 应力波温度：序号+温度
			return stressSensor.getNumber() + SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()).getName();
		}
	}

	/**
	 * 校验传感器列表 - 根据category做不同校验！
	 *
	 * @param sensorInstanceList 传感器实例列表
	 */
	private void checkSensorList(List<SensorInstanceVO> sensorInstanceList) {
		// 1、非点检仪温振一体传感器
		List<SensorInstanceVO> tempVibrateSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.TEMP_VIBRATE.getCode().equals(s.getCategory())
				&& (s.getIsSpotCheck() == null || s.getIsSpotCheck() == 0))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(tempVibrateSensorList)) {
			// 最多只能绑定1个三轴传感器
			if (tempVibrateSensorList.stream().filter(s -> s.getAxisCount() == 3).count() > 1) {
				throw new ServiceException("最多只能绑定1个三轴温振一体传感器!");
			}
			// 最多只能绑定3个单轴传感器
			if (tempVibrateSensorList.stream().filter(s -> s.getAxisCount() == 1).count() > 3) {
				throw new ServiceException("最多只能绑定3个单轴温振一体传感器!");
			}
			// 不能同时绑定单轴和三轴传感器
			if (tempVibrateSensorList.stream().anyMatch(s -> s.getAxisCount() == 1)
				&& tempVibrateSensorList.stream().anyMatch(s -> s.getAxisCount() == 3)) {
				throw new ServiceException("不能同时绑定单轴和三轴温振一体传感器!");
			}
			// 如果是单轴的，不能有重复的安装方向
			if (tempVibrateSensorList.stream().anyMatch(s -> s.getAxisCount() == 1)) {
				List<Integer> installDirectionList = tempVibrateSensorList.stream()
					.filter(s -> s.getAxisCount() == 1)
					.map(SensorInstanceVO::getInstallDirection)
					.collect(Collectors.toList());
				if (installDirectionList.stream().distinct().count() != installDirectionList.size()) {
					throw new ServiceException("温振一体传感器不能有重复的安装方向!");
				}
			}
		}

		// 2、应力波传感器
		List<SensorInstanceVO> stressWaveSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.STRESS_WAVE.getCode().equals(s.getCategory()))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(stressWaveSensorList)) {
			// 安装方向非空
			long noInstallDirectionCount = stressWaveSensorList.stream()
				.filter(s -> Func.isEmpty(s.getInstallDirection()))
				.count();
			if (noInstallDirectionCount > 0) {
				throw new ServiceException("应力波传感器必须有安装方向！");
			}

			// 编号唯一
			long numberCount = stressWaveSensorList.stream()
				.map(SensorInstance::getNumber)
				.distinct()
				.count();
			if (numberCount != stressWaveSensorList.size()) {
				throw new ServiceException("应力波传感器不能有重复的编号!");
			}
		}

		// 3、电流传感器
		List<SensorInstanceVO> electricSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.ELECTRIC.getCode().equals(s.getCategory()))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(electricSensorList)) {
			// 最多有3个 且 相位唯一
			long phaseCount = electricSensorList.stream()
				.map(SensorInstance::getPhase)
				.distinct()
				.count();
			if (phaseCount != electricSensorList.size()) {
				throw new ServiceException("电流传感器不能有重复的相位!");
			}

			if (phaseCount > 3) {
				throw new ServiceException("电流传感器不能超过3个!");
			}
		}

		// 4、转速传感器
		List<SensorInstanceVO> rpmSensorList = sensorInstanceList.stream()
			.filter(s -> SensorCategoryEnum.RPM.getCode().equals(s.getCategory()))
			.collect(Collectors.toList());
		if (Func.isNotEmpty(rpmSensorList) && rpmSensorList.size() > 1) {
			throw new ServiceException("转速传感器不能超过1个!");
		}
	}

	/**
	 * 根据传感器类型、传感器实例、传感器实例参数生成波形名称
	 *
	 * @param measureDirection 波形测量方向
	 * @param sensorType       传感器类型
	 * @param sensorInstanceVO 传感器实例
	 * @param paramVO          传感器实例参数
	 * @return
	 */
	private String getTempVibrateWaveName(Integer measureDirection, SensorType sensorType,
										  SensorInstanceVO sensorInstanceVO, SensorInstanceParamVO paramVO) {
		if (VibrationTypeEnum.IS_VIBRATION.getCode().equals(paramVO.getVibrationType())) {
			// 振动波形（加速度、速度、位移） - 2K水平加速度(0.5-1000)
			BigDecimal cutoffFreq = paramVO.getSamplingFreq()
				.multiply(new BigDecimal("1000"))
				.divide(new BigDecimal("2.56"), 2, RoundingMode.HALF_UP);
			BigDecimal prefix = BigDecimal.valueOf(paramVO.getSamplingPoints())
				.divide(BigDecimal.valueOf(1024), 2, RoundingMode.HALF_UP);
			return prefix.stripTrailingZeros().toPlainString()
				+ "k " + MeasureDirectionEnum.getByCode(measureDirection).getName()
				+ SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()).getName()
				+ "(" + sensorType.getInitialFreq().stripTrailingZeros().toPlainString()
				+ "-" + cutoffFreq.stripTrailingZeros().toPlainString() + ")";
		} else {
			// 非振动波形（温度）
			if (sensorInstanceVO.getAxisCount() == 1) {
				return MeasureDirectionEnum.getByCode(measureDirection).getName()
					+ SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()).getName();
			} else if (sensorInstanceVO.getAxisCount() == 3) {
				return SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()).getName();
			} else {
				return SampledDataTypeEnum.getByCode(paramVO.getSampleDataType()).getName();
			}
		}
	}

	/**
	 * 根据安装方向和轴方向获取实际测量方向
	 *
	 * @param axisCount        轴向数
	 * @param installDirection 传感器实例安装方向：水平、垂直、轴向
	 * @param axialDirection   传感器参数的轴方向：X轴、Y轴、Z轴
	 * @return
	 */
	public Integer getMeasureDirection(Integer axisCount, Integer installDirection, Integer axialDirection) {
		if (axisCount == 0) {
			// 纯非振动传感器（温度、电压、电量传感器等）测量方向为null
			return null;
		} else if (axisCount == 1) {
			// 单轴传感器的安装方向即是实际测量方向
			return installDirection;
		} else if (axisCount == 3) {
			// 三轴传感器根据安装方向和轴方向获取实际测量方向
			switch (Objects.requireNonNull(MeasureDirectionEnum.getByCode(installDirection))) {
				case HORIZONTAL:
					// 3轴传感器水平安装：x -> 垂直； y -> 轴向；z -> 水平
					if (MeasureDirectionEnum.HORIZONTAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.VERTICAL.getCode();
					} else if (MeasureDirectionEnum.VERTICAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.AXIAL.getCode();
					} else {
						return MeasureDirectionEnum.HORIZONTAL.getCode();
					}
				case VERTICAL:
					// 3轴传感器垂直安装：x -> 水平； y -> 轴向；z -> 垂直
					if (MeasureDirectionEnum.HORIZONTAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.HORIZONTAL.getCode();
					} else if (MeasureDirectionEnum.VERTICAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.AXIAL.getCode();
					} else {
						return MeasureDirectionEnum.VERTICAL.getCode();
					}
				case AXIAL:
					// 3轴传感器轴向安装：x -> 水平； y -> 垂直；z -> 轴向
					return axialDirection;
				default:
					throw new ServiceException("传感器实例安装方向不正确！");
			}
		} else {
			log.warn("非法的传感器实例，axisCount = {}", axisCount);
			return null;
		}
	}

	@Override
	public void exportInstanceBindTemplate(HttpServletResponse response) {
		List<SensorInstanceBindExcelDTO> list = baseMapper.listSensorInstanceBindExport();
		if (Func.isNotEmpty(list)) {
			for (int i = 0; i < list.size(); i++) {
				// 设置序号
				list.get(i).setSerialNumber(i + 1);
			}
		}
		try {
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("UTF-8");
			response.addHeader("Content-Disposition",
				"attachment;filename=" + URLEncoder.encode("传感器实例导入模板.xlsx", "UTF-8"));
			EasyExcel.write(response.getOutputStream(), SensorInstanceBindExcelDTO.class)
				.sheet("传感器实例")
				.doWrite(list);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 导入传感器实例
	 *
	 * @param data      传感器实例列表
	 * @param isCovered 是否覆盖 - 暂时忽略
	 */
	@Override
	public void importSensorInstance(List<SensorInstanceBindExcelDTO> data, boolean isCovered) {
		log.info("【开始】导入传感器实例。数据条数 = {}", Func.isNotEmpty(data) ? data.size() : 0);
		Integer successNumber = 0;
		Integer firstFailNumber = 0;
		String failureMonitorPath = "";
		String failureMessage = "";

		try {
			// 按照测点导入
			Map<String, List<SensorInstanceBindExcelDTO>> sensorInstanceListByMonitor = data.stream()
				.filter(dto -> Func.isNotEmpty(dto.getPathName()))
				.map(dto -> {
					String pathName = dto.getPathName().replace(StringPool.COMMA, StringPool.SLASH);
					dto.setPathName(pathName);
					return dto;
				})
				.collect(Collectors.groupingBy(SensorInstanceBindExcelDTO::getPathName));
			// 按照key排序
			sensorInstanceListByMonitor = sensorInstanceListByMonitor.entrySet().stream()
				.sorted(Map.Entry.comparingByKey())
				.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1,
					LinkedHashMap::new));
			log.info("导入传感器实例。测点数 = {}", sensorInstanceListByMonitor.size());
			for (Map.Entry<String, List<SensorInstanceBindExcelDTO>> entry : sensorInstanceListByMonitor.entrySet()) {
				String monitorPath = entry.getKey();
				List<SensorInstanceBindExcelDTO> currentSensorInstanceList = entry.getValue();
				log.info("导入传感器实例。测点 = {}，绑定传感器数 = {}", monitorPath, currentSensorInstanceList.size());

				// 设置失败时的错误数据（当前测点的最小序号）
				firstFailNumber = currentSensorInstanceList.stream()
					.map(SensorInstanceBindExcelDTO::getSerialNumber)
					.min(Integer::compareTo)
					.orElse(0);
				failureMonitorPath = monitorPath;

				// 手动控制事务 - 按测点导入传感器实例
				TransactionStatus transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);
				try {
					importSensorInstanceByMonitor(monitorPath, currentSensorInstanceList);
					platformTransactionManager.commit(transactionStatus);
				} catch (Exception e) {
					platformTransactionManager.rollback(transactionStatus);
					throw e;
				}

				// 更新成功数
				successNumber += currentSensorInstanceList.size();
			}

			// 全部导入成功之后清空错误数据
			firstFailNumber = 0;
			failureMonitorPath = "";
		} catch (Exception e) {
			log.warn("导入传感器实例失败！", e);
			failureMessage = Func.isNotEmpty(e.getMessage()) ? e.getMessage() : "导入失败！错误信息：" + e.toString();
		} finally {
			log.info(
				"【结束】导入传感器实例。successNumber = {}, firstFailNumber = {}, failureMonitorPath = {}, failureMessage = {}",
				successNumber, firstFailNumber, failureMonitorPath, failureMessage);
			szykRedis.set(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_SUCCESS_NUMBER, successNumber);
			szykRedis.set(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_FIRST_FAIL_NUMBER, firstFailNumber);
			szykRedis.set(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_FAILURE_MONITOR_PATH, failureMonitorPath);
			szykRedis.set(EolmConstant.Cache.SENSOR_INSTANCE_IMPORT_FAILURE_MESSAGE, failureMessage);
		}
	}

	/**
	 * 按照测点导入其传感器实例列表
	 *
	 * @param monitorPath               测点路径
	 * @param currentSensorInstanceList 传感器实例列表
	 */
	private void importSensorInstanceByMonitor(String monitorPath,
											   List<SensorInstanceBindExcelDTO> currentSensorInstanceList) {
		// 获取测点
		Monitor monitor = monitorMapper.selectOne(Wrappers.<Monitor>lambdaQuery()
			.eq(Monitor::getPathName, monitorPath));
		if (Func.isEmpty(monitor)) {
			throw new ServiceException("测点不存在！路径：" + monitorPath);
		}

		// 1 - 生成传感器实例
		List<SensorInstanceVO> sensorInstanceVOList = generateSensorInstance(monitor, currentSensorInstanceList);

		// 2 - 绑定传感器实例和测点
		if (Func.isNotEmpty(sensorInstanceVOList)) {
			bindSensorInstance(monitor.getId(), sensorInstanceVOList);
		}
	}

	/**
	 * 根据导入Excel中的传感器数据生成新的传感器实例
	 *
	 * @param monitor                  测点
	 * @param importSensorInstanceList 传感器列表
	 * @return 如果有新的传感器实例，则返回所有和此测点绑定的传感器实例列表（包括新增的传感器实例和之前已绑定的），
	 * 如果没有新生成的传感器实例，则返回null - 不再进行后续的绑定操作。
	 */
	private List<SensorInstanceVO> generateSensorInstance(Monitor monitor,
														  List<SensorInstanceBindExcelDTO> importSensorInstanceList) {
		List<SensorInstanceVO> result = new ArrayList<>();

		// 生成传感器实例
		for (SensorInstanceBindExcelDTO dto : importSensorInstanceList) {
			// 校验Excel数据
			checkRowData(dto);

			// 查询传感器实例是否已存在
			SensorInstance sensorInstance = this.getOne(Wrappers.<SensorInstance>lambdaQuery()
				.eq(SensorInstance::getCode, dto.getSensorCode()));
			if (Func.isNotEmpty(sensorInstance)) {
				// 已存在传感器实例 & 且绑定到其他测点
				if (Func.isNotEmpty(sensorInstance.getMonitorId())
					&& !monitor.getId().equals(sensorInstance.getMonitorId())) {
					Monitor otherMonitor = monitorMapper.selectById(sensorInstance.getMonitorId());
					throw new ServiceException("传感器实例(编码：" + dto.getSensorCode() + ")已存在！但已绑定到其他测点("
						+ otherMonitor.getPathName().replace(StringPool.COMMA, StringPool.SLASH) + ")，序号为："
						+ dto.getSerialNumber());
				}

				SensorInstanceVO sensorInstanceVO = BeanUtil.copyProperties(sensorInstance, SensorInstanceVO.class);
				SensorType sensorType = sensorTypeMapper.selectById(sensorInstance.getTypeId());
				sensorInstanceVO.setCategory(sensorType.getCategory());
				sensorInstanceVO.setAxisCount(sensorType.getAxisCount());
				sensorInstanceVO.setIsSpotCheck(sensorType.getIsSpotCheck());
				// 还未绑定测点：设置绑定时的信息（方向、编号、相位、实力参数列表）
				if (Func.isEmpty(sensorInstanceVO.getMonitorId())) {
					sensorInstanceVO.setInstallDirection(dto.getInstallDirection());
					sensorInstanceVO.setNumber(dto.getNumber());
					sensorInstanceVO.setPhase(dto.getPhase());
					List<SensorInstanceParam> paramList = sensorInstanceParamMapper
						.selectList(Wrappers.<SensorInstanceParam>lambdaQuery()
							.eq(SensorInstanceParam::getInstanceId, sensorInstance.getId()));
					if (Func.isNotEmpty(paramList)) {
						List<SensorInstanceParamVO> paramVOList = paramList.stream()
							.map(param -> BeanUtil.copyProperties(param, SensorInstanceParamVO.class))
							.collect(Collectors.toList());
						sensorInstanceVO.setSensorInstanceParamList(paramVOList);
					}
				}
				result.add(sensorInstanceVO);
			} else {
				// 新增传感器实例
				SensorInstanceVO sensorInstanceVO = insertSensorInstance(dto);
				result.add(sensorInstanceVO);
			}
		}

		return result;
	}

	/**
	 * 校验Excel数据 - 单行数据
	 *
	 * @param dto 传感器实例数据
	 */
	private static void checkRowData(SensorInstanceBindExcelDTO dto) {
		if (Func.isEmpty(dto.getSensorCode()) || Func.isEmpty(dto.getSensorTypeName())
			|| Func.isEmpty(dto.getModel())) {
			throw new ServiceException("传感器编码、传感器名称、传感器型号不能为空！序号为：" + dto.getSerialNumber());
		}
		if (dto.getSensorCode().length() > 50) {
			throw new ServiceException("传感器编码不能超过50个字符！序号为：" + dto.getSerialNumber());
		}
		if (Func.isEmpty(dto.getCategory())) {
			throw new ServiceException("传感器类型不能为空，且只能为[温振一体、应力波、电流、转速]之一！序号为：" + dto.getSerialNumber());
		}
		if (Func.isEmpty(dto.getSupplier())) {
			throw new ServiceException("传感器厂家不能为空，且只能为[航天智控、因联科技、联能、飞英思特]之一！序号为：" + dto.getSerialNumber());
		}
		if (SensorCategoryEnum.TEMP_VIBRATE.getCode().equals(dto.getCategory())
			&& Func.isEmpty(dto.getInstallDirection())) {
			throw new ServiceException("温振一体类型传感器的安装方向不能为空！序号为：" + dto.getSerialNumber());
		}
		if (SensorCategoryEnum.STRESS_WAVE.getCode().equals(dto.getCategory())) {
			if (Func.isEmpty(dto.getNumber()) || Func.isEmpty(dto.getInstallDirection())) {
				throw new ServiceException("应力波类型传感器的安装方向、编号不能为空！序号为：" + dto.getSerialNumber());
			}
			if (dto.getNumber().length() > 20) {
				throw new ServiceException("应力波类型传感器的编号不能超过20个字符！序号为：" + dto.getSerialNumber());
			}
		}
		if (SensorCategoryEnum.ELECTRIC.getCode().equals(dto.getCategory())) {
			if (Func.isEmpty(dto.getPhase())) {
				throw new ServiceException("电流类型传感器的相位不能为空！序号为：" + dto.getSerialNumber());
			}
			if (!Arrays.asList("A,B,C".split(StringPool.COMMA)).contains(dto.getPhase())) {
				throw new ServiceException("电流类型传感器的相位只能为[A、B、C]之一！序号为：" + dto.getSerialNumber());
			}
		}
	}

	/**
	 * 保存传感器实例和参数列表
	 *
	 * @param dto 传感器实例数据
	 */
	private SensorInstanceVO insertSensorInstance(SensorInstanceBindExcelDTO dto) {
		// 新增传感器实例
		SensorType sensorType = sensorTypeMapper.selectOne(Wrappers.<SensorType>lambdaQuery()
			.eq(SensorType::getSupplier, dto.getSupplier())
			.eq(SensorType::getName, dto.getSensorTypeName())
			.eq(SensorType::getModel, dto.getModel()));
		if (Func.isEmpty(sensorType)) {
			throw new ServiceException("传感器类型不存在！传感器厂家："
				+ DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, dto.getSupplier())
				+ "，传感器名称：" + dto.getSensorTypeName() + "，传感器型号：" + dto.getModel()
				+ "，序号为：" + dto.getSerialNumber());
		}

		// 保存传感器实例
		SensorInstance instance = new SensorInstance()
			.setTypeId(sensorType.getId())
			.setCode(dto.getSensorCode())
			.setIsSpotCheck(sensorType.getIsSpotCheck())
			.setSingleSampleInterval(dto.getSingleSampleInterval())
			.setWaveSampleInterval(dto.getWaveSampleInterval());
		boolean saveInstance = this.save(instance);
		if (!saveInstance) {
			throw new ServiceException("新增传感器实例失败！sensorCode = " + dto.getSensorCode()
				+ "，序号为：" + dto.getSerialNumber());
		}

		// 构造返回值
		SensorInstanceVO result = BeanUtil.copyProperties(instance, SensorInstanceVO.class);
		result.setInstallDirection(dto.getInstallDirection());
		result.setCategory(dto.getCategory());
		result.setAxisCount(sensorType.getAxisCount());
		result.setNumber(dto.getNumber());
		result.setPhase(dto.getPhase());
		List<SensorInstanceParamVO> sensorInstanceParamList = new ArrayList<>();
		result.setSensorInstanceParamList(sensorInstanceParamList);

		// 保存传感器实例参数
		List<SensorTypeParam> sensorTypeParamList = sensorTypeParamMapper
			.selectList(Wrappers.<SensorTypeParam>lambdaQuery()
				.eq(SensorTypeParam::getTypeId, sensorType.getId()));
		for (SensorTypeParam sensorTypeParam : sensorTypeParamList) {
			// 原始传感器参数
			SensorInstanceParam sensorInstanceParam = new SensorInstanceParam().setInstanceId(instance.getId())
				.setParamName(sensorTypeParam.getParamName())
				.setSampleDataType(sensorTypeParam.getSampleDataType())
				.setVibrationType(sensorTypeParam.getVibrationType())
				.setAxialDirection(sensorTypeParam.getAxialDirection())
				.setMaxSamplingFreq(sensorTypeParam.getMaxSamplingFreq())
				.setMaxSamplingPoints(sensorTypeParam.getMaxSamplingPoints())
				.setSamplingFreq(
					sensorTypeParam.getDefaultSamplingFreq() != null ? sensorTypeParam.getDefaultSamplingFreq()
						: sensorTypeParam.getMaxSamplingFreq())
				.setSamplingPoints(sensorTypeParam.getDefaultSamplingPoints() != null
					? sensorTypeParam.getDefaultSamplingPoints()
					: sensorTypeParam.getMaxSamplingPoints())
				.setCreateTime(DateUtil.now());
			int insertParam = sensorInstanceParamMapper.insert(sensorInstanceParam);
			if (insertParam != 1) {
				throw new ServiceException("新增传感器实例参数失败！sensorCode = " + dto.getSensorCode()
					+ ", sensorTypeParam.getId() = " + sensorTypeParam.getId() + "，序号为：" + dto.getSerialNumber());
			}
			sensorInstanceParamList.add(BeanUtil.copy(sensorInstanceParam, SensorInstanceParamVO.class));

			// 特征值传感器参数
			if (Func.isNotEmpty(sensorTypeParam.getDefaultFeatures())) {
				String[] features = sensorTypeParam.getDefaultFeatures().split(StringPool.COMMA);
				for (String feature : features) {
					SensorInstanceParam featureParam = BeanUtil.copyProperties(sensorInstanceParam,
						SensorInstanceParam.class);
					featureParam.setId(null)
						.setParamName(featureParam.getParamName() + "-"
							+ NonVibrationDataEnum.getByCode(feature).getName())
						.setVibrationType(0).setFeature(feature).setCreateTime(DateUtil.now());
					int insertFeatureParam = sensorInstanceParamMapper.insert(featureParam);
					if (insertFeatureParam != 1) {
						throw new ServiceException("新增传感器实例参数(feature)失败！sensorCode = " + dto.getSensorCode()
							+ ", sensorTypeParam.getId() = " + sensorTypeParam.getId() + ", feature = " + feature
							+ "，序号为：" + dto.getSerialNumber());
					}
					sensorInstanceParamList.add(BeanUtil.copy(featureParam, SensorInstanceParamVO.class));
				}
			}
		}

		return result;
	}
}
