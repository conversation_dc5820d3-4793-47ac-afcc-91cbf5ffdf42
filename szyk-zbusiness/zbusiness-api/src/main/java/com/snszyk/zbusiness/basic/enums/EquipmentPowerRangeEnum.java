/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单位枚举类
 *
 * <AUTHOR>
 * @date 2024/01/29 16:39
 **/
@Getter
@AllArgsConstructor
public enum EquipmentPowerRangeEnum {

	/**
	 * 速度
	 */
	RANGE_ONE(1, "0~15kw"),

	/**
	 * 加速度
	 */
	RANGE_TWO(2, "15~75kw"),

	/**
	 * 温度
	 */
	RANGE_THREE(3, ">75kw"),

	/**
	 * 未知
	 */
	NOT_SUPPORTED(0, "未知"),

	;

	final Integer code;
	final String name;

	public static EquipmentPowerRangeEnum getByCode(Integer code) {
		for (EquipmentPowerRangeEnum value : EquipmentPowerRangeEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return null;
	}

}
