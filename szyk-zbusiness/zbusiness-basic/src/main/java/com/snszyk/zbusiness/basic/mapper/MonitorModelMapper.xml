<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.MonitorModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="monitorModelResultMap" type="com.snszyk.zbusiness.basic.entity.MonitorModel">
        <id column="id" property="id"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="model_id" property="modelId"/>
        <result column="status" property="status"/>
    </resultMap>

    <resultMap id="monitorModelDTOResultMap" type="com.snszyk.zbusiness.basic.dto.MonitorModelDTO">
        <id column="id" property="id"/>
        <result column="monitor_name" property="monitorName"/>
        <result column="path_name" property="pathName"/>
        <result column="model_id" property="modelId"/>
        <result column="apply_equipment" property="applyEquipment"/>
        <result column="apply_power" property="applyPower"/>
        <result column="type" property="type"/>
        <result column="apply_data" property="applyData"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>


    <!-- 根据id查询 -->
    <select id="page" resultMap="monitorModelDTOResultMap">
        SELECT
            monitor.id,
            monitor.`name` AS monitor_name,
            monitor.path_name,
            any_value ( model.model_id ) AS model_id,
            any_value ( model.status) AS status,
            any_value ( mechanism.apply_equipment) AS apply_equipment,
            any_value ( mechanism.apply_power) AS apply_power,
            any_value ( mechanism.type) AS type,
            any_value ( mechanism.apply_data) AS apply_data,
            (
                SELECT
                    CASE
                        WHEN
                            count( 1 ) > 1 THEN
                            1 ELSE 0
                        END
                FROM
                    eolm_monitor_model
                WHERE
                    monitor_id = monitor.id
            ) AS "has_children"
        FROM
            eolm_monitor monitor
        LEFT JOIN eolm_monitor_model model ON model.monitor_id = monitor.id
        LEFT JOIN eolm_mechanism_model mechanism on mechanism.id = model.model_id
        WHERE
            monitor.is_deleted = 0
        <if test="monitorModel.parentId!=null">
            AND monitor.path like concat(concat('%', #{monitorModel.parentId}), '%')
        </if>
        <if test="monitorModel.keywords!=null and monitorModel.keywords != ''">
            AND (monitor.code like concat('%',#{monitorModel.keywords},'%') or monitor.`name` like
            concat('%',#{monitorModel.keywords},'%'))
        </if>
        GROUP BY
            monitor.id
        ORDER BY
            monitor.id
    </select>

    <select id="monitorModelPage" resultMap="monitorModelDTOResultMap">
        SELECT
            model.*,
            monitor.`name` AS monitor_name,
            monitor.path_name
        FROM eolm_monitor_model model
        LEFT JOIN eolm_monitor monitor ON monitor.id = model.monitor_id
        LEFT JOIN eolm_mechanism_model mechanism on mechanism.id = model.model_id
        WHERE
            monitor.is_deleted = 0
        <if test="monitorModel.parentId!=null">
            AND monitor.path like concat(concat('%', #{monitorModel.parentId}), '%')
        </if>
        <if test="monitorModel.applyData!=null">
            AND mechanism.apply_data = #{monitorModel.applyData}
        </if>
        <if test="monitorModel.keywords!=null and monitorModel.keywords != ''">
            AND (monitor.code like concat('%',#{monitorModel.keywords},'%') or monitor.`name` like
            concat('%',#{monitorModel.keywords},'%'))
        </if>
        ORDER BY
            monitor.id
    </select>

    <select id="getChildrenList" resultMap="monitorModelDTOResultMap">
        SELECT
            model.model_id as id,
            model.status,
            mechanism.apply_equipment,
            mechanism.apply_power,
            mechanism.type,
            mechanism.apply_data
        FROM
            eolm_monitor_model model
        LEFT JOIN eolm_mechanism_model mechanism on mechanism.id = model.model_id
        WHERE model.monitor_id = #{monitorId}
        AND model.model_id != #{modelId}
        ORDER BY
            mechanism.id
    </select>

</mapper>
