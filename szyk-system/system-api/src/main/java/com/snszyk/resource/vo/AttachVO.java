/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.resource.vo;

import com.snszyk.resource.entity.Attach;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 附件表视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AttachVO对象", description = "附件表")
public class AttachVO extends Attach {
	private static final long serialVersionUID = 1L;

	/**
	 * 存储文件类型
	 */
	@ApiModelProperty(value = "存储文件类型")
	private String fileType;

	/**
	 * 图片字节数组
	 */
	@ApiModelProperty(value = "图片字节数组")
	private byte[] imageBytes;

	public AttachVO(){
		super();
	}

	public AttachVO(String fileName, String fileType, byte[] imageBytes){
		super();
		this.setOriginalName(fileName);
		this.setFileType(fileType);
		this.setImageBytes(imageBytes);
	}

}
