/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.basic.entity.DisplayPosition;
import com.snszyk.zbusiness.basic.vo.DisplayPositionVO;

import java.util.Objects;

/**
 * 虚拟展示位置表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-04-13
 */
public class DisplayPositionWrapper extends BaseEntityWrapper<DisplayPosition, DisplayPositionVO> {

	public static DisplayPositionWrapper build() {
		return new DisplayPositionWrapper();
 	}

	@Override
	public DisplayPositionVO entityVO(DisplayPosition displayPosition) {
		DisplayPositionVO displayPositionVO = Objects.requireNonNull(BeanUtil.copy(displayPosition, DisplayPositionVO.class));

		//User createUser = UserCache.getUser(daqConfig.getCreateUser());
		//User updateUser = UserCache.getUser(daqConfig.getUpdateUser());
		//daqConfigVO.setCreateUserName(createUser.getName());
		//daqConfigVO.setUpdateUserName(updateUser.getName());

		return displayPositionVO;
	}

}
