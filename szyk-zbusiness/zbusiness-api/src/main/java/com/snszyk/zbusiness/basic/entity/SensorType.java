package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 传感器类型表
 * <AUTHOR>
 */
@Data
@TableName("basic_sensor_type")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "传感器类型表")
public class SensorType extends BaseEntity {

	/**
	 * 传感器名称
	 */
	@ApiModelProperty(value = "传感器名称")
	private String name;

	/**
	 * 生产厂家（字典：sensor_supplier）
	 */
	@ApiModelProperty(value = "生产厂家（字典：sensor_supplier）")
	private String supplier;

	/**
	 * 型号
	 */
	@ApiModelProperty(value = "型号")
	private String model;

	/**
	 * 传感器类型（字典：sensor_category）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器类型（字典：sensor_category）")
	private Integer category;

	/**
	 * 轴数（0-非振动；1-单轴；3-三轴）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "轴数（0-非振动；1-单轴；3-三轴）")
	private Integer axisCount;

	/**
	 * 有线无线（0：有线；1：无线）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "有线无线（0：有线；1：无线）")
	private Integer isWireless;

	/**
	 * 起始频率Hz（字典：initial_freq）
	 */
	@ApiModelProperty(value = "起始频率Hz（字典：initial_freq）")
	private BigDecimal initialFreq;

	/**
	 * 是否点检仪类型：0-普通传感器；1-点检仪类型传感器。
	 */
	@ApiModelProperty(value = "是否点检仪类型：0-普通传感器；1-点检仪类型传感器。")
	private Integer isSpotCheck;

	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
