package com.snszyk.message.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * 接收人详情信息
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "ReceiverInfoVo对象", description = "ReceiverInfoVo对象")
public class ReceiverInfoVo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 用户列表
	 */
	@ApiModelProperty("用户列表")
	private List<UserVo> userList;

	/**
	 * 部门列表
	 */
	@ApiModelProperty("部门列表")
	private List<DeptVo> deptList;

	/**
	 * 角色列表
	 */
	@ApiModelProperty("角色列表")
	private List<RoleVo> roleList;

	@Data
	@ApiModel
	public static class UserVo {

		/**
		 * 用户id
		 */
		@JsonSerialize(using = ToStringSerializer.class)
		@ApiModelProperty("用户id")
		private Long id;

		/**
		 * 用户名
		 */
		@ApiModelProperty("用户名")
		private String realName;

		/**
		 * 部门id
		 */
		@JsonSerialize(using = ToStringSerializer.class)
		@ApiModelProperty("部门id")
		private Long deptId;

		/**
		 * 部门名称
		 */
		@ApiModelProperty("部门名称")
		private String deptName;

		/**
		 * 祖级部门名称
		 */
		@ApiModelProperty("祖级部门名称")
		private String ancestorName;
	}

	@Data
	@ApiModel
	public static class DeptVo {

		/**
		 * 部门id
		 */
		@JsonSerialize(using = ToStringSerializer.class)
		@ApiModelProperty("部门id")
		private Long deptId;

		/**
		 * 部门名称
		 */
		@ApiModelProperty("部门名称")
		private String deptName;
	}

	@Data
	@ApiModel
	public static class RoleVo {

		/**
		 * 角色id
		 */
		@JsonSerialize(using = ToStringSerializer.class)
		@ApiModelProperty("角色id")
		private Long roleId;

		/**
		 * 角色名称
		 */
		@ApiModelProperty("角色名称")
		private String roleName;
	}
}
