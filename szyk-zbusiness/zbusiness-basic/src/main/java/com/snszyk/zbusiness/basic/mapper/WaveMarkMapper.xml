<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.WaveMarkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="waveMarkResultMap" type="com.snszyk.zbusiness.basic.entity.WaveMark">
        <id column="id" property="id"/>
        <result column="monitor_id" property="monitorId"/>
        <result column="wave_id" property="waveId"/>
        <result column="speed" property="speed"/>
        <result column="teeth_number" property="teethNumber"/>
        <result column="meshing_freq" property="meshingFreq"/>
        <result column="bearing_id" property="bearingId"/>
        <result column="characteristic_freq" property="characteristicFreq"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="getCharacteristicFreqList" resultType="com.snszyk.zbusiness.basic.dto.BearingCharacteristicFreqDTO">
        SELECT eb.bpfi, eb.bpfo, eb.ftf, eb.bsf
        FROM eolm_wave_mark ewm
        LEFT JOIN eolm_bearing eb ON ewm.bearing_id = eb.id
        WHERE ewm.wave_id = #{waveId}
    </select>

    <delete id="removeByParams">
        delete from eolm_wave_mark
        <where>
            <if test="waveMark.monitorId!=null">
                and monitor_id = #{waveMark.monitorId}
            </if>
            <if test="waveMark.waveId!=null">
                and wave_id = #{waveMark.waveId}
            </if>
        </where>
    </delete>

</mapper>
