/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.CollectionStationDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationOnlineStatDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.vo.CollectionStationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采集站表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CollectionStationMapper extends BaseMapper<CollectionStation> {


	/**
	 * 分页查询采集站
	 * @param page 分页参数
	 * @param vo vo
	 * @return
	 */
	List<CollectionStationDTO> selectStationPage(IPage<CollectionStationDTO> page,
												 @Param("vo") CollectionStationVO vo);

	/**
	 * 统计某个租户下的采集站在线、离线个数
	 * @param tenantId 租户
	 * @return
	 */
	@SqlParser(filter = true)
	CollectionStationOnlineStatDTO onlineStat(@Param("tenantId") String tenantId);

}
