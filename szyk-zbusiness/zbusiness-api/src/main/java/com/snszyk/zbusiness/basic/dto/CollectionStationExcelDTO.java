/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 采集站表导出excel
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@Data
@Accessors(chain = true)
@ColumnWidth(25)
public class CollectionStationExcelDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 采集站名称
	 */
	@ExcelProperty(value = {"采集站名称"}, index = 0)
	@ApiModelProperty(value = "采集站名称")
	private String name;
	/**
	 * IP地址
	 */
	@ExcelProperty(value = {"IP地址"}, index = 1)
	@ApiModelProperty(value = "IP地址")
	private String ipAddress;
	/**
	 * 通道数
	 */
	@ExcelProperty(value = {"通道数"}, index = 2)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "通道数")
	private Integer channelCount;
	/**
	 * 类型
	 */
	@ExcelProperty(value = {"类型"}, index = 3)
	@ApiModelProperty(value = "类型")
	private String isWirelessName;
	/**
	 * 状态
	 */
	@ExcelProperty(value = {"状态"}, index = 4)
	@ApiModelProperty(value = "状态")
	private String onlineName;
	/**
	 * 最近更新
	 */
	@ExcelProperty(value = {"最近更新"}, index = 5)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("最近更新")
	private Date updateTime;

}
