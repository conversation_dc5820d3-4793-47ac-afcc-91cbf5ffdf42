package com.snszyk.zbusiness.basic.dto;

import com.snszyk.resource.entity.Attach;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 设备坐标信息
 * @ClassName: DeviceCoordinateDTO
 * @author: wangmh
 * @create: 2022-10-17 16:48
 **/
@Data
@ApiModel(value = "设备视图对象",description = "设备视图对象")
@AllArgsConstructor
@NoArgsConstructor
public class DeviceCoordinateDTO {
	@ApiModelProperty(value = "主键")
	private Long id;
	@ApiModelProperty(value = "编码")
	private String code;
	@ApiModelProperty(value = "名称")
	private String name;
	@ApiModelProperty(value = "附件")
	private Attach image;
	@ApiModelProperty(value = "子级")
	private List<DeviceCoordinateSubDTO> list;
}
