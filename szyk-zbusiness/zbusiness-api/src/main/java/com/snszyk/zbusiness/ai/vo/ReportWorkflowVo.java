//package com.snszyk.zbusiness.ai.vo;
//
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//@Data
//public class ReportWorkflowVo {
//
//	/**
//	 * 文件id
//	 */
//	@ApiModelProperty(value = "文件id", required = true)
//	private String fileId;
//
//	/**
//	 * 类型
//	 */
//	@ApiModelProperty(value = "类型", required = true)
//	private String type;
//
//	/**
//	 * 用户标识
//	 */
//	@ApiModelProperty(value = "用户标识", required = true)
//	private String user;
//
//}
