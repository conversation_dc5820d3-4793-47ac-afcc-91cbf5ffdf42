package com.snszyk.system.service.impl;

import lombok.AllArgsConstructor;
import com.snszyk.core.log.model.LogApi;
import com.snszyk.core.log.model.LogError;
import com.snszyk.core.log.model.LogUsual;
import com.snszyk.system.service.ILogApiService;
import com.snszyk.system.service.ILogErrorService;
import com.snszyk.system.service.ILogService;
import com.snszyk.system.service.ILogUsualService;
import org.springframework.stereotype.Service;

/**
 * Created by Szyk.
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class LogServiceImpl implements ILogService {

	private final ILogUsualService usualService;
	private final ILogApiService apiService;
	private final ILogErrorService errorService;

	@Override
	public Boolean saveUsualLog(LogUsual log) {
		return usualService.save(log);
	}

	@Override
	public Boolean saveApiLog(LogApi log) {
		return apiService.save(log);
	}

	@Override
	public Boolean saveErrorLog(LogError log) {
		return errorService.save(log);
	}

}
