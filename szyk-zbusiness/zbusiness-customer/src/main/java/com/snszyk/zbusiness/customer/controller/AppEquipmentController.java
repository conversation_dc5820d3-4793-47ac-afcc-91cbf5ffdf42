/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.customer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.basic.dto.EquipmentDTO;
import com.snszyk.zbusiness.basic.dto.EquipmentStatDTO;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * 设备信息表 控制器
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("app/equipment")
@Api(value = "设备管理", tags = "设备管理接口")
public class AppEquipmentController extends SzykController {

    private final IEquipmentService equipmentService;

    /**
     * 获取设备状态统计
     */

    @GetMapping
    @ApiImplicitParams({
            @ApiImplicitParam(name = "keywords", value = "设备名称或编号", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "deviceId", value = "地点id", paramType = "query", dataType = "string"),
            @ApiImplicitParam(name = "isAbnormal", value = "是否异常，1-是，0-否", paramType = "query", dataType = "integer")
    })
    @ApiOperation(value = "设备分页", notes = "设备分页")
    public R<IPage<EquipmentDTO>> equipmentLedger(@ApiIgnore EquipmentVO vo, Query query) {
        IPage<EquipmentDTO> page = Condition.getPage(query);
        List<EquipmentDTO> list = this.equipmentService.equipmentLedger(page, vo);
        page.setRecords(list);
        return R.data(page);
    }
    /**
     * 设备统计
     */
    @GetMapping("/statistics")
    @ApiOperation(value = "设备统计", notes = "设备统计")
    public R<EquipmentStatDTO> equipmentStatistics() {
        return R.data(equipmentService.equipmentStatistics());
    }

}
