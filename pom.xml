<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.snszyk</groupId>
    <artifactId>szyk-single</artifactId>
    <packaging>pom</packaging>
    <version>2.0.0.RELEASE</version>
    <modules>
        <module>szyk-common</module>
        <module>szyk-admin</module>
        <module>szyk-system</module>
        <module>szyk-zbusiness</module>
        <module>szyk-message</module>
        <module>xxl-job-admin</module>
    </modules>

    <properties>
        <szyk.project.id>szyk-api</szyk.project.id>
        <szyk.project.version>2.0.0.RELEASE</szyk.project.version>

        <java.version>1.8</java.version>
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <flowable.version>6.4.2</flowable.version>
        <spring.boot.version>2.3.12.RELEASE</spring.boot.version>
        <spring.platform.version>Cairo-SR8</spring.platform.version>
        <flowable.version>6.4.2</flowable.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.snszyk.platform</groupId>
                <artifactId>szyk-bom</artifactId>
                <version>${szyk.project.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.spring.platform</groupId>
                <artifactId>platform-bom</artifactId>
                <version>${spring.platform.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <!-- 阿里云效制品库 -->
    <distributionManagement>
        <repository>
            <id>rdc-releases</id>
            <url>https://packages.aliyun.com/maven/repository/2127288-release-fXWqLV/</url>
        </repository>
        <snapshotRepository>
            <id>rdc-snapshots</id>
            <url>https://packages.aliyun.com/maven/repository/2127288-snapshot-VbfF5u/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
