/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.cache;

import com.snszyk.system.entity.Region;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.system.service.IRegionService;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * 行政区划缓存工具类
 *
 * <AUTHOR>
 */
public class RegionCache {
	public static final int PROVINCE_LEVEL = 1;
	public static final int CITY_LEVEL = 2;
	public static final int DISTRICT_LEVEL = 3;
	public static final int TOWN_LEVEL = 4;
	public static final int VILLAGE_LEVEL = 5;

	private static final String REGION_CODE = "region:code:";

	private static final IRegionService regionService;

	static {
		regionService = SpringUtil.getBean(IRegionService.class);
	}

	/**
	 * 获取行政区划实体
	 *
	 * @param code 区划编号
	 * @return Param
	 */
	public static Region getByCode(String code) {
		return CacheUtil.get(SYS_CACHE, REGION_CODE, code, () -> regionService.getById(code));
	}

}
