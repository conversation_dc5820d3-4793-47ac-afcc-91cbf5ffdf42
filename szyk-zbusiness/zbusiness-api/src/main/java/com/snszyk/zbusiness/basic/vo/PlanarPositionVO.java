/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 2D图展示框位置 视图实体类
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Data
@ApiModel(value = "PlanarPositionVO对象", description = "PlanarPositionVO对象")
public class PlanarPositionVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;
	/**
	 * 展示框位置信息列表
	 */
	@ApiModelProperty(value = "展示框位置信息列表")
	private List<DisplayPositionVO> displayPositionList;

}
