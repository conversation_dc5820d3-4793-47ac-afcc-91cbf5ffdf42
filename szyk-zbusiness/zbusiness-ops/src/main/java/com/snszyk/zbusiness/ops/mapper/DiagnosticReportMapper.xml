<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.ops.mapper.DiagnosticReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="diagnosticReportResultMap" type="com.snszyk.zbusiness.ops.entity.DiagnosticReport">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="report_no" property="reportNo"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="diagnosis_id" property="diagnosisId"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="trend_chart" property="trendChart"/>
        <result column="wave_form" property="waveForm"/>
        <result column="diagnose_time" property="diagnoseTime"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="diagnosticReportDTOResultMap" type="com.snszyk.zbusiness.ops.dto.DiagnosticReportDTO">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="report_no" property="reportNo"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="diagnosis_id" property="diagnosisId"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="diagnose_time" property="diagnoseTime"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="page" resultMap="diagnosticReportDTOResultMap">
        select * from eolm_diagnostic_report where is_deleted = 0
        <if test="diagnosticReport.equipmentId!=null">
            and equipment_id = #{diagnosticReport.equipmentId}
        </if>
        <if test="diagnosticReport.name!=null and diagnosticReport.name!=''">
            and name like concat(concat('%', #{diagnosticReport.name}),'%')
        </if>
        <if test="diagnosticReport.reportNo!=null and diagnosticReport.reportNo!=''">
            and report_no like concat(concat('%', #{diagnosticReport.reportNo}),'%')
        </if>
        order by create_time desc
    </select>

</mapper>
