/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.EquipmentAi;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备AI模型表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-01-12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EquipmentAiDTO extends EquipmentAi {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备路径
	 */
	@ApiModelProperty(value = "设备路径")
	private String pathName;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 模型名称
	 */
	@ApiModelProperty(value = "模型名称")
	private String modelName;

	/**
	 * 模型参数个数
	 */
	@ApiModelProperty(value = "模型参数个数")
	private Integer modelParams;

	/**
	 * 模型版本
	 */
	@ApiModelProperty(value = "模型版本")
	private String modelVersion;

	/**
	 * 是否有子集
	 */
	@ApiModelProperty(value = "是否有子集")
	private Integer hasChildren;

	/**
	 * 子集
	 */
	@ApiModelProperty(value = "子集")
	private List<EquipmentAiDTO> children;

}
