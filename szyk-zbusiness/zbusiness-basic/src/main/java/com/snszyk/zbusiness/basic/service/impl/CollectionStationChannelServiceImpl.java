/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStationChannel;
import com.snszyk.zbusiness.basic.mapper.CollectionStationChannelMapper;
import com.snszyk.zbusiness.basic.service.ICollectionStationChannelService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 采集站通道表 服务实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class CollectionStationChannelServiceImpl extends ServiceImpl<CollectionStationChannelMapper, CollectionStationChannel>
	implements ICollectionStationChannelService {

	@Override
	public List<CollectionStationChannelDTO> selectChannelList(Long stationId) {
		return baseMapper.selectChannelList(stationId);
	}

	@Override
	public List<CollectionStationChannelDTO> listBy(Collection<? extends Serializable> stationIdList) {
		List<CollectionStationChannel> stationChannelList = this.lambdaQuery()
			.in(CollectionStationChannel::getStationId, stationIdList)
			.list();
		return ObjectUtil.isEmpty(stationChannelList) ? Collections.emptyList() : BeanUtil.copy(stationChannelList, CollectionStationChannelDTO.class);
	}
}
