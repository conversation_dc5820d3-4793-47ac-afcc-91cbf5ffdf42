//package com.snszyk.zbusiness.ops.feign;
//
//import com.alibaba.fastjson.JSONObject;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
///**
// * 调用python服务获取频谱数据
// *
// * <AUTHOR>
// */
//@FeignClient(
//		url="${python.server}",name="algorithm",
//		value = "algorithm"
//	)
//public interface PythonServerFeign {
//
//	/**
//	 * 频谱和包络谱
//	 * @param originTime 传感器数据时间
//	 * @param waveId 波形配置id
//	 * @param monitorId 传感器数据表名
//	 * @return {"freq": list(double), "envelope": list(double)}
//	 */
//	@GetMapping("/freqAndEnvelope")
//	JSONObject freqAndEnvelope(@RequestParam("origin_time") String originTime,
//							   @RequestParam("wave_id") String waveId,
//							   @RequestParam("monitor_id") String monitorId);
//
//}
