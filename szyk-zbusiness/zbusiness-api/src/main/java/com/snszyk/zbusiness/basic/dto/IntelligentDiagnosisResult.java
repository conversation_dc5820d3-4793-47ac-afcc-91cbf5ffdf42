package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 智能诊断结果
 * <AUTHOR>
 */
@Data
@ApiModel(value = "智能诊断结果")
public class IntelligentDiagnosisResult implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 是否诊断成功
	 */
	@ApiModelProperty(value = "是否诊断成功")
	private Boolean success;

	/**
	 * 诊断失败原因 - success未false时
	 */
	@ApiModelProperty(value = "诊断失败原因，success未false时")
	private String failReason;

	/**
	 * 诊断结论
	 */
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;

	/**
	 * 诊断建议
	 */
	@ApiModelProperty(value = "诊断建议")
	private String suggestion;
}
