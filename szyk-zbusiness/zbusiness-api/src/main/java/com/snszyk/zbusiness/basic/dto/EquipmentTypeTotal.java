package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.enums.ModelTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设备故障类型统计
 */
@Data
public class EquipmentTypeTotal {

	/**
	 * 设备故障类型
	 */
	@ApiModelProperty(value = "异常类型")
	private Integer mechanismType;

	public String getMechanismTypeName() {
		return ModelTypeEnum.getByCode(mechanismType).getName();
	}
	/**
	 * 数量
	 */
	@ApiModelProperty(value = "总数")
	private Integer ct;
}
