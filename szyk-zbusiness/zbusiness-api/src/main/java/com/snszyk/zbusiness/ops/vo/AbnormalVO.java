/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.dto.WaveDTO;
import com.snszyk.zbusiness.ops.entity.Abnormal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备异常信息表视图实体类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AbnormalVO对象", description = "设备异常信息表")
public class AbnormalVO extends Abnormal {
	private static final long serialVersionUID = 1L;

	/**
	 * 波形
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "波形")
	private WaveDTO wave;

	/**
	 * 开始日期
	 */
	@ApiModelProperty(value = "开始日期")
	private String startDate;

	/**
	 * 结束日期
	 */
	@ApiModelProperty(value = "结束日期")
	private String endDate;

	/**
	 * 机理类型字符串
	 */
	@ApiModelProperty(value = "机理类型字符串")
	private String faultType;

	/**
	 * 设备名称或编号模糊查询
	 */
	@ApiModelProperty(value = "设备名称或编号模糊查询")
	private String equipmentKeyword;

	/**
	 * 设备筛选(0:重点关注设备, 1:全部设备)
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备筛选(0:重点关注设备, 1:全部设备)")
	private Integer equipmentFilter;

	/**
	 * query
	 * 路径的id
	 */
	@ApiModelProperty(value = "设备的路径")
	private Long deviceId;

}
