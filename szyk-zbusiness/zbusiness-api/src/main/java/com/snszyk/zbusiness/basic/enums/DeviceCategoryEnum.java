/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 基础树类型枚举类
 *
 * <AUTHOR>
 * @date 2023/10/18 11:56
 **/
@Getter
@AllArgsConstructor
public enum DeviceCategoryEnum {

	/**
	 * 位置
	 */
	DEVICE(0, "位置"),
	/**
	 * 设备
	 */
	EQUIPEMNT(1, "设备"),
	/**
	 * 部位
	 */
	MONITOR(2, "部位"),
	;

	final Integer code;
	final String name;

	public static DeviceCategoryEnum getByCode(Integer code){
		for (DeviceCategoryEnum value : DeviceCategoryEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
