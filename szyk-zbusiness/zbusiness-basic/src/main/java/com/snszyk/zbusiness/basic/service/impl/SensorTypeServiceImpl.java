/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.SensorTypeDTO;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import com.snszyk.zbusiness.basic.entity.SensorType;
import com.snszyk.zbusiness.basic.mapper.SensorTypeMapper;
import com.snszyk.zbusiness.basic.mapper.SensorTypeParamMapper;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.ISensorTypeService;
import com.snszyk.zbusiness.basic.vo.SensorTypeVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.common.enums.DictBizEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 传感器类型表 服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class SensorTypeServiceImpl extends BaseServiceImpl<SensorTypeMapper, SensorType> implements ISensorTypeService {

	private final SensorTypeParamMapper sensorTypeParamMapper;
	private final ISensorInstanceService sensorInstanceService;

	@Override
	public IPage<SensorTypeDTO> page(IPage<SensorTypeDTO> page, SensorTypeVO vo) {
		List<SensorTypeDTO> records = baseMapper.page(page, vo);
		if (CollectionUtil.isNotEmpty(records)) {
			records.forEach(sensorTypeDTO -> {
				// 设置是否有传感器实例
				int sensorInstanceCount = sensorInstanceService.count(Wrappers.<SensorInstance>query()
					.lambda()
					.eq(SensorInstance::getTypeId, sensorTypeDTO.getId())
					.eq(SensorInstance::getIsDeleted, 0));
				if (sensorInstanceCount > 0) {
					sensorTypeDTO.setHasSensorInstance(1);
				} else {
					sensorTypeDTO.setHasSensorInstance(0);
				}

				// 设置传感器厂家名称
				if (Func.isNotEmpty(sensorTypeDTO.getSupplier())) {
					sensorTypeDTO.setSupplierName(DictBizCache.getValue(DictBizEnum.SENSOR_SUPPLIER, sensorTypeDTO.getSupplier()));
				}

				// 设置传感器类型名称
				if (Func.isNotEmpty(sensorTypeDTO.getCategory())) {
					sensorTypeDTO.setCategoryName(DictBizCache.getValue(DictBizEnum.SENSOR_CATEGORY, sensorTypeDTO.getCategory()));
				}
			});
		}

		return page.setRecords(records);
	}
}
