package com.snszyk.zbusiness.ops.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.zbusiness.ops.dto.BearingImportDTO;
import com.snszyk.zbusiness.ops.dto.ImportListenerResp;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 轴承导入监听类
 *
 * <AUTHOR>
 * @date 2023-04-20 16:56
 */
@Slf4j
public class BearingImportListener extends AnalysisEventListener<BearingImportDTO> {

	/**
	 * 导入模板表头
	 */
	private static final String[] IMPORT_TEMPLATE_HEADER = {"*制造厂商", "*型号", "滚子/滚柱数目", "*<PERSON>FO", "*BPFI", "*FTF", "*BSF"};

	public static List<BearingImportDTO> importList = new ArrayList<>();
	public static final ThreadLocal<ImportListenerResp> RESP = new ThreadLocal<>();

	@Override
	public void invoke(BearingImportDTO data, AnalysisContext context) {
		//log.info("解析到的一条数据: excelRow = {}", data);
		//importList.add(data);
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {

	}

	@Override
	public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
		log.info("表头数据 excelHead= {}", headMap);
		if(headMap.size()!= IMPORT_TEMPLATE_HEADER.length){
			throw new ServiceException("文件数据和模板格式不匹配");
		}
		headMap.values().forEach(header -> {
			if(!Arrays.asList(IMPORT_TEMPLATE_HEADER).contains(header)){
				throw new ServiceException("文件数据和模板格式不匹配");
			}
		});
	}
}
