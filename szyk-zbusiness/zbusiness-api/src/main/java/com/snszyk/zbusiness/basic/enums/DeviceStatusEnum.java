/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备故障状态枚举类
 *
 * <AUTHOR>
 * @date 2022/12/15 15:10
 **/
@Getter
@AllArgsConstructor
public enum DeviceStatusEnum {

	/**
	 * 无故障
	 */
	NO_FAULT(0, "无故障"),
	/**
	 * 有故障
	 */
	IS_FAULT(1, "有故障"),
	;

	final Integer code;
	final String name;

	public static DeviceStatusEnum getByCode(Integer code){
		for (DeviceStatusEnum value : DeviceStatusEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
