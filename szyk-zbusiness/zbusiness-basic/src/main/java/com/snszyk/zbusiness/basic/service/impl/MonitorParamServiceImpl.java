/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.entity.MonitorParam;
import com.snszyk.zbusiness.basic.mapper.MonitorParamMapper;
import com.snszyk.zbusiness.basic.service.IMonitorParamService;
import com.snszyk.zbusiness.basic.service.IMonitorService;
import com.snszyk.zbusiness.basic.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备测点参数配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-04
 */
@Service
@AllArgsConstructor
public class MonitorParamServiceImpl extends ServiceImpl<MonitorParamMapper, MonitorParam> implements IMonitorParamService {

	private final IMonitorService monitorService;

	@Override
	public boolean submit(MonitorParamVO vo) {
		Long monitorId = vo.getMonitorId();
		Monitor monitor = monitorService.getById(monitorId);
		if(monitor == null){
			throw new ServiceException("当前部位不存在！");
		}
		MonitorParam monitorParam = this.getOne(Wrappers.<MonitorParam>query().lambda().eq(MonitorParam::getMonitorId, monitorId));
		if(Func.isEmpty(monitorParam)){
			monitorParam = new MonitorParam();
			monitorParam.setMonitorId(monitorId);
		}
		if(Func.isNotEmpty(vo.getRpmInfoList())){
			monitorParam.setRpmInfo(JSONUtil.toJsonStr(vo.getRpmInfoList()));
		}
		if(Func.isNotEmpty(vo.getBladeInfoList())){
			monitorParam.setBladeInfo(JSONUtil.toJsonStr(vo.getBladeInfoList()));
		}
		if(Func.isNotEmpty(vo.getBearingInfoList())){
			monitorParam.setBearingId(vo.getBearingInfoList().stream().map(bearingInfo ->
				Func.toStr(bearingInfo.getId())).collect(Collectors.joining(",")))
				.setBearingInfo(JSONUtil.toJsonStr(vo.getBearingInfoList()));
		}
		if(Func.isNotEmpty(vo.getGearInfoList())){
			monitorParam.setGearInfo(JSONUtil.toJsonStr(vo.getGearInfoList()));
		}
		return this.saveOrUpdate(monitorParam);
	}

	@Override
	public MonitorParamVO detail(Long monitorId) {
		Monitor monitor = monitorService.getById(monitorId);
		if(monitor == null){
			throw new ServiceException("当前部位不存在！");
		}
		MonitorParam monitorParam = this.getOne(Wrappers.<MonitorParam>query().lambda().eq(MonitorParam::getMonitorId, monitorId));
		if(monitorParam == null){
			return null;
		}
		MonitorParamVO detail = Objects.requireNonNull(BeanUtil.copy(monitorParam, MonitorParamVO.class));
		detail.setRpmInfoList(JSONUtil.toList(monitorParam.getRpmInfo(), ParamRpmVO.class));
		detail.setBladeInfoList(JSONUtil.toList(monitorParam.getBladeInfo(), ParamBladeVO.class));
		detail.setBearingInfoList(JSONUtil.toList(monitorParam.getBearingInfo(), ParamBearingVO.class));
		detail.setGearInfoList(JSONUtil.toList(monitorParam.getGearInfo(), ParamGearVO.class));
		return detail;
	}

}
