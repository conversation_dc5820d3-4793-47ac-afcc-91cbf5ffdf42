package com.snszyk.common.utils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

public class ResponseUtil {

    public static void buildDownloadResponse(String fileName, HttpServletRequest request, HttpServletResponse response) {

        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-type", "application/octet-stream;charset=UTF-8");
        response.setContentType("application/octet-stream;charset=UTF-8");
        String userAgent = request.getHeader("User-Agent").toLowerCase();
        // Java下载文件时IE浏览器出现文件名乱码
        try {
            // 针对IE或者以IE为内核的浏览器：
            if (userAgent.contains("msie") || userAgent.contains("trident")) {
                fileName = URLEncoder.encode(fileName, "UTF-8");
            } else {
                // 非IE浏览器的处理：
                fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
            }
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        } catch (UnsupportedEncodingException ignore) {
        }
    }
}
