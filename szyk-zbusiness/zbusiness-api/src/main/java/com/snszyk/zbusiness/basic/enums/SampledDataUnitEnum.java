/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 采样数据单位 枚举类
 *
 * <AUTHOR>
 * @date 2024/01/03 10:56
 **/
@Getter
@AllArgsConstructor
public enum SampledDataUnitEnum {

	/**
	 * 速度单位
	 */
	VELOCITY("VELOCITY", 1),

	/**
	 * 加速度单位
	 */
	ACCELERATION("ACCELERATION", 2),

	/**
	 * 设备温度单位
	 */
	EQUIPMENT_TEMPERATURE("DTEMP", 3),

	/**
	 * 环境温度单位
	 */
	ENVIRONMENT_TEMPERATURE("ETEMP", 3),

	/**
	 * 电压
	 */
	VOLTAGE("VOLTAGE", 4),

	/**
	 * 电量
	 */
	ELECTRICITY_QUANTITY("SPOWER", 5),

	/**
	 * 位移
	 */
	DISPLACEMENT("DISPLACEMENT", 6),

	/**
	 * 应力波
	 */
	STRESS("STRESS", 2),

	/**
	 * 电流
	 */
	ELECTRIC("ELECTRIC", 7),

	/**
	 * 转速
	 */
	RPM("RPM", 8),

	/**
	 * 未知
	 */
	NOT_SUPPORTED("NOT_SUPPORTED", 0),
	;

	final String code;
	final Integer value;

	public static SampledDataUnitEnum getByCode(String code){
		for (SampledDataUnitEnum value : SampledDataUnitEnum.values()) {
			if (value.getCode().equals(code)){
				return value;
			}
		}
		return NOT_SUPPORTED;
	}

}
