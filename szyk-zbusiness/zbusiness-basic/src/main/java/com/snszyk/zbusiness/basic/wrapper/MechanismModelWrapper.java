/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.basic.entity.MechanismModel;
import com.snszyk.zbusiness.basic.vo.MechanismModelVO;

import java.util.Objects;

/**
 * 机理模型库包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
public class MechanismModelWrapper extends BaseEntityWrapper<MechanismModel, MechanismModelVO> {

	public static MechanismModelWrapper build() {
		return new MechanismModelWrapper();
 	}

	@Override
	public MechanismModelVO entityVO(MechanismModel mechanismModel) {
		MechanismModelVO mechanismModelVO = Objects.requireNonNull(BeanUtil.copy(mechanismModel, MechanismModelVO.class));

		//User createUser = UserCache.getUser(mechanismModel.getCreateUser());
		//User updateUser = UserCache.getUser(mechanismModel.getUpdateUser());
		//mechanismModelVO.setCreateUserName(createUser.getName());
		//mechanismModelVO.setUpdateUserName(updateUser.getName());

		return mechanismModelVO;
	}

}
