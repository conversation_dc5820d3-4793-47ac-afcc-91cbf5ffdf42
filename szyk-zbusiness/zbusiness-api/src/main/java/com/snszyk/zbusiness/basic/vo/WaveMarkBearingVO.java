/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 波形标注表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@ApiModel(value = "WaveMarkBearingVO对象", description = "波形标注表")
public class WaveMarkBearingVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 轴承id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "轴承id")
	private Long id;
	/**
	 * 外圈特征频率
	 */
	@ApiModelProperty(value = "外圈特征频率")
	private BigDecimal bpfo;
	/**
	 * 内圈特征频率
	 */
	@ApiModelProperty(value = "内圈特征频率")
	private BigDecimal bpfi;
	/**
	 * 保持架的特征频率
	 */
	@ApiModelProperty(value = "保持架的特征频率")
	private BigDecimal ftf;
	/**
	 * 滚动体的特征频率
	 */
	@ApiModelProperty(value = "滚动体的特征频率")
	private BigDecimal bsf;

}
