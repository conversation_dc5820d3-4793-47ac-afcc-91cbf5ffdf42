/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.common.utils.BizCodeUtil;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseEntity;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.mp.support.SzykPage;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.*;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.config.HealthIndexConfig;
import com.snszyk.zbusiness.basic.dto.*;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.enums.EquipmentTypeEnum;
import com.snszyk.zbusiness.basic.service.*;
import com.snszyk.zbusiness.basic.vo.BasicTreeVO;
import com.snszyk.zbusiness.basic.vo.EquipmentRunningStatVO;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import com.snszyk.zbusiness.ops.dto.AlarmDTO;
import com.snszyk.zbusiness.ops.dto.AlarmDetailDTO;
import com.snszyk.zbusiness.ops.dto.DiagnosticReportDTO;
import com.snszyk.zbusiness.ops.enums.AlarmLevelEnum;
import com.snszyk.zbusiness.ops.enums.AlarmStatusEnum;
import com.snszyk.zbusiness.ops.service.IAlarmDetailService;
import com.snszyk.zbusiness.ops.service.IAlarmService;
import com.snszyk.zbusiness.ops.service.IDiagnosticReportService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.snszyk.core.cache.constant.CacheConstant.BIZ_CACHE;

/**
 * 设备信息表 逻辑服务实现类
 *
 * <AUTHOR>
 * @date 2022/10/10 09:16
 **/
@Slf4j
@AllArgsConstructor
@Service
public class EquipmentLogicService {

	private final IEquipmentService equipmentService;
	private final IDeviceService deviceService;
	private final IMonitorService monitorService;
	private final IBasicTreeService basicTreeService;
	private final ISensorInstanceService sensorInstanceService;
	private final IDisplayPositionService displayPositionService;
	private final IEquipmentAiService equipmentAiService;
	private final IMonitorModelService monitorModelService;
	private final IMonitorParamService monitorParamService;
	private final IWaveMarkService waveMarkService;
	private final IWaveService waveService;
	private final IAlarmService alarmService;
	private final IAlarmDetailService alarmDetailService;
	private final IDiagnosticReportService diagnosticReportService;
	private final RabbitTemplate rabbitTemplate;
	private final IAttachService attachService;
	private static final String HEXADECIMAL_KEY = "0x0";
	private static final String HEXADECIMAL_SIDAS = HEXADECIMAL_KEY + StringPool.ONE;
	private static final String HEXADECIMAL_LUBRICATE = HEXADECIMAL_KEY + "2";
	private static final String HEXADECIMAL_ALL = HEXADECIMAL_KEY + "3";


	/**
	 * 分页列表
	 *
	 * @param page
	 * @param vo
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.zbusiness.basic.dto.EquipmentDTO>
	 * <AUTHOR>
	 * @date 2022/10/11 14:19
	 */
	public IPage<EquipmentDTO> page(IPage<EquipmentDTO> page, EquipmentVO vo) {
		List<Long> deviceIds = new ArrayList<>();
		// 顶级树节点
		Device topDevice = deviceService.getByCode(AuthUtil.getDeptId());
		if (Func.isEmpty(topDevice)) {
			topDevice = deviceService.getOne(Wrappers.<Device>query().lambda()
				.eq(Device::getTenantId, AuthUtil.getTenantId()).eq(Device::getParentId, 0));
		}
		if (Func.isEmpty(vo.getDeviceId())) {
			List<Device> deviceChild = deviceService.getDeviceChild(topDevice.getId());
			if (Func.isNotEmpty(deviceChild)) {
				deviceIds.addAll(deviceChild.stream().map(Device::getId).collect(Collectors.toList()));
			}
		} else {
			Device device = deviceService.getById(vo.getDeviceId());
			if (device == null) {
				throw new ServiceException("所选地点已删除，请刷新后重试!");
			}
			deviceIds.add(vo.getDeviceId());
			// 校验是否水平越权
			BasicTree node = basicTreeService.getById(vo.getDeviceId());
			if (!node.getPath().contains(Func.toStr(topDevice.getId()))) {
				throw new ServiceException(ResultCode.UN_AUTHORIZED);
			}
			List<Device> deviceChild = deviceService.getDeviceChild(device.getId());
			if (Func.isNotEmpty(deviceChild)) {
				deviceIds.addAll(deviceChild.stream().map(Device::getId).collect(Collectors.toList()));
			}
		}
		vo.setDeviceIds(deviceIds);
		if (Func.isNotEmpty(vo.getDeviceIds())) {
			IPage<EquipmentDTO> iPage = equipmentService.page(page, vo);
			if (iPage != null && Func.isNotEmpty(iPage.getRecords())) {
				iPage.getRecords().forEach(equipmentDTO -> {
					BasicTree node = basicTreeService.getById(equipmentDTO.getId());
					equipmentDTO.setPathName(node.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
					equipmentDTO.setGradeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_GRADE, equipmentDTO.getGrade()))
						.setCategoryName(DictBizCache.getValue(DictBizEnum.EQ_CATEGORY, equipmentDTO.getCategory()));
					// 设备类型
					switch (equipmentDTO.getType()) {
						case 1:
							equipmentDTO.setTypes(Func.toStr(EquipmentTypeEnum.SIDAS.getCode())).setTypesName(EquipmentTypeEnum.SIDAS.getName());
							break;
						case 2:
							equipmentDTO.setTypes(Func.toStr(EquipmentTypeEnum.LUBRICATE.getCode())).setTypesName(EquipmentTypeEnum.LUBRICATE.getName());
							break;
						case 3:
							equipmentDTO.setTypes(EquipmentTypeEnum.SIDAS.getCode() + StringPool.COMMA + EquipmentTypeEnum.LUBRICATE.getCode())
								.setTypesName(EquipmentTypeEnum.SIDAS.getName() + StringPool.COMMA + EquipmentTypeEnum.LUBRICATE.getName());
							break;
						default:
							throw new ServiceException("非法的type值" + equipmentDTO.getType() + "！仅支持1、2、3。");
					}
				});
			}
			return iPage;
		} else {
			log.warn("deviceIds为空，暂无数据！");
			return page;
		}
	}

	/**
	 * 门户-设备列表
	 *
	 * @param page
	 * @param equipment
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.snszyk.zbusiness.basic.dto.EquipmentDTO>
	 * <AUTHOR>
	 * @date 2022/10/20 15:27
	 */
	public IPage<EquipmentDTO> portalPage(IPage<EquipmentDTO> page, EquipmentVO equipment) {
		equipment.setDeviceIds(Collections.singletonList(equipment.getDeviceId()));
		IPage<EquipmentDTO> iPage = equipmentService.page(page, equipment);
		if (iPage != null && Func.isNotEmpty(iPage.getRecords())) {
			iPage.getRecords().forEach(equipmentDTO -> {
				// 测点列表
				List<Monitor> list = monitorService.list(Wrappers.<Monitor>query().lambda().eq(Monitor::getEquipmentId, equipmentDTO.getId()));
				if (Func.isNotEmpty(list)) {
					equipmentDTO.setMonitorList(BeanUtil.copy(list, MonitorDTO.class));
				}
				// 设备图片
				if (Func.isNotEmpty(equipmentDTO.getImage())) {
					List<Attach> attachListR = attachService.listByIds(Collections.singletonList(equipmentDTO.getImage()));
					equipmentDTO.setImageList(attachListR);
				}
			});
		}
		return iPage;
	}

	/**
	 * 详情
	 *
	 * @param id
	 * @return com.snszyk.zbusiness.basic.dto.EquipmentDTO
	 * <AUTHOR>
	 * @date 2022/10/10 15:19
	 */
	public EquipmentDTO detail(Long id) {
		Equipment equipment = equipmentService.getById(id);
		if (equipment == null) {
			throw new ServiceException("当前设备已删除，请刷新后重试!");
		}
		EquipmentDTO detailDTO = Objects.requireNonNull(BeanUtil.copy(equipment, EquipmentDTO.class));
		detailDTO.setGradeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_GRADE, detailDTO.getGrade()))
			.setCategoryName(DictBizCache.getValue(DictBizEnum.EQ_CATEGORY, detailDTO.getCategory()))
			.setProduceTechName(DictBizCache.getValue(DictBizEnum.PRODUCE_TECH, detailDTO.getProduceTech()));
		BasicTree node = basicTreeService.getById(id);
		detailDTO.setPathName(node.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
		detailDTO.setAlarmLevelName(DictBizCache.getValue(DictBizEnum.ALARM_LEVEL, node.getAlarmLevel()));
		// 测点列表
		List<Monitor> list = monitorService.list(Wrappers.<Monitor>query().lambda().eq(Monitor::getEquipmentId, id).orderByAsc(Monitor::getSort));
		if (Func.isNotEmpty(list)) {
			detailDTO.setMonitorList(list.stream().map(monitor -> {
				MonitorDTO monitorDTO = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorDTO.class));
				// 传感器信息
				List<SensorInstanceDTO> sensorInstanceList = sensorInstanceService.querySensorInfos(Collections.singletonList(monitor.getId()));
				monitorDTO.setSensorInfos(sensorInstanceList);
				return monitorDTO;
			}).collect(Collectors.toList()));
		}
		// 附件信息
		if (Func.isNotEmpty(equipment.getImage())) {
			List<Attach> attachListR = attachService.listByIds(Arrays.asList(equipment.getImage()));
			detailDTO.setImageList(attachListR);

		}
		// 设备类型
		Integer type = equipment.getType();
		switch (type) {
			case 1:
				detailDTO.setTypes(Func.toStr(EquipmentTypeEnum.SIDAS.getCode())).setTypesName(EquipmentTypeEnum.SIDAS.getName());
				break;
			case 2:
				detailDTO.setTypes(Func.toStr(EquipmentTypeEnum.LUBRICATE.getCode())).setTypesName(EquipmentTypeEnum.LUBRICATE.getName());
				break;
			case 3:
				detailDTO.setTypes(EquipmentTypeEnum.SIDAS.getCode() + StringPool.COMMA + EquipmentTypeEnum.LUBRICATE.getCode())
					.setTypesName(EquipmentTypeEnum.SIDAS.getName() + StringPool.COMMA + EquipmentTypeEnum.LUBRICATE.getName());
				break;
			default:
				throw new ServiceException("非法的type值" + type + "！仅支持1、2、3。");
		}
		return detailDTO;
	}

	/**
	 * 新增或修改
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2022/10/13 16:38
	 */
	public Long submit(EquipmentVO vo) {
		Device device = deviceService.getById(vo.getDeviceId());
		if (device == null) {
			throw new ServiceException("所选地点已删除，请刷新后重试!");
		}
		Equipment equipment = Objects.requireNonNull(BeanUtil.copy(vo, Equipment.class));
		// 校验编码唯一性
		//if (equipmentService.isCodeDuplicate(equipment)) {
		//	throw new ServiceException("设备编码已存在！");
		//}
		// 设备原来的点检标识
		Integer oldNeedSpotCheck = 0;
		// 设备类型
		List<Integer> typeList = Func.toIntList(vo.getTypes());
		Integer sidasType = (int) Math.pow(2, EquipmentTypeEnum.SIDAS.getCode());
		Integer lubricateType = (int) Math.pow(2, EquipmentTypeEnum.LUBRICATE.getCode());
		if (Func.isNotEmpty(vo.getId())) {
			Equipment equipmentInDb = equipmentService.getById(vo.getId());
			Integer inDbType = equipmentInDb.getType();
			// 编辑时，先验证当前设备是否已被删除
			if (equipmentInDb == null) {
				throw new ServiceException("当前设备已删除，请刷新后重试!");
			}
			// 同步更新传感器3D模型配置
			if (Func.isEmpty(vo.getSceneId()) || !Func.equals(equipmentInDb.getSceneId(), vo.getSceneId())) {
				sensorInstanceService.update(Wrappers.<SensorInstance>update().lambda()
					.set(SensorInstance::getVirtualSensorId, null)
					.eq(SensorInstance::getEquipmentId, vo.getId())
					.eq(SensorInstance::getIsDeleted, 0));
			}
			// 功率更新同步至报警门限
			if (equipmentInDb.getPower().compareTo(vo.getPower()) != 0) {
				log.info("发送设备功率更新数据MQ消息给报警门限：{}", vo);
				rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_ALARM_THRESHOLD, EolmConstant.Rabbit.ROUTING_ALARM_THRESHOLD_UPDATE, vo);
			}

			//修改设备名称 & pathName
			if (!Func.equals(equipmentInDb.getName(), vo.getName())) {
				// 更新设备的pathName
				equipment.setPathName(equipmentInDb.getPathName().substring(0, equipmentInDb.getPathName().lastIndexOf(StringPool.COMMA))
					+ StringPool.COMMA
					+ equipment.getName());

				// 发送名称更新MQ
				BasicTreeNameUpdateDTO dto = new BasicTreeNameUpdateDTO().setId(equipment.getId())
					.setNewName(equipment.getName())
					.setNewPathName(equipment.getPathName())
					.setType(BasicTreeNameUpdateDTO.TYPE_EQUIPMENT);
				rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_BASIC_TREE_NAME_UPDATE, null, dto);
				log.info("发送设备名称更新MQ成功：dto = {}", dto);
			}
			oldNeedSpotCheck = equipmentInDb.getNeedSpotCheck();
		} else {
			// 设备编码
			equipment.setCode(BizCodeUtil.generate("EQ")).setSort(equipmentService.getMaxSort() + 1);
		}
		if (typeList.size() > 1) {
			Integer type = 0;
			for (Integer t : typeList) {
				type += (int) Math.pow(2, t);
			}
			equipment.setType(type);
		} else {
			equipment.setType((int) Math.pow(2, typeList.get(0)));
		}
		boolean saveOrUpdate = equipmentService.saveOrUpdate(equipment);
		log.info("设备信息保存成功：{}", saveOrUpdate);
		// 更新基础树结构
		BasicTreeVO basicTreeVO = new BasicTreeVO().toBasicTreeVO(equipment);
		basicTreeVO.setParentId(vo.getDeviceId()).setNodeLevel(vo.getLevel()).setNodeType(equipment.getType());
		String path = basicTreeService.submit(basicTreeVO);
		if (Func.isEmpty(vo.getId())) {
			String pathName = basicTreeService.getById(equipment.getId()).getPathName();
			equipmentService.update(Wrappers.<Equipment>update().lambda()
				.eq(Equipment::getId, equipment.getId()).set(Equipment::getPath, path).set(Equipment::getPathName, pathName));
		}
		// 树结构节点区分类型
		List<Equipment> list = equipmentService.list(Wrappers.<Equipment>update().lambda()
			.eq(Equipment::getDeviceId, equipment.getDeviceId()));
		if (Func.isNotEmpty(list)) {
			List<Integer> types = list.stream().map(e -> e.getType()).collect(Collectors.toList());
			// sidas、sidas+lubricate
			if (types.contains(1) || types.contains(3)) {
				BasicTree basicTree = basicTreeService.getById(equipment.getId());
				List<Long> parentIds = Func.toLongList(basicTree.getPath());
				for (Integer i = 1; i < parentIds.size(); i++) {
					if (types.contains(2)) {
						basicTreeService.update(Wrappers.<BasicTree>update().lambda()
							.set(BasicTree::getNodeType, 3).like(BasicTree::getPath, parentIds.get(i)));
						deviceService.update(Wrappers.<Device>update().lambda()
							.set(Device::getType, 3).eq(Device::getId, parentIds.get(i)));
					} else {
						basicTreeService.update(Wrappers.<BasicTree>update().lambda()
							.set(BasicTree::getNodeType, equipment.getType()).like(BasicTree::getPath, parentIds.get(i)));
						deviceService.update(Wrappers.<Device>update().lambda()
							.set(Device::getType, equipment.getType()).eq(Device::getId, parentIds.get(i)));
					}
				}
			}
		}
		return equipment.getId();
	}



	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return boolean
	 * <AUTHOR>
	 * @date 2022/10/13 13:59
	 */
	public boolean removeByIds(List<Long> ids) {
		List<Equipment> list = equipmentService.list(Wrappers.<Equipment>query().lambda().in(Equipment::getId, ids));
		if (Func.isEmpty(list)) {
			throw new ServiceException("当前设备已删除，请刷新后重试!");
		}
		//判断是否存在业务数据
		ids.forEach(id -> {
			Equipment equipment = equipmentService.getOne(Wrappers.<Equipment>query().lambda()
				.eq(Equipment::getId, id).select(Equipment::getId, Equipment::getName, Equipment::getType));
			// 诊断业务
			AlarmDTO alarm = alarmService.getBy(id, AlarmStatusEnum.WAIT_HANDLE.getCode());
			if (Func.isNotEmpty(alarm)) {
				throw new ServiceException(StringUtil.format("设备{}，存在报警数据", equipment.getName()));
			}

			DiagnosticReportDTO diagnosticReport = diagnosticReportService.getByEquipmentId(id);
			if (Func.isNotEmpty(diagnosticReport)) {
				throw new ServiceException(StringUtil.format("设备{}，存在诊断报告", equipment.getName()));
			}

		});
		ids.forEach(id -> {
			// 同步删除AI模型配置
			equipmentAiService.removeByEquipment(id);

			displayPositionService.removeByEquipment(id);
			// 删除波形、报警门限
			List<Monitor> monitorList = monitorService.list(Wrappers.<Monitor>lambdaQuery()
				.eq(Monitor::getEquipmentId, id)
				.eq(BaseEntity::getIsDeleted, 0));
			if (Func.isNotEmpty(monitorList)) {
				List<Long> monitorIds = monitorList.stream().map(BaseEntity::getId).collect(Collectors.toList());
				List<Wave> waveList = waveService.list(Wrappers.<Wave>lambdaQuery().in(Wave::getMonitorId, monitorIds));
				if (Func.isNotEmpty(waveList)) {
					// 删除门限
					List<Long> waveIdList = waveList.stream()
						.map(Wave::getId)
						.collect(Collectors.toList());
					int removeAlarmThresholdCount = waveService.removeAlarmThresholdByWave(waveIdList);
					log.info("删除设备同步删除其所有测点下的门限：{}", removeAlarmThresholdCount);
					// 删除波形
					boolean removeWave = waveService.removeByIds(waveIdList);
					log.info("删除设备同步删除其所有测点下的波形：{}", removeWave);
				}
				// 同步删除部位参数配置
				monitorParamService.remove(Wrappers.<MonitorParam>query().lambda().in(MonitorParam::getMonitorId, monitorIds));
				// 删除特征频率识别wave_mark表
				waveMarkService.remove(Wrappers.<WaveMark>query().lambda().in(WaveMark::getMonitorId, monitorIds));
			}
			// 同步删除传感器绑定关系
			int unbindByEquipment = sensorInstanceService.unbindByEquipment(id);
			log.info("删除设备时 - 同步删除传感器绑定关系：{}", unbindByEquipment);
			// 同步删除设备下的所有测点
			monitorService.remove(Wrappers.<Monitor>query().lambda().eq(Monitor::getEquipmentId, id));
			basicTreeService.remove(Wrappers.<BasicTree>query().lambda().eq(BasicTree::getParentId, id));
		});
		CacheUtil.clear(BIZ_CACHE);
		// 删除基础树节点
		basicTreeService.removeByIds(ids);
		return equipmentService.removeByIds(ids);
	}

	/**
	 * 关闭报警
	 *
	 * @param id 设备id
	 * @return boolean
	 * <AUTHOR>
	 * @date 2023/01/30 10:39
	 */
	public boolean closeAlarm(Long id) {
		Integer alarmLevel = AlarmLevelEnum.NORMAL.getCode();
		// 更新当前设备的报警等级
		basicTreeService.update(Wrappers.<BasicTree>update().lambda()
			.eq(BasicTree::getId, id).set(BasicTree::getAlarmLevel, alarmLevel));
		// 更新当前设备所有子级的报警等级
		List<BasicTree> childList = basicTreeService.getNodeChild(id);
		List<Long> childIds = childList.stream().map(BasicTree::getId).collect(Collectors.toList());
		basicTreeService.update(Wrappers.<BasicTree>update().lambda()
			.in(BasicTree::getId, childIds).set(BasicTree::getAlarmLevel, alarmLevel));
		// 更新当前设备所有父级的报警等级（递归找父级节点的所有子级的最大报警等级作为父级节点的报警等级）
		BasicTree equipmentNode = basicTreeService.getById(id);
		List<String> parentIdList = Arrays.asList(equipmentNode.getPath().split(StringPool.COMMA));
		// 倒序遍历 parentIdList & 排除当前设备id：查询每一级父级节点的所有子级的最大报警等级，作为那一级父级节点的报警等级
		for (int i = parentIdList.size() - 2; i >= 0; i--) {
			String parentId = parentIdList.get(i);
			List<BasicTree> childrenNodeList = basicTreeService.getNodeChild(Long.valueOf(parentId));
			Integer maxAlarmLevel = childrenNodeList.stream()
				.map(BasicTree::getAlarmLevel)
				.max(Comparator.comparing(integer -> integer))
				.get();
			basicTreeService.update(Wrappers.<BasicTree>update().lambda()
				.eq(BasicTree::getId, parentId)
				.set(BasicTree::getAlarmLevel, maxAlarmLevel));
		}
		// 更新当前设备下所有波形报警等级
		List<Monitor> monitorList = monitorService.list(Wrappers.<Monitor>query().lambda()
			.eq(Monitor::getEquipmentId, id));
		if (Func.isNotEmpty(monitorList)) {
			waveService.update(Wrappers.<Wave>update().lambda()
				.set(Wave::getAlarmLevel, AlarmLevelEnum.NORMAL.getCode())
				.in(Wave::getMonitorId, monitorList.stream().map(BaseEntity::getId).collect(Collectors.toList())));
		}
		return true;
	}

	/**
	 * 替换指定位置的字符串
	 *
	 * @param sourceStr
	 * @param pos
	 * @param targetStr
	 * @return
	 */
	public String replacePosStr(String sourceStr, Integer pos, String targetStr) {
		StringBuilder sb = new StringBuilder(sourceStr);
		return sb.replace(pos, pos + 1, targetStr).toString();
	}

	/**
	 * 门户设备详情运行情况统计
	 *
	 * @param vo
	 * @return com.snszyk.zbusiness.basic.dto.EquipmentStatisticsDTO
	 * <AUTHOR>
	 * @date 2023/07/06 11:20
	 */
	public EquipmentStatisticsDTO equipmentRunningStatistics(EquipmentVO vo) {
		EquipmentStatisticsDTO dto = new EquipmentStatisticsDTO();
		Equipment equipment = equipmentService.getById(vo.getId());
		dto = initEquipmentStatisticsDTO(dto, equipment);
		return dto;
	}

	private EquipmentStatisticsDTO initEquipmentStatisticsDTO(EquipmentStatisticsDTO dto, Equipment equipment) {
		dto.setRunningRate(new BigDecimal(0L)).setDownRate(new BigDecimal(0L));
		// 本次运行时长
		BigDecimal currentRuntime = new BigDecimal(equipment.getCurrentRuntime());
		// 运行总时长
		BigDecimal runningTimeSum = new BigDecimal(equipment.getRunningTime());
		BigDecimal intervalTime = BigDecimal.ZERO;
		if (Func.isNotEmpty(equipment.getOriginRuntime())) {
			intervalTime = this.timeIntervalBetweenDate(DateUtil.now(), equipment.getOriginRuntime());
		}
		if (equipment.getIsRunning() == 1) {
			runningTimeSum = runningTimeSum.add(intervalTime);
			currentRuntime = intervalTime;
		}
		dto.setRunningTimeCurrent(currentRuntime);
		dto.setRunningTimeSum(runningTimeSum);
		// 停机总时长
		BigDecimal downTimeSum = new BigDecimal(equipment.getShutdownTime());
		if (equipment.getIsRunning() == 0) {
			downTimeSum = downTimeSum.add(intervalTime);
		}
		BigDecimal timeSum = runningTimeSum.add(downTimeSum);
		if (!Func.equals(BigDecimal.ZERO, timeSum)) {
			BigDecimal runningRate = runningTimeSum.multiply(new BigDecimal(100L)).divide(timeSum, 2, RoundingMode.HALF_UP);
			dto.setRunningRate(runningRate);
		}
		return dto;
	}


	/**
	 * 获取两个时间的间隔
	 *
	 * @param date1
	 * @param date2
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2023/07/11 17:01
	 */
	public BigDecimal timeIntervalBetweenDate(Date date1, Date date2) {
		long time1 = date1.getTime();
		long time2 = date2.getTime();
		long diff = (time1 < time2) ? (time2 - time1) : (time1 - time2);
		//long day = diff / (24 * 60 * 60 * 1000);
		//long hour = diff / (60 * 60 * 1000) - day * 24;
		//long min = diff / (60 * 1000) - day * 24 * 60 - hour * 60;
		//return day + "天" + hour + "时" + min + "分";
		return new BigDecimal(diff);
	}

	/**
	 * 根据毫秒时间戳获取时间间隔
	 *
	 * @param timeStamp
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2023/07/11 17:01
	 */
	public String timeIntervalByTimeStamp(Long timeStamp) {
		long day = timeStamp / (24 * 60 * 60 * 1000);
		long hour = timeStamp / (60 * 60 * 1000) - day * 24;
		long min = timeStamp / (60 * 1000) - day * 24 * 60 - hour * 60;
		//long sec = diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60;
		return day + "天" + hour + "时" + min + "分";
	}

	/**
	 * 门户设备详情统计
	 *
	 * @param id
	 * @return com.snszyk.zbusiness.basic.dto.EquipmentStatisticsDTO
	 * <AUTHOR>
	 * @date 2023/07/06 15:20
	 */
	public EquipmentStatisticsDTO equipmentStatistics(Long id) {
		EquipmentStatisticsDTO dto = new EquipmentStatisticsDTO();
		Equipment equipment = equipmentService.getOne(Wrappers.<Equipment>query().lambda()
			.eq(Equipment::getId, id)
			.eq(BaseEntity::getIsDeleted, 0)
			.select(BaseEntity::getId, Equipment::getIsRunning, Equipment::getLifeExpectancy, Equipment::getStartDateOfUse));
		// 运行状态
		dto.setRunningState(equipment.getIsRunning());
		// 健康状态
		dto.setHealthStatus(0);
		BasicTree node = basicTreeService.getById(id);
		if (node.getAlarmLevel() != 0) {
			dto.setHealthStatus(1);
		}
		// 健康指数
		dto.setHealthIndex(HealthIndexConfig.getHealthIndex(equipment.getId(), equipment.getLifeExpectancy(), equipment.getStartDateOfUse(), node.getAlarmLevel()));
		// 振动模型数量（测点的模型数量eolm_monitor_model表 + ai模型数量）
		dto.setModelCount(0);
		List<Monitor> monitorList = monitorService.list(Wrappers.<Monitor>query().lambda()
			.eq(Monitor::getEquipmentId, id)
			.select(Monitor::getId));
		if (Func.isNotEmpty(monitorList)) {
			List<MonitorModel> monitorModelList = monitorModelService.list(Wrappers.<MonitorModel>query().lambda()
				.in(MonitorModel::getMonitorId, monitorList.stream()
					.map(Monitor::getId)
					.collect(Collectors.toList())));
			if (Func.isNotEmpty(monitorModelList)) {
				dto.setModelCount((int) monitorModelList.stream()
					.map(MonitorModel::getModelId)
					.distinct()
					.count());
			}
		}
		List<EquipmentAi> equipmentAiList = equipmentAiService.list(Wrappers.<EquipmentAi>query().lambda().eq(EquipmentAi::getEquipmentId, id));
		if (Func.isNotEmpty(equipmentAiList)) {
			List<Long> aiModelIds = equipmentAiList.stream()
				.map(EquipmentAi::getAiModelId)
				.distinct()
				.collect(Collectors.toList());
			dto.setModelCount(dto.getModelCount() + aiModelIds.size());
		}

		// 报警情况
		dto.setAlarmCount(0);

		AlarmDTO alarm = alarmService.getBy(id, AlarmStatusEnum.WAIT_HANDLE.getCode());
		if (Func.isNotEmpty(alarm) && Func.isNotEmpty(alarm.getId())) {

			List<AlarmDetailDTO> alarmDetailList = alarmDetailService.getAlarmDetailList(alarm.getId());
			// 实时报警
			dto.setAlarmCount(alarmDetailList.size());
		}
		// 报警明细总个数

		Integer alarmDetailCount = alarmDetailService.countByEquipmentId(id);
		dto.setAlarmSum(alarmDetailCount);
		equipment = this.equipmentService.getById(equipment.getId());
		dto = initEquipmentStatisticsDTO(dto, equipment);
		return dto;
	}


	/**
	 * 获取基础数据（设备、测点、传感器、波形配置）
	 *
	 * @param needSpotCheck 是否需要点检的设备
	 * @return
	 */
	public BasicDataDTO getBasicData(Integer needSpotCheck) {
		BasicDataDTO result = new BasicDataDTO();

		//获取设备列表
		List<Equipment> equipmentList = equipmentService.list(new QueryWrapper<Equipment>().lambda()
			.eq(Equipment::getNeedSpotCheck, needSpotCheck)
			.eq(BaseEntity::getIsDeleted, 0));
		result.setEquipmentList(equipmentList);

		if (CollectionUtil.isNotEmpty(equipmentList)) {
			//获取测点列表
			List<Monitor> monitorList = monitorService.list(new QueryWrapper<Monitor>().lambda()
				.eq(BaseEntity::getIsDeleted, 0)
				.in(Monitor::getEquipmentId, equipmentList.stream()
					.map(BaseEntity::getId)
					.collect(Collectors.toList())));
			result.setMonitorList(monitorList);

			if (CollectionUtil.isNotEmpty(monitorList)) {
				//获取测点绑定的传感器列表
				List<SensorInstanceDTO> sensorList = sensorInstanceService.querySensorInfos(monitorList.stream()
					.map(BaseEntity::getId)
					.distinct()
					.collect(Collectors.toList()));
				result.setSensorList(sensorList);

				if (CollectionUtil.isNotEmpty(sensorList)) {
					// 获取传感器配置的波形列表
					List<Wave> waveList = waveService.list(Wrappers.<Wave>lambdaQuery()
						.in(Wave::getSensorCode, sensorList.stream()
							.map(SensorInstance::getCode)
							.collect(Collectors.toList())));
					result.setWaveList(waveList);
					log.info("有 {} 个 needSpotCheck = {} 的波形！", waveList == null ? 0 : waveList.size(), needSpotCheck);
				} else {
					log.info("暂无 needSpotCheck = {} 的传感器！", needSpotCheck);
				}
			} else {
				log.info("暂无 needSpotCheck = {} 的测点！", needSpotCheck);
			}
		} else {
			log.info("暂无 needSpotCheck = {} 的设备！", needSpotCheck);
		}

		return result;
	}

	/**
	 * 导出设备运行情况统计
	 *
	 * @param query    查询条件
	 * @param vo       导出条件
	 * @param tenantId 租户ID
	 * @return
	 */
	public SzykPage<EquipmentRunningStatExcelDTO> equipmentRunningPage(Query query, EquipmentRunningStatVO vo, String tenantId) {
		IPage<EquipmentRunningStatExcelDTO> page = equipmentService.equipmentRunningPage(Condition.getPage(query), vo, tenantId);
		return SzykPage.of(page);
	}

	/**
	 * 导出设备运行情况统计
	 *
	 * @param vo       导出条件
	 * @param tenantId 租户ID
	 * @param response
	 */
	public void exportEquipmentRunning(EquipmentRunningStatVO vo, String tenantId, HttpServletResponse response) {
		vo.setQuery(new Query().setCurrent(1).setSize(Integer.MAX_VALUE));
		vo.setTenantId(tenantId);
		IPage<EquipmentRunningStatExcelDTO> page = equipmentService.equipmentRunningPage(Condition.getPage(vo.getQuery()), vo, vo.getTenantId());
		if (CollectionUtil.isNotEmpty(page.getRecords())) {
			List<EquipmentRunningStatExcelDTO> data = page.getRecords();
			try {
				response.addHeader("Content-Type", "application/octet-stream");
				response.setCharacterEncoding("UTF-8");
				response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("设备运行情况统计.xlsx", "UTF-8"));
				EasyExcel.write(response.getOutputStream(), EquipmentRunningStatExcelDTO.class)
					.autoCloseStream(Boolean.FALSE)
					.sheet("设备运行情况统计")
					.doWrite(data);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
			log.info("导出设备运行情况失败！");
		}
	}

	/**
	 * 获取设备统计信息
	 *
	 * @param tenantId 租户ID
	 * @return
	 */
	public EquipmentStatDTO equipmentStat(String tenantId) {
		return equipmentService.equipmentStat(tenantId);
	}
}
