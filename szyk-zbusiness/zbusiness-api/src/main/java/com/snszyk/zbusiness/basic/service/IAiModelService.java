/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.basic.dto.AiModelDTO;
import com.snszyk.zbusiness.basic.entity.AiModel;
import com.snszyk.zbusiness.basic.vo.AiModelVO;

import java.util.List;

/**
 * AI模型表 服务类
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public interface IAiModelService extends BaseService<AiModel> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param aiModel
	 * @return
	 */
	IPage<AiModelDTO> page(IPage<AiModelDTO> page, AiModelVO aiModel);

	/**
	 * 提交
	 *
	 * @param vo
	 * @return
	 */
	boolean submit(AiModelVO vo);

	/**
	 * 校验并删除模型
	 *
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveAiModel(List<Long> ids);

	/**
	 * 设备配置AI模型列表
	 *
	 * @param equipmentId
	 * @return
	 */
	List<AiModelVO> equipmentAiModelList(Long equipmentId);

	/**
	 * 应用AI模型
	 *
	 * @param id
	 * @param equipmentId
	 * @return
	 */
	boolean apply(Long id, Long equipmentId);

	/**
	 * 查询设备绑定AI信息
	 *
	 * @param sensorCode
	 * @return
	 */
	List<AiModelDTO> queryAiModelParams(String sensorCode);

}
