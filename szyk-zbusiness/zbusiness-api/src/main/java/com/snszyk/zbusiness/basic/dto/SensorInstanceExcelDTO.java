/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 传感器实例表导出excel
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@Data
@Accessors(chain = true)
@ColumnWidth(25)
public class SensorInstanceExcelDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 传感器编码
	 */
	@ExcelProperty(value = {"传感器编码"}, index = 0)
	@ApiModelProperty(value = "传感器编码")
	private String code;
	/**
	 * 安装路径
	 */
	@ExcelProperty(value = {"安装路径"}, index = 1)
	@ApiModelProperty(value = "安装路径")
	private String pathName;
	/**
	 * 类型
	 */
	@ExcelProperty(value = {"类型"}, index = 2)
	@ApiModelProperty(value = "类型")
	private String isWirelessName;
	/**
	 * 电量
	 */
	@ExcelProperty(value = {"电量"}, index = 3)
	@ApiModelProperty(value = "电量")
	private String batteryPower;
	/**
	 * 状态
	 */
	@ExcelProperty(value = {"状态"}, index = 4)
	@ApiModelProperty(value = "状态")
	private String onlineName;
	/**
	 * 最近更新
	 */
	@ExcelProperty(value = {"最近更新"}, index = 5)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("最近更新")
	private Date updateTime;

}
