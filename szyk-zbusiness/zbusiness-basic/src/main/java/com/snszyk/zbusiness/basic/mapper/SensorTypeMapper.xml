<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.zbusiness.basic.mapper.SensorTypeMapper">

    <select id="page" resultType="com.snszyk.zbusiness.basic.dto.SensorTypeDTO">
        SELECT *
        FROM basic_sensor_type
        WHERE is_deleted = 0
        <if test="vo.nameOrModel!=null and vo.nameOrModel!=''">
            AND (`name` LIKE concat(concat('%', #{vo.nameOrModel}),'%') or `model` LIKE concat(concat('%', #{vo.nameOrModel}),'%'))
        </if>
        <if test="vo.supplier!=null and vo.supplier!=''">
            AND `supplier` = #{vo.supplier}
        </if>
        <if test="vo.category!=null">
            AND category = #{vo.category}
        </if>
        <if test="vo.isWireless!=null">
            AND is_wireless = #{vo.isWireless}
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
