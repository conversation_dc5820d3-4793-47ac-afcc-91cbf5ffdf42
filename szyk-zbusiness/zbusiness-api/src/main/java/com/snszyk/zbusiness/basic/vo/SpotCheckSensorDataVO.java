package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 点检仪的传感器数据 - 按测点上传
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SpotCheckSensorDataVO对象", description = "点检仪的传感器数据")
public class SpotCheckSensorDataVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;

	/**
	 * 传感器列表 - 此测点绑定的传感器列表
	 */
	@ApiModelProperty(value = "传感器列表 - 此测点绑定的传感器列表")
	private List<SensorVO> sensorList;

	/**
	 * 传感器数据 - 实例化的传感器
	 * <AUTHOR>
	 */
	@Data
	@Accessors(chain = true)
	@ApiModel(value = "SensorDataVO对象", description = "实例化的传感器的数据")
	public static class SensorVO implements Serializable{

		private static final long serialVersionUID = 1L;

		/**
		 * 传感器编码
		 */
		@ApiModelProperty("传感器编码")
		private String sensorCode;

		/**
		 * 传感器数据列表
		 */
		@ApiModelProperty("传感器数据列表")
		private List<DataVO> dataList;
	}

	/**
	 * 传感器数据 - 一条传感器数据
	 * <AUTHOR>
	 */
	@Data
	@Accessors(chain = true)
	@ApiModel(value = "DataVO对象", description = "一条传感器数据")
	public static class DataVO implements Serializable{

		private static final long serialVersionUID = 1L;

		/**
		 * 振动类型：0 - 非振动；1 - 振动
		 */
		@ApiModelProperty(value = "振动类型：0 - 非振动；1 - 振动")
		private Integer vibrationType;

		/**
		 * 特征值（有效值）：vibrationType = 0 时有
		 */
		@ApiModelProperty("特征值（有效值）")
		private BigDecimal characteristicValue;

		/**
		 * 时域波形：vibrationType = 1 时有
		 */
		@ApiModelProperty("时域波形")
		private String timeDomainWaveform;

		/**
		 * 采样数据类型（字典：sampled_data_type）
		 */
		@ApiModelProperty(value = "采样数据类型（字典：sampled_data_type）")
		private String outputType;

		/**
		 * 采集时间
		 */
		@ApiModelProperty("采集时间")
		@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
		private Date originTime;
	}

}
