<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.MechanismModelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="mechanismModelResultMap" type="com.snszyk.zbusiness.basic.entity.MechanismModel">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="description" property="description"/>
        <result column="apply_equipment" property="applyEquipment"/>
        <result column="apply_power" property="applyPower"/>
        <result column="apply_data" property="applyData"/>
        <result column="type" property="type"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="mechanismModelDTOResultMap" type="com.snszyk.zbusiness.basic.dto.MechanismModelDTO">
        <result column="id" property="id"/>
        <result column="apply_equipment" property="applyEquipment"/>
        <result column="apply_power" property="applyPower"/>
        <result column="apply_data" property="applyData"/>
        <result column="type" property="type"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="page" resultMap="mechanismModelDTOResultMap">
        select * from eolm_mechanism_model where is_deleted = 0 and apply_data = #{mechanismModel.applyData}
        <if test="mechanismModel.status!=2">
            and status = #{mechanismModel.status}
        </if>
        <if test="mechanismModel.applyEquipment!=null">
            and apply_equipment = #{mechanismModel.applyEquipment}
        </if>
        <if test="mechanismModel.applyPower!=null">
            and apply_power = #{mechanismModel.applyPower}
        </if>
        <if test="mechanismModel.type!=null">
            and type = #{mechanismModel.type}
        </if>
        ORDER BY create_time DESC
    </select>


</mapper>
