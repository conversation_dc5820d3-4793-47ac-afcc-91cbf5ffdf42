<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.ops.mapper.AbnormalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="abnormalResultMap" type="com.snszyk.zbusiness.ops.entity.Abnormal">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="abnormal_level" property="abnormalLevel"/>
        <result column="abnormal_reason" property="abnormalReason"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="first_time" property="firstTime"/>
        <result column="last_time" property="lastTime"/>
        <result column="is_fault" property="isFault"/>
        <result column="fault_id" property="faultId"/>
        <result column="close_reason" property="closeReason"/>
        <result column="close_time" property="closeTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="abnormalDTOResultMap" type="com.snszyk.zbusiness.ops.dto.AbnormalDTO">
        <id column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="abnormal_level" property="abnormalLevel"/>
        <result column="abnormal_reason" property="abnormalReason"/>
        <result column="conclusion" property="conclusion"/>
        <result column="suggestion" property="suggestion"/>
        <result column="first_time" property="firstTime"/>
        <result column="last_time" property="lastTime"/>
        <result column="is_fault" property="isFault"/>
        <result column="fault_id" property="faultId"/>
        <result column="close_reason" property="closeReason"/>
        <result column="close_time" property="closeTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="record_count" property="recordCount"/>
    </resultMap>


    <select id="page" resultMap="abnormalDTOResultMap">
        SELECT a.*, COUNT(ar.id) AS record_count
        FROM eolm_abnormal a
        LEFT JOIN eolm_abnormal_record ar ON a.id = ar.abnormal_id
        LEFT JOIN eolm_equipment e ON a.equipment_id = e.id
        WHERE 1=1
        <if test="abnormal.status!=null">
            AND a.status= #{abnormal.status}
        </if>
        <if test="abnormal.deviceId != null">
            AND  e.path LIKE CONCAT('%',#{abnormal.deviceId},'%')
        </if>

        <if test="abnormal.equipmentId!=null">
            AND a.equipment_id = #{abnormal.equipmentId}
        </if>
        <if test="abnormal.startDate!=null">
            AND a.create_time <![CDATA[ >= ]]> #{abnormal.startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="abnormal.endDate!=null">
            AND a.create_time <![CDATA[ <= ]]> #{abnormal.endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="abnormal.tenantId!=null and abnormal.tenantId!=''">
            AND a.tenant_id = #{abnormal.tenantId}
        </if>
        <if test="abnormal.equipmentKeyword!=null and abnormal.equipmentKeyword!=''">
            AND (e.name LIKE CONCAT('%', #{abnormal.equipmentKeyword}, '%') OR e.code LIKE CONCAT('%',
            #{abnormal.equipmentKeyword}, '%'))
        </if>
        <!--        <if test="abnormal.equipmentFilter != null and abnormal.equipmentFilter == 0">-->
        <!--            AND a.status = 0-->
        <!--        </if>-->
        GROUP BY a.id
        ORDER BY
        /* 待处理 > 已处理 */
        CASE
        WHEN a.status = 0 THEN 0 /* 未处理 */
        WHEN a.status = 1 THEN 1 /* 已成故障 */
        WHEN a.status = 2 THEN 2 /* 已关闭 */
        ELSE 3
        END ASC,
        /* 异常等级高 > 异常等级低 */
        a.abnormal_level DESC,
        /* 异常数量多 > 异常数量少 */
        record_count DESC,
        /* 最后保持创建时间倒序 */
        a.create_time DESC
    </select>

<!--    <select id="abnormalTypeTotal" resultType="com.snszyk.zbusiness.ops.dto.AbnormalTypeDTO">-->
<!--        SELECT GROUP_CONCAT(abnormal_type)abnormalType,GROUP_CONCAT(ct) ct FROM (-->
<!--                 SELECT abnormal_type,COUNT(*) ct FROM `eolm_abnormal` a JOIN-->
<!--                 `eolm_abnormal_record` b ON a.id=b.abnormal_id WHERE YEAR(a.first_time)=YEAR(NOW())-->
<!--        AND a.equipment_id=#{id} GROUP BY b.abnormal_type-->
<!--    ) a-->

<!--    </select>-->

    <!-- 设备监测简化信息结果映射 -->
    <resultMap id="equipmentMonitorSimpleDTOResultMap" type="com.snszyk.zbusiness.ops.dto.EquipmentMonitorSimpleDTO">
        <result column="equipment_id" property="equipmentId"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="path_name" property="pathName"/>
        <result column="monitor_count" property="monitorCount"/>
        <result column="abnormal_level" property="abnormalLevel"/>
        <result column="conclusion" property="conclusion"/>
        <result column="last_time" property="lastTime"/>
        <result column="record_count" property="recordCount"/>
        <result column="has_unclosed_abnormal" property="hasUnclosedAbnormal"/>
    </resultMap>

    <!-- 设备监测分页查询 -->
    <select id="equipmentMonitorPage" resultMap="abnormalDTOResultMap">
        SELECT
            e.id AS equipment_id,
            e.name,
            e.code,
            CONCAT(d.name, '/', e.name) AS path_name,
            (SELECT COUNT(1) FROM eolm_monitor m WHERE m.equipment_id = e.id AND m.is_deleted = 0) AS monitor_count,
            a.abnormal_level,
            a.conclusion,
            a.id,
            a.tenant_id,
            a.status,
            a.last_time,
            (SELECT COUNT(1) FROM eolm_abnormal_record ar WHERE ar.abnormal_id = a.id AND ar.abnormal_level = a.abnormal_level) AS record_count
        FROM
            eolm_equipment e
        LEFT JOIN eolm_device d ON e.device_id = d.id
        LEFT JOIN (
            SELECT
                t.equipment_id,
                t.abnormal_level,
                t.conclusion,
                t.last_time,
                t.id,
                t.tenant_id,
                t.status
            FROM (
                SELECT
                    ab.equipment_id,
                    ab.abnormal_level,
                    ab.conclusion,
                    ab.last_time,
                    ab.id,
                    ab.tenant_id,
                    ab.status,
                    ROW_NUMBER() OVER(PARTITION BY ab.equipment_id ORDER BY ab.abnormal_level DESC, ab.last_time DESC) as rn
                FROM
                    eolm_abnormal ab
                WHERE ab.status = 0
                    <if test="query.tenantId != null and query.tenantId != ''">
                    AND ab.tenant_id = #{query.tenantId}
                    </if>
            ) t
            WHERE t.rn = 1
        ) a ON e.id = a.equipment_id
        WHERE
            e.is_deleted = 0
            <if test="query.tenantId != null and query.tenantId != ''">
            AND e.tenant_id = #{query.tenantId}
            </if>
            <if test="query.equipmentFilter != null and query.equipmentFilter == 0">
            AND EXISTS (
                SELECT 1 FROM eolm_abnormal ab
                WHERE ab.equipment_id = e.id
                AND ab.status = 0
            )
            </if>
            <if test="query.equipmentKeyword != null and query.equipmentKeyword != ''">
            AND (e.name LIKE CONCAT('%', #{query.equipmentKeyword}, '%') OR e.code LIKE CONCAT('%', #{query.equipmentKeyword}, '%'))
            </if>
        ORDER BY
            IFNULL(a.abnormal_level, 0) DESC,
            record_count DESC,
            a.last_time DESC
    </select>

    <!-- 设备监测简化分页查询 -->
    <select id="equipmentMonitorSimplePage" resultMap="equipmentMonitorSimpleDTOResultMap">
        SELECT
        e.id AS equipment_id,
        e.name,
        e.code,
        d.path_name ,
        (SELECT COUNT(1) FROM eolm_monitor m WHERE m.equipment_id = e.id AND m.is_deleted = 0) AS monitor_count,
        a.abnormal_level,
        a.conclusion,
        a.last_time,
        (SELECT COUNT(1) FROM eolm_abnormal_record ar WHERE ar.abnormal_id = a.id AND ar.abnormal_level =
        a.abnormal_level) AS record_count,
        (SELECT COUNT(1) FROM eolm_abnormal ab WHERE ab.equipment_id = e.id AND ab.status != 2) AS has_unclosed_abnormal
        FROM
        eolm_equipment e
        LEFT JOIN eolm_device d ON e.device_id = d.id
        LEFT JOIN (
        SELECT
        t.equipment_id,
        t.abnormal_level,
        t.conclusion,
        t.last_time,
        t.id
        FROM (
        SELECT
        ab.equipment_id,
        ab.abnormal_level,
        ab.conclusion,
        ab.last_time,
        ab.id,
        ROW_NUMBER() OVER(PARTITION BY ab.equipment_id ORDER BY ab.abnormal_level DESC, ab.last_time DESC) as rn
        FROM
        eolm_abnormal ab
        WHERE ab.status != 2
        <if test="query.tenantId != null and query.tenantId != ''">
            AND ab.tenant_id = #{query.tenantId}
        </if>
        ) t
        WHERE t.rn = 1
        ) a ON e.id = a.equipment_id
        WHERE
        e.is_deleted = 0
        <if test="query.tenantId != null and query.tenantId != ''">
            AND e.tenant_id = #{query.tenantId}
        </if>
        <if test="query.equipmentFilter != null and query.equipmentFilter == 0">
            AND EXISTS (
            SELECT 1 FROM eolm_abnormal ab
            WHERE ab.equipment_id = e.id
            AND ab.status != 2
            )
        </if>
        <if test="query.equipmentKeyword != null and query.equipmentKeyword != ''">
            AND (e.name LIKE CONCAT('%', #{query.equipmentKeyword}, '%') OR e.code LIKE CONCAT('%',
            #{query.equipmentKeyword}, '%'))
        </if>
        ORDER BY
        IFNULL(a.abnormal_level, 0) DESC,
        record_count DESC,
        a.last_time DESC
    </select>
</mapper>
