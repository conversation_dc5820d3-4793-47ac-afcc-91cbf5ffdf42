/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("eolm_collection_station")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CollectionStation对象", description = "CollectionStation对象")
public class CollectionStation extends TenantEntity {

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	private String code;

	/**
	 * 名称
	 */
	@NotBlank(message = "采集站名称不能为空！")
	@ApiModelProperty(value = "名称")
	private String name;

	/**
	 * 是否在线：0-离线；1-在线
	 */
	@ApiModelProperty(value = "是否在线：0-离线；1-在线")
	private Integer online;

	/**
	 * IP地址
	 */
	@NotBlank(message = "IP地址不能为空！")
	@ApiModelProperty(value = "IP地址")
	private String ipAddress;

	/**
	 * 通道数
	 */
	@NotNull(message = "通道数不能为空！")
	@Range(message = "通道数必须大于0！", min = 1)
	@ApiModelProperty(value = "通道数")
	private Integer channelCount;

	/**
	 * 厂家
	 */
	@ApiModelProperty(value = "厂家")
	private String manufacturer;

	/**
	 * 有线无线（0：有线；1：无线）
	 */
	@ApiModelProperty(value = "有线无线（0：有线；1：无线）")
	private Integer isWireless;

}
