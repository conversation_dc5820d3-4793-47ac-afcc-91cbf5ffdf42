/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.snszyk.zbusiness.basic.entity.Equipment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备信息表 视图实体类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentVO对象", description = "设备信息表")
public class EquipmentVO extends Equipment {

	/**
	 * 设备分类名称
	 */
	@ApiModelProperty(value = "设备分类名称")
	private String categoryName;

	/**
	 * 设备树id列表
	 */
	@ApiModelProperty(value = "设备树id列表")
	private List<Long> deviceIds;

	/**
	 * 设备测点列表
	 */
	@ApiModelProperty(value = "设备测点列表")
	private List<MonitorVO> monitorList;

	/**
	 * 报警等级
	 */
	@ApiModelProperty(value = "报警等级")
	private Integer alarmLevel;

	/**
	 * 层级
	 */
	@ApiModelProperty(value = "层级")
	private Integer level;

	/**
	 * 查询条件
	 */
	@ApiModelProperty(value = "查询条件")
	private String keywords;

	/**
	 * 设备类型列表（0：诊断监测，1：润滑检测）
	 */
	@ApiModelProperty(value = "设备类型列表（0：诊断监测，1：润滑检测）")
	private String types;

	@ApiModelProperty(value = "设备异常等级")
	private Integer abnormalLevel;

	/**
	 * 异常状态
	 */
	@ApiModelProperty(value = "是否异常，1-是；0-否")
	private Integer isAbnormal;

	public EquipmentVO() {
		super();
	}

	/**
	 * 构造函数
	 *
	 * @param id
	 * @param alarmLevel
	 */
	public EquipmentVO(Long id, Integer alarmLevel) {
		super();
		this.setId(id);
		this.setAlarmLevel(alarmLevel);
	}

}
