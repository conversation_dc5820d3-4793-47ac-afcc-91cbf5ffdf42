package com.snszyk.zbusiness.basic.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * 传感器类型转换器 - 1-温振一体；2-应力波；3-电流；4-转速
 * <AUTHOR>
 */
public class SensorCategoryConverter implements Converter<Integer> {

	private static final String TEMP_VIBRATE = "温振一体";
	private static final String STRESS_WAVE = "应力波";
	private static final String ELECTRIC = "电流";
	private static final String RPM = "转速";

	@Override
	public Class supportJavaTypeKey() {
		return Integer.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public Integer convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		String cellValue = cellData.getStringValue();
		if (Objects.equals(TEMP_VIBRATE, cellValue)) {
			return 1;
		} else if (Objects.equals(STRESS_WAVE, cellValue)) {
			return 2;
		} else if (Objects.equals(ELECTRIC, cellValue)) {
			return 3;
		} else if (Objects.equals(RPM, cellValue)) {
			return 4;
		}
		return null;
	}

	@Override
	public WriteCellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		if (value == 1) {
			return new WriteCellData(TEMP_VIBRATE);
		} else if (value == 2) {
			return new WriteCellData(STRESS_WAVE);
		} else if (value == 3) {
			return new WriteCellData(ELECTRIC);
		} else if (value == 4) {
			return new WriteCellData(RPM);
		}

		return null;
	}

}
