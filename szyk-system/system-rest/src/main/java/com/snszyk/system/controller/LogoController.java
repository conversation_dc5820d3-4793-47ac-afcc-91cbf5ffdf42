package com.snszyk.system.controller;

import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.secure.annotation.PreAuth;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.RoleConstant;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.resource.entity.Attach;
import com.snszyk.resource.service.IAttachService;
import com.snszyk.system.dto.LogoDTO;
import com.snszyk.system.entity.Logo;
import com.snszyk.system.service.ILogoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * logo管理 控制器
 *
 * <AUTHOR>
 * @since 2023/5/10 15:07
 **/
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/logo")
@Api(value = "logo管理", tags = "logo管理")
public class LogoController {

	private final ILogoService logoService;
	private final IAttachService attachService;


	@PostMapping("/submit")
	@ApiOperation("提交")
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	public R submit(@RequestBody Logo logo){
		 logo.setIsDeleted(SzykConstant.DB_NOT_DELETED);
		 return R.status(logoService.saveOrUpdate(logo));
	}

	@GetMapping("/info")
	@ApiOperation("详情")
	public R<LogoDTO> info(){
		List<Logo> list = logoService.list();
		if (CollectionUtil.isNotEmpty(list)){
			Logo logo = list.get(0);
			Attach attach = attachService.getById(logo.getAttachId());
			LogoDTO logoDTO = BeanUtil.copyProperties(logo, LogoDTO.class);
			logoDTO.setAttach(attach);
			return R.data(logoDTO);
		}
		return R.data(null);
	}
}
