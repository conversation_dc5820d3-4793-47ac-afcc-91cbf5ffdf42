package com.snszyk.message.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * ClassName: TimeTypeEnum
 * Package: com.snszyk.simas.enums
 * Description:
 *
 * @Auth zhangzhenpu
 * @Create 2024/11/13 17:22
 */
@AllArgsConstructor
@Getter
public enum TimeTypeEnum {
	/**
	 * 今天
	 */
	TODAY("今天"),
	/**
	 * 近7天
	 */
	SEVEN_DAYS("近7天"),
	/**
	 * 近一个月
	 */
	ONE_MONTH("近一个月"),
	/**
	 * 近三个月
	 */
	THREE_MONTHS("近三个月"),
	/**
	 * 近一年
	 */
	ONE_YEAR("近一年");
	String desc;


	public static TimeTypeEnum getByDesc(String desc) {
		for (TimeTypeEnum value : TimeTypeEnum.values()) {
			if (value.getDesc().equals(desc)) {
				return value;
			}
		}
		return null;
	}

	public static LocalDateTime getStartTime(TimeTypeEnum timeTypeEnum) {
		switch (timeTypeEnum) {
			case TODAY:
				return getStartTimeOfYesterday();
			case SEVEN_DAYS:
				return getStartTimeOfYesterday().minusDays(6);
			case ONE_MONTH:
				return getStartTimeOfMonth();
			case THREE_MONTHS:
				return getStartTimeOfThreeMonth();
			case ONE_YEAR:
				return getStartTimeOfYear();
			default:
				return null;
		}
	}


	/**
	 * 获取近一天(从昨天开始算)的开始日期时间,从0时0分0秒开始
	 * 返回LocalDateTime
	 *
	 * @return
	 */

	public static LocalDateTime getStartTimeOfYesterday() {
		return LocalDate.now().minusDays(1).atStartOfDay();
	}

	/**
	 * 获取近一个月的开始日期时间,从0时0分0秒开始
	 */
	public static LocalDateTime getStartTimeOfMonth() {
		return LocalDate.now().minusMonths(1).atStartOfDay();
	}

	/**
	 * 近三个月的开始日期时间,从0时0分0秒开始
	 */
	public static LocalDateTime getStartTimeOfThreeMonth() {

		return LocalDate.now().minusMonths(3).atStartOfDay();
	}

	/**
	 * 近一年的开始日期时间,从0时0分0秒开始
	 */
	public static LocalDateTime getStartTimeOfYear() {
		return LocalDate.now().minusYears(1).atStartOfDay();
	}


}
