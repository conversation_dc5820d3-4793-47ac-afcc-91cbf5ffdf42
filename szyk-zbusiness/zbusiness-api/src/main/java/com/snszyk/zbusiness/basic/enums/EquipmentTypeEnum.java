/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型枚举类
 *
 * <AUTHOR>
 * @date 2024/04/09 10:56
 **/
@Getter
@AllArgsConstructor
public enum EquipmentTypeEnum {

	/**
	 * 诊断监测
	 */
	SIDAS(0, "诊断监测"),
	/**
	 * 润滑监测
	 */
	LUBRICATE(1, "润滑监测"),
	/**
	 * 不支持
	 */
	UNSUPPORTED(9999, "不支持的类型"),
	;

	final Integer code;
	final String name;

	public static EquipmentTypeEnum getByCode(Integer code){
		for (EquipmentTypeEnum value : EquipmentTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return UNSUPPORTED;
	}

}
