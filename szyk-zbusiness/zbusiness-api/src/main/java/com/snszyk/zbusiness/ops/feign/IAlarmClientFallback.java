///*
// *      Copyright (c) 2018-2088
// */
//package com.snszyk.zbusiness.ops.feign;
//
//import com.snszyk.core.tool.api.R;
//import com.snszyk.zbusiness.basic.vo.StandardDictVO;
//import com.snszyk.zbusiness.ops.dto.AlarmDTO;
//import com.snszyk.zbusiness.ops.dto.AlarmDetailDTO;
//import com.snszyk.zbusiness.ops.dto.AlarmStatisticsDTO;
//import com.snszyk.zbusiness.ops.entity.Alarm;
//import com.snszyk.zbusiness.ops.vo.*;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * Feign失败配置
// *
// * <AUTHOR>
// */
//@Component
//public class IAlarmClientFallback implements IAlarmClient {
//
//	@Override
//	public R<AlarmThresholdVO> alarmThreshold(AlarmThresholdVO alarmThresholdVO) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<AlarmDTO> alarmInfoById(Long alarmId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<AlarmDTO> getAlarmById(Long alarmId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R alarmBizToFault(Long alarmId, Long faultId) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R alarmClosedByFault(AlarmCloseVO alarmCloseVO) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R abnormalClosedByFault(AbnormalCloseVO abnormalCloseVO) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R<AlarmDTO> alarmInfoByDevice(Long deviceId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<List<Integer>> sidasStateDistribution(String tenantId, String devicePath) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R<Integer> alarmByDevicePath(String devicePath,List<String> status) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R<List<Alarm>> latestPendingAlarm(String devicePath) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R<Integer> alarmHistory(Long deviceId) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R removeThresholdByWaveId(Long waveId) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R removeThresholdByEquipment(Long equipmentId) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R removeThresholdByMonitor(Long monitorId) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R<List<AlarmDetailDTO>> getAlarmDetailList(Long alarmId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<List<AlarmChartVO>> chartByAlarmId(Long alarmId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<List<AlarmStatisticsDTO>> getAlarmStatisticData(AlarmStatisticVO vo) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<Integer> alarmDetailCountByDevice(Long equipmentId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<Integer> alarmDetailCountByMonitor(Long monitorId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R<List<Map<String, Object>>> getLast2MonthsAlarmCount(String tenantId) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R applyStandardThreshold(StandardDictVO standardDict) {
//		return R.fail("更新数据失败");
//	}
//
//	@Override
//	public R<AlarmRecordVO> alarmRecordByWave(Long waveId, String alarmIndex) {
//		return R.fail("获取数据失败");
//	}
//
//	@Override
//	public R abnormalToFault(Long abnormalId, Long faultId) {
//		return R.fail("更新数据失败");
//	}
//
//}
