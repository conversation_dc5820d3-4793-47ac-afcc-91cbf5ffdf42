/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.MonitorModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部位机理模型表视图实体类
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MechanismModelApplyVO对象", description = "部位机理模型表")
public class MonitorModelVO extends MonitorModel {
	private static final long serialVersionUID = 1L;

	/**
	 * 父主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父主键")
	private Long parentId;

	/**
	 * 机理类型（1：振动机理，2：应力波机理）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "机理类型（1：振动机理，2：应力波机理）")
	private Integer applyData;

	/**
	 * 查询条件：部位名称或编号
	 */
	@ApiModelProperty(value = "查询条件：部位名称或编号")
	private String keywords;

}
