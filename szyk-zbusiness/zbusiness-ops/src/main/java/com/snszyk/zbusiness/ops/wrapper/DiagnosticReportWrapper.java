/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.ops.entity.DiagnosticReport;
import com.snszyk.zbusiness.ops.vo.DiagnosticReportVO;

import java.util.Objects;

/**
 * 诊断报告表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-08-08
 */
public class DiagnosticReportWrapper extends BaseEntityWrapper<DiagnosticReport, DiagnosticReportVO> {

	public static DiagnosticReportWrapper build() {
		return new DiagnosticReportWrapper();
 	}

	@Override
	public DiagnosticReportVO entityVO(DiagnosticReport diagnosticReport) {
		DiagnosticReportVO diagnosticReportVO = Objects.requireNonNull(BeanUtil.copy(diagnosticReport, DiagnosticReportVO.class));

		//User createUser = UserCache.getUser(diagnosticReport.getCreateUser());
		//User updateUser = UserCache.getUser(diagnosticReport.getUpdateUser());
		//diagnosticReportVO.setCreateUserName(createUser.getName());
		//diagnosticReportVO.setUpdateUserName(updateUser.getName());

		return diagnosticReportVO;
	}

}
