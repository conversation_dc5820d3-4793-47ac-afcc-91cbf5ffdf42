/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.ops.dto.AbnormalDTO;
import com.snszyk.zbusiness.ops.dto.EquipmentMonitorSimpleDTO;
import com.snszyk.zbusiness.ops.entity.Abnormal;
import com.snszyk.zbusiness.ops.service.IAbnormalService;
import com.snszyk.zbusiness.ops.service.logic.AbnormalLogicService;
import com.snszyk.zbusiness.ops.vo.AbnormalCloseVO;
import com.snszyk.zbusiness.ops.vo.AbnormalVO;
import com.snszyk.zbusiness.ops.vo.EquipmentMonitorQueryVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

/**
 * 设备异常信息表 控制器
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@RestController
@AllArgsConstructor
@RequestMapping("/abnormal")
@Api(value = "设备异常信息表", tags = "设备异常信息表接口")
public class AbnormalController extends SzykController {

	private final IAbnormalService abnormalService;
	private final AbnormalLogicService abnormalLogicService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<AbnormalDTO> detail(Long id) {
		return R.data(abnormalService.detail(id));
	}

	/**
	 * 自定义分页 设备异常信息表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "status", value = "单据状态", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "startDate", value = "开始日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "tenantId", value = "租户id", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "endDate", value = "结束日期", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "equipmentKeyword", value = "设备名称或编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入abnormal")
	public R<IPage<AbnormalDTO>> page(@ApiIgnore AbnormalVO abnormal, Query query) {
		IPage<AbnormalDTO> pages = abnormalService.page(Condition.getPage(query), abnormal);
		return R.data(pages);
	}

	/**
	 * 新增 设备异常信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入abnormal")
	public R save(@Valid @RequestBody Abnormal abnormal) {
		return R.status(abnormalService.save(abnormal));
	}

	/**
	 * 修改 设备异常信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入abnormal")
	public R update(@Valid @RequestBody Abnormal abnormal) {
		return R.status(abnormalService.updateById(abnormal));
	}

	/**
	 * 新增或修改 设备异常信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入abnormal")
	public R submit(@Valid @RequestBody Abnormal abnormal) {
		return R.status(abnormalService.saveOrUpdate(abnormal));
	}


	/**
	 * 删除 设备异常信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(abnormalService.removeByIds(Func.toLongList(ids)));
	}

	/**
	 * 手动关闭异常 设备异常信息表
	 */
	@PostMapping("/closeAbnormal")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "手动关闭异常", notes = "传入abnormalClose")
	public R close(@Valid @RequestBody AbnormalCloseVO abnormalClose) {
		return R.status(abnormalLogicService.closeAbnormal(abnormalClose));
	}

	@GetMapping("/abnormalTotal")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "近一年异常数量统计", notes = "近一年异常数量统计")
	public R<Integer> abnormalTotal(@RequestParam("id") Long id) {
		return R.data(this.abnormalService.list(Wrappers.<Abnormal>query().eq("equipment_Id", id)).size());
	}

//	/**
//	 * 设备监测分页查询
//	 */
//	@PostMapping("/equipment-abnormal-monitor")
//	@ApiOperationSupport(order = 11)
//	@ApiOperation(value = "设备监测分页查询", notes = "传入query")
//	public R<IPage<AbnormalDTO>> equipmentMonitorPage(@RequestBody EquipmentMonitorQueryVO query) {
//
//		IPage<AbnormalDTO> pages = abnormalService.equipmentMonitorPage(query);
//		return R.data(pages);
//	}

	/**
	 * 设备监测简化分页查询
	 */
	@PostMapping("/equipment-abnormal-monitor")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "设备监测分页查询", notes = "传入query")
	public R<IPage<EquipmentMonitorSimpleDTO>> equipmentMonitorSimplePage(@RequestBody EquipmentMonitorQueryVO query) {
		IPage<EquipmentMonitorSimpleDTO> pages = abnormalService.equipmentMonitorSimplePage(query);
		return R.data(pages);
	}

}
