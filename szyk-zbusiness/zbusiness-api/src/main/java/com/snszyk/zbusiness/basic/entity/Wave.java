package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 传感器波形表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("sidas_wave")
@ApiModel(value = "传感器波形表", description = "传感器波形表")
public class Wave implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 波形名称
	 */
	@ApiModelProperty(value = "波形名称")
	private String waveName;

	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("测点id")
	private Long monitorId;

	/**
	 * 传感器实例编码
	 */
	@ApiModelProperty(value = "传感器实例编码")
	private String sensorCode;

	/**
	 * 传感器实例参数id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("传感器实例参数id")
	private Long sensorInstanceParamId;

	/**
	 * 采样数据类型
	 */
	@ApiModelProperty(value = "采样数据类型")
	private String sampleDataType;

	/**
	 * 测量方向
	 */
	@ApiModelProperty(value = "测量方向")
	private Integer measureDirection;

	/**
	 * 编号，仅应力波传感器
	 */
	@ApiModelProperty(value = "编号，仅应力波传感器")
	private String number;

	/**
	 * 相位（A、B、C），仅电流传感器
	 */
	@ApiModelProperty(value = "相位（A、B、C），仅电流传感器")
	private String phase;

	/**
	 * 报警等级
	 */
	@ApiModelProperty(value = "报警等级")
	private Integer alarmLevel;

	/**
	 * 停机线
	 */
	@ApiModelProperty(value = "停机线")
	private BigDecimal haltLine;

	/**
	 * 解绑：0-未解绑；1-已解绑
	 */
	@ApiModelProperty(value = "解绑：0-未解绑；1-已解绑")
	private Integer unbind;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

}
