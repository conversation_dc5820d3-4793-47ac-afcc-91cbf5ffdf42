/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.basic.dto.MonitorDTO;
import com.snszyk.zbusiness.basic.dto.MonitorModelDTO;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.dto.WaveDTO;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.entity.SensorInstance;
import com.snszyk.zbusiness.basic.service.IMonitorModelService;
import com.snszyk.zbusiness.basic.service.IMonitorParamService;
import com.snszyk.zbusiness.basic.service.IMonitorService;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.logic.MonitorLogicService;
import com.snszyk.zbusiness.basic.vo.*;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;


/**
 * 设备测点表 控制器
 *
 * <AUTHOR>
 * @since 2022-10-13
 */
@RestController
@AllArgsConstructor
@RequestMapping("/monitor")
@Api(value = "设备测点表", tags = "设备测点表接口")
@Validated
public class MonitorController extends SzykController {

	private final IMonitorService monitorService;
	private final MonitorLogicService monitorLogicService;
	private final ISensorInstanceService sensorInstanceService;
	private final IMonitorParamService monitorParamService;
	private final IMonitorModelService monitorModelService;

	/**
	 * 详情
	 */
	@GetMapping("{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<MonitorDTO> detail(@PathVariable Long id) {
		return R.data(monitorLogicService.detail(id));
	}

	/**
	 * 列表 测点信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入设备id")
	public R<List<MonitorDTO>> list(Long equipmentId) {
		List<MonitorDTO> list = monitorLogicService.listByEquipment(equipmentId);
		return R.data(list);
	}

	/**
	 * 分页 测点信息表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树结构id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "keywords", value = "测点名称或编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入monitor")
	public R<IPage<MonitorDTO>> page(@ApiIgnore MonitorVO monitor, Query query) {
		monitor.setIsConfig(Boolean.FALSE);
		IPage<MonitorDTO> pages = monitorService.page(Condition.getPage(query), monitor);
		return R.data(pages);
	}

	/**
	 * 新增 测点信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入monitor")
	public R save(@Valid @RequestBody Monitor monitor) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(monitorService.save(monitor));
	}

	/**
	 * 修改 测点信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入monitor")
	public R update(@Valid @RequestBody Monitor monitor) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(monitorService.updateById(monitor));
	}

	/**
	 * 新增或修改 测点信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入equipment")
	public R submit(@RequestBody EquipmentVO equipment) {
		return R.status(monitorLogicService.submit(equipment));
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(monitorLogicService.removeMonitor(Func.toLongList(ids)));
	}

	/**
	 * 校验删除
	 */
	@PostMapping("/check-remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "校验删除", notes = "传入id")
	public R<Boolean> checkAndRemove(@ApiParam(value = "主键", required = true) @RequestParam Long id) {
		// 校验是否绑定传感器实例
		int bindCount = sensorInstanceService.count(Wrappers.<SensorInstance>query().lambda()
			.eq(SensorInstance::getMonitorId, id)
			.eq(SensorInstance::getIsDeleted, 0));
		if (bindCount > 0) {
			return R.data(Boolean.FALSE);
		}
		return R.data(Boolean.TRUE);
	}

	/**
	 * 所含测点列表 测点信息表
	 */
	@GetMapping("/monitorList")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "所含测点列表", notes = "传入parentId、name")
	public R<List<MonitorDTO>> monitorList(@ApiParam(value = "树结构id", required = true) @RequestParam Long parentId,
										   @ApiParam(value = "部位名称") @RequestParam(required = false) String monitorName,
										   @ApiParam(value = "部位类型（字典：equipment_type）") @RequestParam(required = false) Integer equipmentType) {
		List<Monitor> list = monitorService.list(Wrappers.<Monitor>query().lambda()
			.like(Monitor::getPath, parentId).like(Func.isNotEmpty(monitorName), Monitor::getName, monitorName)
			.eq(Func.isNotEmpty(equipmentType), Monitor::getEquipmentType, equipmentType).orderByDesc(Monitor::getCreateTime));
		List<MonitorDTO> monitors = list.stream().map(monitor -> {
			MonitorDTO dto = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorDTO.class));
			dto.setEquipmentTypeName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, monitor.getEquipmentType()))
				.setPathName(dto.getPathName().replace(StringPool.COMMA, StringPool.SLASH));
			return dto;
		}).collect(Collectors.toList());
		return R.data(monitors);
	}

	/**
	 * 测点配置传感器-测点绑定的传感器实例列表
	 */
	@GetMapping("/monitorSensorInstanceList")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "测点绑定的传感器实例列表", notes = "传入monitorId")
	public R<List<SensorInstanceDTO>> monitorSensorInstanceList(@ApiParam(value = "测点id") @RequestParam Long monitorId) {
		return R.data(monitorLogicService.monitorSensorInstanceList(monitorId));
	}

	/**
	 * 测点绑定传感器
	 */
	@PostMapping("/bindSensorInstance")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "测点绑定传感器", notes = "传入monitorId、sensorInstanceList")
	public R<Boolean> bindSensorInstance(@ApiParam(value = "测点id") @RequestParam Long monitorId,
										 @RequestBody List<SensorInstanceVO> sensorInstanceList) {
		return R.status(monitorLogicService.bindSensorInstance(monitorId, sensorInstanceList));
	}

	/**
	 * 解绑传感器实例 测点信息表
	 */
	@PostMapping("/unbindSensorInstance")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "id", value = "传感器实例id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "code", value = "传感器编码", paramType = "query", dataType = "String")
	})
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "解绑传感器实例", notes = "传入sensorInstance")
	public R<Boolean> unbindSensorInstance(@RequestBody SensorInstanceVO sensorInstanceVO) {
		return R.status(monitorLogicService.unbindSensorInstance(sensorInstanceVO));
	}

	/**
	 * 3D模型配置测点分页 测点信息表
	 */
	@GetMapping("/monitorPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树结构id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "equipmentId", value = "设备id", paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "keywords", value = "测点名称或编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "3D模型配置测点分页", notes = "传入monitor")
	public R<IPage<MonitorDTO>> monitorPage(@ApiIgnore MonitorVO monitor, Query query) {
		monitor.setIsConfig(Boolean.TRUE);
		IPage<MonitorDTO> pages = monitorService.page(Condition.getPage(query), monitor);
		return R.data(pages);
	}

	/**
	 * 绑定传感器实例的虚拟3D模型 测点信息表
	 */
	@PostMapping("/bindSensorInstanceModel")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "【新】绑定传感器实例的3D模型", notes = "传入sensorInstanceList")
	public R<Boolean> bindSensorInstanceModel(@RequestBody List<SensorInstanceVO> sensorInstanceList) {
		return R.status(monitorLogicService.bindSensorInstanceModel(sensorInstanceList));
	}

	/**
	 * 校验&删除虚拟传感器
	 */
	@PostMapping("/remove-virtual-sensor")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "【新】校验并删除虚拟传感器", notes = "传入ids")
	public R<DelResultVO> removeVirtualSensor(@ApiParam(value = "虚拟传感器主键集合", required = true) @RequestParam String ids) {
		DelResultVO vo = monitorLogicService.removeVirtualSensor(Func.toStrList(ids));
		R<DelResultVO> result = new R<>();
		result.setCode(ResultCode.SUCCESS.getCode());
		result.setData(vo);
		result.setSuccess(vo.getFailureNumber() == 0);
		return result;
	}

	/**
	 * 配置设备测点参数 测点信息表
	 */
	@PostMapping("/configMonitorParam")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "配置设备测点参数", notes = "传入monitorParam")
	public R configMonitorParam(@RequestBody MonitorParamVO monitorParam) {
		return R.status(monitorParamService.submit(monitorParam));
	}

	/**
	 * 设备测点参数配置详情 测点信息表
	 */
	@GetMapping("/monitorParamDetail")
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "设备测点参数配置详情", notes = "传入id")
	public R<MonitorParamVO> monitorParamDetail(Long monitorId) {
		return R.data(monitorParamService.detail(monitorId));
	}

	/**
	 * 机理模型部位分页 测点信息表
	 */
	@GetMapping("/modelMonitorPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树结构id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "modelId", value = "机理模型id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "applyStatus", value = "应用状态（0：未应用，1：已应用，2：全部）", required = true, paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "keywords", value = "测点名称或编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 18)
	@ApiOperation(value = "机理模型部位分页", notes = "传入monitor")
	public R<IPage<MonitorDTO>> modelMonitorPage(@ApiIgnore MonitorVO monitor, Query query) {
		IPage<MonitorDTO> pages = monitorService.modelMonitorPage(Condition.getPage(query), monitor);
		return R.data(pages);
	}

	/**
	 * 标准门限波形分页 波形表
	 */
	@GetMapping("/thresholdWavePage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树结构id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "thresholdId", value = "标准门限id", required = true, paramType = "query", dataType = "Long")
	})
	@ApiOperationSupport(order = 19)
	@ApiOperation(value = "标准门限波形分页", notes = "传入wave")
	public R<IPage<WaveDTO>> thresholdWavePage(@ApiIgnore WaveVO wave, Query query) {
		IPage<WaveDTO> pages = monitorService.thresholdWavePage(Condition.getPage(query), wave);
		return R.data(pages);
	}

	/**
	 * 测点波形列表
	 */
	@GetMapping("/waveList")
	@ApiOperationSupport(order = 20)
	@ApiOperation(value = "测点波形列表", notes = "传入monitorId")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "tenantId", value = "租户id", required = true, paramType = "query", dataType = "String"),
		@ApiImplicitParam(name = "monitorId", value = "测点id", required = true, paramType = "query", dataType = "Long")
	})
	public R<List<WaveDTO>> waveList(@NotBlank @RequestParam String tenantId, @NotNull @RequestParam Long monitorId) {
		List<WaveDTO> list = monitorService.waveList(tenantId, monitorId);
		return R.data(list);
	}

	/**
	 * 测点应用机理模型列表
	 */
	@GetMapping("/monitorModelPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树结构id", required = true, paramType = "query", dataType = "Long"),
		@ApiImplicitParam(name = "applyData", value = "机理类型（1：振动机理，2：应力波机理）", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "keywords", value = "部位名称或编号", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 21)
	@ApiOperation(value = "测点应用机理模型列表", notes = "传入monitorModel")
	public R<IPage<MonitorModelDTO>> monitorModelPage(@ApiIgnore MonitorModelVO monitorModel, Query query) {
		IPage<MonitorModelDTO> pages = monitorModelService.monitorModelPage(Condition.getPage(query), monitorModel);
		return R.data(pages);
	}

}
