package com.snszyk.zbusiness.basic.vo;

import com.snszyk.core.mp.support.Query;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 设备运行状态统计vo
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode()
@ApiModel(value = "CollectionStationVO对象", description = "CollectionStationVO对象")
public class EquipmentRunningStatVO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty("设备/组织路径id")
	private String pathId;

	@ApiModelProperty("租户id")
	private String tenantId;

	@ApiModelProperty("分页参数")
	private Query query;

}
