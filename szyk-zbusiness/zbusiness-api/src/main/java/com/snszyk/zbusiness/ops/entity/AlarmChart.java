/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 报警管理图谱表实体类
 *
 * <AUTHOR>
 * @since 2023-05-23
 */
@Data
@Accessors(chain = true)
@TableName("eolm_alarm_chart")
@ApiModel(value = "AlarmChart对象", description = "报警管理图谱表")
public class AlarmChart implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
	/**
	 * 报警id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警id")
	private Long alarmId;
	/**
	 * 报警明细id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警明细id")
	private Long alarmDetailId;
	/**
	 * 报警记录id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警记录id")
	private Long alarmRecordId;
	/**
	 * 测点id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点id")
	private Long monitorId;
	/**
	 * 报警指标
	 */
	@ApiModelProperty(value = "报警指标")
	private String alarmIndex;
	/**
	 * 趋势图id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "趋势图id")
	private Long trendChart;
	/**
	 * 时域图id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "时域图id")
	private Long timeDomainDiagram;
	/**
	 * 频域图id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "频域图id")
	private Long freqDomainDiagram;
	/**
	 * 包络图id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "包络图id")
	private Long envelopDiagram;
	/**
	 * 数据采集时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "数据采集时间")
	private Date originTime;
	/**
	 * 报警时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "报警时间")
	private Date alarmTime;

}
