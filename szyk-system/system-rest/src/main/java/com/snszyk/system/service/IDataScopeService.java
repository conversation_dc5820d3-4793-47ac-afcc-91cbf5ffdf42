/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.service;

import com.snszyk.system.entity.DataScope;
import com.snszyk.core.mp.base.BaseService;
import com.snszyk.system.vo.DelResultVO;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 */
public interface IDataScopeService extends BaseService<DataScope> {


	/**
	 * 校验并删除数据权限
	 * @param ids
	 * @return
	 */
	DelResultVO checkAndRemoveScope(List<Long> ids);
}
