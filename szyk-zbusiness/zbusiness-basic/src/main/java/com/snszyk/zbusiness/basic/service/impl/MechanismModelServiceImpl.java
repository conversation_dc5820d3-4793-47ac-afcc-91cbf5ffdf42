/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.dto.MechanismModelDTO;
import com.snszyk.zbusiness.basic.dto.SystemConfigStatDTO;
import com.snszyk.zbusiness.basic.entity.*;
import com.snszyk.zbusiness.basic.mapper.MechanismModelMapper;
import com.snszyk.zbusiness.basic.service.*;
import com.snszyk.zbusiness.basic.vo.MechanismModelParamVO;
import com.snszyk.zbusiness.basic.vo.MechanismModelVO;
import com.snszyk.zbusiness.basic.vo.ModelParamVO;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.common.enums.DictBizEnum;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 机理模型 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-01
 */
@Service
@AllArgsConstructor
public class MechanismModelServiceImpl extends BaseServiceImpl<MechanismModelMapper, MechanismModel> implements IMechanismModelService {

	private final IMonitorService monitorService;
	private final IMonitorModelService monitorModelService;
	private final IMechanismModelParamService mechanismModelParamService;
	private final IAiModelService aiModelService;
	private final IEquipmentAiService equipmentAiService;

	@Override
	public IPage<MechanismModelDTO> page(IPage<MechanismModelDTO> page, MechanismModelVO vo) {
		List<MechanismModelDTO> list = baseMapper.page(page, vo);
		if(Func.isNotEmpty(list)){
			list.forEach(mechanismModel -> {
				mechanismModel.setApplyEquipmentName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, mechanismModel.getApplyEquipment()))
					.setApplyPowerName(DictBizCache.getValue(DictBizEnum.POWER_RANGE, mechanismModel.getApplyPower()))
					.setTypeName(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, mechanismModel.getType()))
					.setApplyDataName(DictBizCache.getValue(DictBizEnum.DATA_TYPE, mechanismModel.getApplyData()));
				List<MechanismModelParam> modelParamList = mechanismModelParamService.list(Wrappers.<MechanismModelParam>query().lambda()
					.eq(MechanismModelParam::getModelId, mechanismModel.getId()).orderByAsc(MechanismModelParam::getParamType));
				if(Func.isNotEmpty(modelParamList)){
					List<MechanismModelParamVO> modelParamVoList = modelParamList.stream().map(modelParam -> {
						MechanismModelParamVO modelParamVO = Objects.requireNonNull(BeanUtil.copy(modelParam, MechanismModelParamVO.class));
						modelParamVO.setParamTypeName(DictBizCache.getValue(DictBizEnum.MODEL_PARAM_TYPE, modelParam.getParamType()))
							.setParamList(JSONUtil.toList(modelParam.getParamInfo(), ModelParamVO.class));
						return modelParamVO;
					}).collect(Collectors.toList());
					mechanismModel.setModelParamList(modelParamVoList);
				}
			});
		}
		return page.setRecords(list);
	}

	@Override
	public MechanismModelDTO detail(Long id) {
		MechanismModel mechanismModel = this.getById(id);
		if(mechanismModel == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		MechanismModelDTO detail = Objects.requireNonNull(BeanUtil.copy(mechanismModel, MechanismModelDTO.class));
		detail.setApplyEquipmentName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, detail.getApplyEquipment()))
			.setApplyPowerName(DictBizCache.getValue(DictBizEnum.POWER_RANGE, detail.getApplyPower()))
			.setTypeName(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, detail.getType()))
			.setApplyDataName(DictBizCache.getValue(DictBizEnum.DATA_TYPE, detail.getApplyData()));
		List<MechanismModelParam> modelParamList = mechanismModelParamService.list(Wrappers.<MechanismModelParam>query().lambda()
			.eq(MechanismModelParam::getModelId, detail.getId()).orderByAsc(MechanismModelParam::getParamType));
		if(Func.isNotEmpty(modelParamList)){
			List<MechanismModelParamVO> list = modelParamList.stream().map(modelParam -> {
				MechanismModelParamVO modelParamVO = Objects.requireNonNull(BeanUtil.copy(modelParam, MechanismModelParamVO.class));
				modelParamVO.setParamTypeName(DictBizCache.getValue(DictBizEnum.MODEL_PARAM_TYPE, modelParam.getParamType()))
					.setParamList(JSONUtil.toList(modelParam.getParamInfo(), ModelParamVO.class));
				return modelParamVO;
			}).collect(Collectors.toList());
			detail.setModelParamList(list);
		}
		return detail;
	}

	@Override
	public boolean submit(MechanismModelVO vo) {
		// 编辑时，若修改应用设备类型或应用功率范围，页面给出提示，若确认，则解除当前模型与部位的绑定关系，需要用户重新配置
		if(Func.isNotEmpty(vo.getId())){
			MechanismModel entity = this.getById(vo.getId());
			if(!Func.equals(entity.getApplyEquipment(), vo.getApplyEquipment())
				|| !Func.equals(entity.getApplyPower(), vo.getApplyPower())){
				monitorModelService.remove(Wrappers.<MonitorModel> query().lambda().eq(MonitorModel::getModelId, vo.getId()));
			}
		}
		// 校验是否已存在
		MechanismModel entity = this.getOne(Wrappers.<MechanismModel>query().lambda()
			.eq(MechanismModel::getApplyEquipment, vo.getApplyEquipment()).eq(MechanismModel::getApplyPower, vo.getApplyPower())
			.eq(MechanismModel::getType, vo.getType()).eq(MechanismModel::getApplyData, vo.getApplyData())
			.ne(Func.isNotEmpty(vo.getId()), MechanismModel::getId, vo.getId()));
		if(Func.isNotEmpty(entity)){
			throw new ServiceException("已存在基本信息与当前相同的机理模型！");
		}
		MechanismModel mechanismModel = Objects.requireNonNull(BeanUtil.copy(vo, MechanismModel.class));
		if(Func.isEmpty(this.list())){
			mechanismModel.setSort(Func.toInt(StringPool.ONE));
		}
		if(Func.isEmpty(mechanismModel.getId())){
			mechanismModel.setStatus(0);
			List<MechanismModel> list = this.list(Wrappers.<MechanismModel>query().lambda().orderByDesc(MechanismModel::getSort));
			if(Func.isNotEmpty(list)){
				mechanismModel.setSort(list.get(0).getSort()+1);
			}
		}
		// 保存基本信息
		boolean saveOrUpdate = this.saveOrUpdate(mechanismModel);
		// 保存模型参数
		if(Func.isNotEmpty(mechanismModel.getId())){
			mechanismModelParamService.remove(Wrappers.<MechanismModelParam>query().lambda().eq(MechanismModelParam::getModelId, mechanismModel.getId()));
		}
		if(Func.isNotEmpty(vo.getModelParamList())){
			List<MechanismModelParam> modelParamList = vo.getModelParamList().stream().map(modelParamVO -> {
				MechanismModelParam mechanismModelParam = Objects.requireNonNull(BeanUtil.copy(modelParamVO, MechanismModelParam.class));;
				mechanismModelParam.setModelId(mechanismModel.getId()).setParamInfo(JSONUtil.toJsonStr(modelParamVO.getParamList()));
				return mechanismModelParam;
				}).collect(Collectors.toList());
			mechanismModelParamService.saveBatch(modelParamList);
		}
		return saveOrUpdate;
	}

	@Override
	public Integer distribute(Long id, List<Long> monitorIds, Integer applyStatus) {
		MechanismModel mechanismModel = this.getById(id);
		if(mechanismModel == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		// 不应用
		if(applyStatus == 0){
			monitorIds.forEach(monitorId -> {
				monitorModelService.remove(Wrappers.<MonitorModel>query().lambda()
					.eq(MonitorModel::getMonitorId, monitorId)
					.eq(MonitorModel::getModelId, id));
			});
			return monitorIds.size();
		}
		List<MonitorModel> list = monitorIds.stream().map(monitorId -> {
			MonitorModel monitorModel = monitorModelService.getOne(Wrappers.<MonitorModel>query().lambda()
						.eq(MonitorModel::getMonitorId, monitorId)
						.eq(MonitorModel::getModelId, id));
			if(monitorModel == null){
				monitorModel = new MonitorModel();
				Monitor monitor = monitorService.getById(monitorId);
				monitorModel.setEquipmentId(monitor.getEquipmentId()).setMonitorId(monitorId).setModelId(id);
			}
			return monitorModel;
		}).collect(Collectors.toList());
		monitorModelService.saveOrUpdateBatch(list);
		list.forEach(monitorModel -> {
			MechanismModelVO vo = new MechanismModelVO(monitorModel.getMonitorId(), monitorModel.getModelId());
			this.configMonitorModel(vo);
		});
		return list.size();
	}

	@Override
	public SystemConfigStatDTO systemConfigStat() {
		Integer mechanismModelCount = this.count();
		Integer aiModelCount = aiModelService.count();
		SystemConfigStatDTO dto = new SystemConfigStatDTO(mechanismModelCount, aiModelCount);
		List<MonitorModel> mechanismModelApplyList = monitorModelService.list().stream()
			.collect(Collectors.groupingBy(MonitorModel::getModelId))
			.values().stream()
			.map(list -> list.get(0))
			.collect(Collectors.toList());
		dto.setMechanismModelApplyCount(mechanismModelApplyList.size());
		List<EquipmentAi> aiModelApplyList = equipmentAiService.list().stream()
			.collect(Collectors.groupingBy(EquipmentAi::getAiModelId))
			.values().stream()
			.map(list -> list.get(0))
			.collect(Collectors.toList());
		dto.setAiModelApplyCount(aiModelApplyList.size());
		return dto;
	}

	@Override
	public boolean configMonitorModel(MechanismModelVO vo) {
		MonitorModel monitorModel = monitorModelService.getOne(Wrappers.<MonitorModel>query().lambda()
			.eq(MonitorModel::getMonitorId, vo.getMonitorId()).eq(MonitorModel::getModelId, vo.getId()));
		MechanismModelDTO mechanismModelDTO;
		MechanismModel mechanismModel = this.getById(vo.getId());
		mechanismModelDTO = Objects.requireNonNull(BeanUtil.copy(mechanismModel, MechanismModelDTO.class));
		if(monitorModel != null && Func.isEmpty(monitorModel.getModelInfo())){
			List<MechanismModelParam> modelParamList = mechanismModelParamService.list(Wrappers.<MechanismModelParam>query().lambda()
				.eq(MechanismModelParam::getModelId, mechanismModelDTO.getId()).orderByAsc(MechanismModelParam::getParamType));
			if(Func.isNotEmpty(modelParamList)){
				List<MechanismModelParamVO> list = modelParamList.stream().map(modelParam -> {
					MechanismModelParamVO modelParamVO = Objects.requireNonNull(BeanUtil.copy(modelParam, MechanismModelParamVO.class));
					modelParamVO.setParamTypeName(DictBizCache.getValue(DictBizEnum.MODEL_PARAM_TYPE, modelParam.getParamType()))
						.setParamList(JSONUtil.toList(modelParam.getParamInfo(), ModelParamVO.class));
					return modelParamVO;
				}).collect(Collectors.toList());
				mechanismModelDTO.setModelParamList(list);
			}
		}
		if(monitorModel != null && Func.isNotEmpty(monitorModel.getModelInfo())){
			if(Func.isNotEmpty(vo.getModelParamList())){
				mechanismModelDTO.setModelParamList(vo.getModelParamList());
			} else {
				// 库数据覆盖个性化数据
				List<MechanismModelParam> modelParamList = mechanismModelParamService.list(Wrappers.<MechanismModelParam>query().lambda()
					.eq(MechanismModelParam::getModelId, mechanismModelDTO.getId()).orderByAsc(MechanismModelParam::getParamType));
				if(Func.isNotEmpty(modelParamList)){
					List<MechanismModelParamVO> list = modelParamList.stream().map(modelParam -> {
						MechanismModelParamVO modelParamVO = Objects.requireNonNull(BeanUtil.copy(modelParam, MechanismModelParamVO.class));
						modelParamVO.setParamTypeName(DictBizCache.getValue(DictBizEnum.MODEL_PARAM_TYPE, modelParam.getParamType()))
							.setParamList(JSONUtil.toList(modelParam.getParamInfo(), ModelParamVO.class));
						return modelParamVO;
					}).collect(Collectors.toList());
					mechanismModelDTO.setModelParamList(list);
				}
			}
		}
		if(Func.isNotEmpty(mechanismModelDTO)){
			monitorModel.setModelInfo(JSONUtil.toJsonStr(mechanismModelDTO));
		}
		return monitorModelService.updateById(monitorModel);
	}

	@Override
	public MechanismModelDTO monitorModelDetail(Long monitorId, Long modelId) {
		MonitorModel monitorModel = monitorModelService.getOne(Wrappers.<MonitorModel>query().lambda()
			.eq(MonitorModel::getMonitorId, monitorId).eq(MonitorModel::getModelId, modelId));
		if(monitorModel == null){
			return null;
		}
		MechanismModelDTO detail = null;
		if(Func.isNotEmpty(monitorModel.getModelInfo())){
			detail = JSONUtil.toBean(monitorModel.getModelInfo(), MechanismModelDTO.class);
			detail.setApplyEquipmentName(DictBizCache.getValue(DictBizEnum.EQUIPMENT_TYPE, detail.getApplyEquipment()))
				.setApplyPowerName(DictBizCache.getValue(DictBizEnum.POWER_RANGE, detail.getApplyPower()))
				.setTypeName(DictBizCache.getValue(DictBizEnum.MODEL_TYPE, detail.getType()))
				.setApplyDataName(DictBizCache.getValue(DictBizEnum.DATA_TYPE, detail.getApplyData()));
			if(Func.isNotEmpty(detail.getModelParamList())){
				detail.getModelParamList().forEach(modelParamVO -> {
					modelParamVO.setParamTypeName(DictBizCache.getValue(DictBizEnum.MODEL_PARAM_TYPE, modelParamVO.getParamType()));
				});
			}
		}
		return detail;
	}

}
