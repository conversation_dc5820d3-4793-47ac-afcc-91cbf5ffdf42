package com.snszyk.zbusiness.basic.feign;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * feign callback
 * <AUTHOR>
 */
@Component
@Slf4j
public class PythonServerFeignCallback implements PythonServerFeign {

	@Override
	public List<BigDecimal> freqDomainWave(String originTime, String waveId, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public JSONObject freqAndEnvelope(String originTime, String waveId, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public List<BigDecimal> wave(String originTime, String waveId, Integer waveType, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public JSONObject bode(String originTime, String waveId, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public List<List<BigDecimal>> waterfall(String originTime, String waveId, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public JSONObject increase(String beginTime, String endTime, String waveId, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public JSONObject mechanismModel(String originTime, String waveId, String sendAlarm, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public JSONObject dbm(String originTime, String waveId, String monitorId) {
		log.error("数据异常");
		return null;
	}

	@Override
	public JSONObject getStopLineByWaveId(String waveId) {
		log.error("数据异常");
		return null;
	}

}
