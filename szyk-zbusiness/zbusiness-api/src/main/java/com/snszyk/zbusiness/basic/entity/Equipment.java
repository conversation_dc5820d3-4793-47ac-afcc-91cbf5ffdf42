/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 设备信息表实体类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Data
@Accessors(chain = true)
@TableName("eolm_equipment")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Equipment对象", description = "设备信息表")
public class Equipment extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 父主键
	*/
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父主键")
	private Long deviceId;
	/**
	* 编码
	*/
	@ApiModelProperty(value = "编码")
	private String code;
	/**
	* 名称
	*/
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	* 型号
	*/
	@ApiModelProperty(value = "型号")
	private String model;
	/**
	* 频率
	*/
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "频率")
	private BigDecimal frequency;
	/**
	* 转速
	*/
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@ApiModelProperty(value = "转速")
	private BigDecimal rev;
	/**
	* 照片
	*/
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "照片")
	private Long image;
	/**
	 * 设备等级,1:A级关键，2:B级重要，3:C级一般
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备等级,1:A级关键，2:B级重要，3:C级一般")
	private Integer grade;
	/**
	* 分类（业务字典：设备分类）
	*/
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "分类（业务字典：设备分类）")
	private Integer category;
	/**
	* 设备类型（二进制算法，1：sidas，2：lubricate，3：sidas+lubricate）
	*/
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备类型（二进制算法，1：sidas，2：lubricate，3：sidas+lubricate）")
	private Integer type;
	/**
	 * 功率
	 */
	@ApiModelProperty(value = "设备功率")
	private BigDecimal power;
	/**
	 * 故障状态
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "故障状态")
	private Integer isFault;
	/**
	 * 3D场景id
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "3D场景id")
	private Long sceneId;
	/**
	 * 制造厂商
	 */
	@ApiModelProperty(value = "制造厂商")
	private String manufacturer;
	/**
	 * 生产工艺
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "生产工艺")
	private Integer produceTech;
	/**
	 * 预期寿命（天）
	 */
	@ApiModelProperty(value = "预期寿命（天）")
	private Integer lifeExpectancy;
	/**
	 * 开始使用日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("开始使用日期")
	private Date startDateOfUse;
	/**
	 * RFID标签
	 */
	@ApiModelProperty(value = "RFID标签")
	private String rfid;
	/**
	 * 生产日期
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd")
	@ApiModelProperty("生产日期")
	private Date productionDate;
	/**
	 * 是否需要点检：0-不需要，1-需要
	 */
	@ApiModelProperty(value = "是否需要点检：0-不需要，1-需要")
	private Integer needSpotCheck;
	/**
	 * 运行状态：0-停机；1-运行中。
	 */
	@TableField(updateStrategy= FieldStrategy.NOT_NULL)
	@ApiModelProperty(value = "运行状态：0-停机；1-运行中。")
	private Integer isRunning;
	/**
	 * 最新运行时间
	 */
	@TableField(updateStrategy= FieldStrategy.NOT_NULL)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("最新运行时间")
	private Date originRuntime;
	/**
	 * 运行时长
	 */
	@TableField(updateStrategy= FieldStrategy.NOT_NULL)
	@ApiModelProperty(value = "运行时长")
	private Long runningTime;
	/**
	 * 停机时长
	 */
	@TableField(updateStrategy= FieldStrategy.NOT_NULL)
	@ApiModelProperty(value = "停机时长")
	private Long shutdownTime;
	/**
	 * 本次运行时长
	 */
	@TableField(updateStrategy= FieldStrategy.NOT_NULL)
	@ApiModelProperty(value = "本次运行时长")
	private Long currentRuntime;
	/**
	 * 全路径
	 */
	@ApiModelProperty(value = "全路径")
	private String path;
	/**
	 * 全路径名称
	 */
	@ApiModelProperty(value = "全路径名称")
	private String pathName;
	/**
	 * 排序
	 */
	@ApiModelProperty(value = "排序")
	private Integer sort;
	/**
	 * 点检状态
	 */
	@ApiModelProperty(value = "点检状态")
	private Integer inspectStatus;

	@Override
	public String toString() {
		return "Equipment{" +
			"deviceId=" + deviceId +
			", code='" + code + '\'' +
			", name='" + name + '\'' +
			", model='" + model + '\'' +
			", frequency=" + frequency +
			", rev=" + rev +
			", image=" + image +
			", category=" + category +
			", sort=" + sort +
			", power=" + power +
			", isFault=" + isFault +
			", sceneId=" + sceneId +
			", manufacturer='" + manufacturer + '\'' +
			", produceTech=" + produceTech +
			", isRunning=" + isRunning +
			", runningTime=" + runningTime +
			", shutdownTime=" + shutdownTime +
			'}';
	}
}
