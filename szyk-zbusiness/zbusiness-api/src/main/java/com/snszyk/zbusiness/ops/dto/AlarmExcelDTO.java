/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 报警管理业务表导出excel
 *
 * <AUTHOR>
 * @since 2023-04-18
 */
@Data
@ColumnWidth(25)
public class AlarmExcelDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 处理状态
	 */
	@ExcelProperty(value = {"处理状态"}, index = 0)
	@ApiModelProperty(value = "处理状态")
	private String statusName;
	/**
	 * 设备名称
	 */
	@ExcelProperty(value = {"设备名称"}, index = 1)
	@ApiModelProperty(value = "设备名称")
	private String equipmentName;
	/**
	 * 设备路径
	 */
	@ExcelProperty(value = {"设备路径"}, index = 2)
	@ApiModelProperty(value = "设备路径")
	private String pathName;
	/**
	 * 报警等级
	 */
	@ExcelProperty(value = {"报警等级"}, index = 3)
	@ApiModelProperty(value = "报警等级")
	private String alarmLevelName;
	/**
	 * 报警类型
	 */
	@ExcelProperty(value = {"报警类型"}, index = 4)
	@ApiModelProperty(value = "报警类型")
	private String alarmTypeName;
	/**
	 * 首次报警时间
	 */
	@ExcelProperty(value = {"首次报警时间"}, index = 5)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("首次报警时间")
	private Date firstAlarmTime;
	/**
	 * 最新报警时间
	 */
	@ExcelProperty(value = {"最新报警时间"}, index = 6)
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty("最新报警时间")
	private Date lastAlarmTime;
	/**
	 * 持续时间
	 */
	@ExcelProperty(value = {"持续时间"}, index = 7)
	@ApiModelProperty(value = "持续时间")
	private String duration;
	/**
	 * 报警摘要
	 */
	@ExcelProperty(value = {"报警摘要"}, index = 8)
	@ApiModelProperty(value = "报警摘要")
	private String alarmAbstract;

}
