package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 集团驾驶舱-检测范围
 * @ClassName: DetectionRangeDTO
 * @author: wangmh
 * @create: 2022-12-15 10:54
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DetectionRangeDTO {

	@ApiModelProperty(value	= "设备总数")
	private Integer equipmentNum;

	@ApiModelProperty(value = "监测设备数")
	private Integer monitorEquipmentNum;

	@ApiModelProperty(value = "监测测点数")
	private Integer monitorNum;

	@ApiModelProperty(value = "覆盖率")
	private String coverage;
}
