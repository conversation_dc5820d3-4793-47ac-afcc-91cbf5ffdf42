package com.snszyk.zbusiness.basic.config;

import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.snszyk.core.tool.utils.StringPool;

/**
 * 按部位（id）组成动态表名 - 每个部位的传感器数据单独存入一个表：sidas_sensor_data_${monitor_id}
 * <AUTHOR>
 */
public class MonitorTableNameHandler implements TableNameHandler {

	/**
	 * 传感器数据表名
	 */
	public static final String SENSOR_DATA_TABLE_NAME = "sidas_sensor_data";

	/**
	 * 每个请求线程维护一个monitorId数据，避免多线程数据冲突。
	 */
	private static final ThreadLocal<Long> MONITOR_ID_DATA = new ThreadLocal<>();

	/**
	 * 设置请求线程的monitorId数据
	 * @param monitorId 测点id
	 */
	public static void setMonitorIdData(Long monitorId) {
		MONITOR_ID_DATA.set(monitorId);
	}

	/**
	 * 删除当前请求线程的monitorId数据
	 */
	public static void removeMonitorIdData() {
		MONITOR_ID_DATA.remove();
	}

	/**
	 * 动态表名接口实现方法
	 * @param sql       当前执行 SQL
	 * @param tableName 表名
	 * @return
	 */
	@Override
	public String dynamicTableName(String sql, String tableName) {
		if (SENSOR_DATA_TABLE_NAME.equals(tableName)) {
			Long currentMonitorId = MONITOR_ID_DATA.get();
			if (currentMonitorId != null) {
				return SENSOR_DATA_TABLE_NAME + StringPool.UNDERSCORE + currentMonitorId;
			} else {
				throw new RuntimeException("当前线程的monitorId为null，请先调用setMonitorIdData()方法设置monitorId！");
			}
		} else {
			return tableName;
		}
	}

}
