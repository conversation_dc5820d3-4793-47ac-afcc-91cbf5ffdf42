/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.Monitor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 测点及相应门限
 *
 * <AUTHOR>
 * @date 2023/02/10 18:56
 **/
@Data
@ApiModel(value = "MonitorThresholdDTO对象", description = "MonitorThresholdDTO对象")
public class MonitorThresholdDTO extends Monitor {

	/**
	 * 一级门限值
	 */
	@ApiModelProperty(value = "一级门限值")
	private BigDecimal firstThreshold;

	/**
	 * 二级门限值
	 */
	@ApiModelProperty(value = "二级门限值")
	private BigDecimal secondThreshold;

	/**
	 * 三级门限值
	 */
	@ApiModelProperty(value = "三级门限值")
	private BigDecimal thirdThreshold;

	/**
	 * 四级门限值
	 */
	@ApiModelProperty(value = "四级门限值")
	private BigDecimal fourthThreshold;

	/**
	 * 指标
	 */
	@ApiModelProperty(value = "指标")
	private String quotaCode;

}
