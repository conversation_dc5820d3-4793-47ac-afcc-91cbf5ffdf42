/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.zbusiness.basic.entity.CollectionStation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 采集站表数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CollectionStationDTO对象", description = "采集站表")
public class CollectionStationDTO extends CollectionStation {

	private static final long serialVersionUID = 1L;

	/**
	 * 在线离线
	 */
	@ApiModelProperty(value = "在线离线")
	private String onlineName;

	/**
	 * 有线无线
	 */
	@ApiModelProperty(value = "有线无线")
	private String isWirelessName;

	/**
	 * 通道列表
	 */
	@ApiModelProperty("通道列表")
	private List<CollectionStationChannelDTO> channelList;

}
