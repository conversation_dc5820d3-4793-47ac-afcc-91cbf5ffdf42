/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;


import com.snszyk.zbusiness.ops.entity.AbnormalRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备异常明细表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AbnormalRecordDTO extends AbnormalRecord {
	private static final long serialVersionUID = 1L;

	/**
	 * 异常类型名称
	 */
	@ApiModelProperty(value = "异常类型名称")
	private String abnormalTypeName;

	/**
	 * 诊断结果列表
	 */
	@ApiModelProperty(value = "诊断结果列表")
	private List<AbnormalResultDTO> resultList;

	public AbnormalRecordDTO(){
		super();
	}

	public AbnormalRecordDTO(Integer abnormalType){
		super();
		this.setAbnormalType(abnormalType);
	}

}
