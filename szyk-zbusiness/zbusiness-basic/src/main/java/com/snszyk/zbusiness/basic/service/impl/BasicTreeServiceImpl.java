/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.auth.utils.CustomAuthUtil;
import com.snszyk.core.cache.constant.CacheConstant;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tenant.SzykTenantProperties;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.node.ForestNodeMerger;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.cache.BizCache;
import com.snszyk.zbusiness.basic.dto.TreeDTO;
import com.snszyk.zbusiness.basic.entity.BasicTree;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.enums.EquipmentPowerRangeEnum;
import com.snszyk.zbusiness.basic.mapper.BasicTreeMapper;
import com.snszyk.zbusiness.basic.mapper.EquipmentMapper;
import com.snszyk.zbusiness.basic.mapper.MonitorMapper;
import com.snszyk.zbusiness.basic.service.IBasicTreeService;
import com.snszyk.zbusiness.basic.vo.BasicTreeVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.snszyk.core.cache.constant.CacheConstant.BIZ_CACHE;

/**
 * 基础树表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Service
@AllArgsConstructor
public class BasicTreeServiceImpl extends ServiceImpl<BasicTreeMapper, BasicTree> implements IBasicTreeService {

	private static final String TENANT_ID = "tenantId";
	private static final String PARENT_ID = "parentId";
	private static final String APPLY_EQUIPMENT_TYPE = "applyEquipment";
	private static final String APPLY_POWER_RANGE = "applyPower";
	private final SzykRedis szykRedis;
	private final SzykTenantProperties tenantProperties;
	private final MonitorMapper monitorMapper;
	private final EquipmentMapper equipmentMapper;

	@Override
	public List<BasicTreeVO> lazyList(Long parentId, Map<String, Object> param) {
		String tenantId = Func.toStr(param.get(TENANT_ID));
		// 非平台管理员角色
		if (!CustomAuthUtil.isPlatformAdmin()) {
			tenantId = AuthUtil.getTenantId();

		}
		if (Func.isEmpty(param.get(PARENT_ID))) {
			parentId = 0L;
		}
		return baseMapper.lazyList(tenantId, parentId, param);
	}

	@Override
	public List<BasicTreeVO> tree(String tenantId) {
		return ForestNodeMerger.merge(baseMapper.tree(tenantId));
	}

	@Override
	public List<BasicTreeVO> lazyTree(String tenantId, Long parentId) {
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		return ForestNodeMerger.merge(baseMapper.lazyTree(tenantId, parentId));
	}

	@Override
	public String getNodeIds(String tenantId, String nodeNames) {
		List<BasicTree> deptList = baseMapper.selectList(Wrappers.<BasicTree>query().lambda()
			.eq(BasicTree::getTenantId, tenantId).in(BasicTree::getNodeName, Func.toStrList(nodeNames)));
		if (deptList != null && deptList.size() > 0) {
			return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public String getNodeIdsByFuzzy(String tenantId, String nodeNames) {
		LambdaQueryWrapper<BasicTree> queryWrapper = Wrappers.<BasicTree>query().lambda().eq(BasicTree::getTenantId, tenantId);
		queryWrapper.and(wrapper -> {
			List<String> names = Func.toStrList(nodeNames);
			names.forEach(name -> wrapper.like(BasicTree::getNodeName, name).or());
		});
		List<BasicTree> deptList = baseMapper.selectList(queryWrapper);
		if (deptList != null && deptList.size() > 0) {
			return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public List<String> getNodeNames(String nodeIds) {
		return baseMapper.getNodeNames(Func.toLongArray(nodeIds));
	}

	@Override
	public List<BasicTree> getNodeChild(Long nodeId) {
		return baseMapper.selectList(Wrappers.<BasicTree>query().lambda().like(BasicTree::getAncestors, nodeId));
	}

	@Override
	public List<BasicTree> getNodeParent(Long nodeId) {
		// 当前子节点
		BasicTree basicTree = baseMapper.selectById(nodeId);
		List<Long> ids = Func.toLongList(basicTree.getPath());
		// 所有父节点
		List<Long> parentIds = ids.stream().filter(id -> !Func.equals(nodeId, id)).collect(Collectors.toList());
		Collections.reverse(parentIds);
		return baseMapper.selectList(Wrappers.<BasicTree>query().lambda().in(BasicTree::getId, parentIds));
	}

	@Override
	public List<BasicTree> getNodeParent(String tenantId, Map<String, Object> param) {
		QueryWrapper<BasicTree> queryWrapper = Condition.getQueryWrapper(param, BasicTree.class);
		List<BasicTree> nodeList = baseMapper.selectList(!tenantId.equals(SzykConstant.ADMIN_TENANT_ID) ? queryWrapper.lambda().eq(BasicTree::getTenantId, tenantId) : queryWrapper);
		List<BasicTree> allList = new ArrayList<>();
		nodeList.forEach(childNode -> {
			List<Long> ids = Func.toLongList(childNode.getPath());
			// 所有节点
			List<BasicTree> list = baseMapper.selectList(Wrappers.<BasicTree>query().lambda().in(BasicTree::getId, ids));
			allList.addAll(list);
		});
		return allList.stream().distinct().collect(Collectors.toList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public String submit(BasicTreeVO basicTreeVO) {
		CacheUtil.clear(BIZ_CACHE);
		BasicTree basicTree = Objects.requireNonNull(BeanUtil.copy(basicTreeVO, BasicTree.class));
		if (Func.isEmpty(basicTree.getParentId())) {
			basicTree.setTenantId(AuthUtil.getTenantId());
			basicTree.setParentId(SzykConstant.TOP_PARENT_ID);
			basicTree.setAncestors(String.valueOf(SzykConstant.TOP_PARENT_ID));
		}
		String parentPathName = "";
		if (basicTree.getParentId() > 0) {
			BasicTree parent = getById(basicTree.getParentId());
			if (Func.toLong(basicTree.getParentId()) == Func.toLong(basicTree.getId())) {
				throw new ServiceException("父节点不可选择自身!");
			}
			basicTree.setTenantId(parent.getTenantId());
			String ancestors = parent.getAncestors() + StringPool.COMMA + basicTree.getParentId();
			basicTree.setAncestors(ancestors);
			parentPathName = parent.getPathName();
		}
		saveOrUpdate(basicTree);
		// 更新path和pathName
		String path;
		String pathName;
		if (Func.equals(SzykConstant.TOP_PARENT_ID, basicTree.getParentId())) {
			path = String.valueOf(basicTree.getId());
			pathName = basicTree.getNodeName();
		} else {
			path = basicTree.getAncestors().substring(2) + StringPool.COMMA + basicTree.getId();
			pathName = parentPathName + StringPool.COMMA + basicTree.getNodeName();
		}
		basicTree.setPath(path);
		basicTree.setPathName(pathName);
		szykRedis.set(CacheConstant.BIZ_CACHE.concat(StringPool.COLON).concat(BizCache.BASIC_TREE_ID)
			.concat(String.valueOf(basicTree.getId())), basicTree);
		updateById(basicTree);
		return basicTree.getPath();
	}

	@Override
	public List<BasicTreeVO> selectDeviceTree(Long parentId) {
		String tenantId = AuthUtil.getTenantId();
		return ForestNodeMerger.merge(baseMapper.selectDeviceTree(tenantId, parentId));
	}

	@Override
	public List<BasicTreeVO> cockpitDeviceTree(Integer hasTop, SzykUser szykUser) {
		BasicTree basicTree = baseMapper.selectOne(Wrappers.<BasicTree>query().lambda()
			.eq(BasicTree::getNodeCode, szykUser.getDeptId()));
		// 多租户
		if (tenantProperties.getEnhance()) {
			return ForestNodeMerger.merge(baseMapper.cockpitDeviceTree(hasTop, basicTree.getId()));
		}
		return ForestNodeMerger.merge(baseMapper.cockpitDeviceTree(hasTop, null));
	}

	@Override
	public List<BasicTree> bizFilterList(String tenantId, Map<String, Object> param) {
		LambdaQueryWrapper<BasicTree> queryWrapper = null;
		List<Monitor> monitorList = null;
		// 机理模型筛选
		if (Func.isNotEmpty(param.get(APPLY_EQUIPMENT_TYPE)) && Func.isNotEmpty(param.get(APPLY_POWER_RANGE))) {
			LambdaQueryWrapper<Equipment> lambdaQueryWrapper = null;
			if (Func.toInt(param.get(APPLY_POWER_RANGE)) == EquipmentPowerRangeEnum.RANGE_ONE.getCode()) {
				lambdaQueryWrapper = Wrappers.<Equipment>query().lambda()
					.gt(Equipment::getPower, 0).le(Equipment::getPower, 15);
			}
			if (Func.toInt(param.get(APPLY_POWER_RANGE)) == EquipmentPowerRangeEnum.RANGE_TWO.getCode()) {
				lambdaQueryWrapper = Wrappers.<Equipment>query().lambda()
					.gt(Equipment::getPower, 15).le(Equipment::getPower, 75);
			}
			if (Func.toInt(param.get(APPLY_POWER_RANGE)) == EquipmentPowerRangeEnum.RANGE_THREE.getCode()) {
				lambdaQueryWrapper = Wrappers.<Equipment>query().lambda().gt(Equipment::getPower, 75);
			}
			List<Equipment> equipmentList = equipmentMapper.selectList(lambdaQueryWrapper);
			if (Func.isNotEmpty(equipmentList)) {
				monitorList = monitorMapper.selectList(Wrappers.<Monitor>query().lambda()
					.eq(Monitor::getEquipmentType, param.get(APPLY_EQUIPMENT_TYPE))
					.in(Monitor::getEquipmentId, equipmentList.stream().map(Equipment::getId).collect(Collectors.toList())));
			}
		} else {
			// 标准门限筛选
			if (Func.isNotEmpty(param.get(APPLY_EQUIPMENT_TYPE))) {
				monitorList = monitorMapper.selectList(Wrappers.<Monitor>query().lambda()
					.eq(Monitor::getEquipmentType, param.get(APPLY_EQUIPMENT_TYPE)));
			} else {
				return baseMapper.selectList(Wrappers.<BasicTree>query().lambda().eq(BasicTree::getTenantId, AuthUtil.getTenantId()));
			}
		}
		if (Func.isNotEmpty(monitorList)) {
			queryWrapper = new LambdaQueryWrapper<>();
			queryWrapper = queryWrapper.in(BasicTree::getId, monitorList.stream().map(Monitor::getId).collect(Collectors.toList()));
		}
		List<BasicTree> allList = new ArrayList<>();
		if (queryWrapper != null) {
			List<BasicTree> nodeList = baseMapper.selectList(queryWrapper);
			nodeList.forEach(childNode -> {
				List<Long> ids = Func.toLongList(childNode.getPath());
				// 所有节点
				List<BasicTree> list = baseMapper.selectList(Wrappers.<BasicTree>query().lambda().in(BasicTree::getId, ids));
				allList.addAll(list);
			});
		}
		return allList.stream().distinct().collect(Collectors.toList());
	}

	@Override
	public List<TreeDTO> areaList() {
		return baseMapper.areaList();
	}

}
