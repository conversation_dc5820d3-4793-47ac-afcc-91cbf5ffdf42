/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.CollectionUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationExcelDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationOnlineStatDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.mapper.CollectionStationChannelMapper;
import com.snszyk.zbusiness.basic.mapper.CollectionStationMapper;
import com.snszyk.zbusiness.basic.service.ICollectionStationService;
import com.snszyk.zbusiness.basic.vo.CollectionStationVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 采集站表 服务实现类
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Service
public class CollectionStationServiceImpl extends BaseServiceImpl<CollectionStationMapper, CollectionStation> implements ICollectionStationService {

	private final CollectionStationChannelMapper channelMapper;
	private final SzykRedis szykRedis;

	@Override
	public IPage<CollectionStationDTO> selectStationPage(IPage<CollectionStationDTO> page, CollectionStationVO vo) {
		List<CollectionStationDTO> records = baseMapper.selectStationPage(page, vo);
		if (CollectionUtil.isNotEmpty(records)) {
			records.forEach(station -> {
				station.setIsWirelessName(DictBizCache.getValue(DictBizEnum.SENSOR_TYPE, station.getIsWireless()))
					.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, station.getOnline()));
				List<CollectionStationChannelDTO> channelList = channelMapper.selectChannelList(station.getId());
				if (CollectionUtil.isNotEmpty(channelList)) {
					channelList.forEach(channelDTO -> {
						//根据【sensorCode:sensorInstanceParamId:outputType】获取最新电量值
						if (Func.isNotEmpty(channelDTO.getSpowerSensorInstanceParamId())) {
							String key = channelDTO.getSensorCode() + StringPool.COLON + channelDTO.getSpowerSensorInstanceParamId()
								+ StringPool.COLON + SampledDataTypeEnum.SENSOR_POWER.getCode();
							channelDTO.setElectricity(szykRedis.get(key));
						}
						channelDTO.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, channelDTO.getOnline()));
					});
					station.setChannelList(channelList);
				}
			});
		}
		return page.setRecords(records);
	}

	@Override
	public CollectionStationOnlineStatDTO onlineStat(String tenantId) {
		return baseMapper.onlineStat(tenantId);
	}

	@Override
	public void exportExcel(CollectionStationVO vo, HttpServletResponse response) {
		List<CollectionStation> list = this.list(Wrappers.<CollectionStation>query().lambda().orderByDesc(CollectionStation::getUpdateTime));
		List<CollectionStationExcelDTO> resultList = list.stream().map(collectionStation -> {
			CollectionStationExcelDTO excelDTO = Objects.requireNonNull(BeanUtil.copy(collectionStation, CollectionStationExcelDTO.class));
			excelDTO.setOnlineName(DictBizCache.getValue(DictBizEnum.ONLINE_STATE, collectionStation.getOnline()))
				.setIsWirelessName(DictBizCache.getValue(DictBizEnum.SENSOR_TYPE, collectionStation.getIsWireless()));
			excelDTO.setUpdateTime(null);
//			String updateTime = szykRedis.get(EolmConstant.Cache.COLLECTION_STATION_LATEST_DATA_TIME_PREFIX + collectionStation.getId());
//			if(Func.isNotEmpty(updateTime)){
//				excelDTO.setUpdateTime(DateUtil.parse(updateTime, DateUtil.PATTERN_DATETIME));
//			}
			return excelDTO;
		}).collect(Collectors.toList());
		try {
			response.addHeader("Content-Type", "application/octet-stream");
			response.setCharacterEncoding("UTF-8");
			response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("采集站工作状态.xlsx", "UTF-8"));
			EasyExcel.write(response.getOutputStream(), CollectionStationExcelDTO.class)
				.autoCloseStream(Boolean.FALSE)
				.sheet("采集站工作状态")
				.doWrite(resultList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public List<CollectionStationDTO> listByTenantIds(List<String> tenantIds) {
		List<CollectionStation> list = this.lambdaQuery()
			.in(CollectionStation::getTenantId, tenantIds)
			.list();
		return ObjectUtil.isEmpty(list) ? Collections.emptyList() : BeanUtil.copy(list, CollectionStationDTO.class);
	}

}
