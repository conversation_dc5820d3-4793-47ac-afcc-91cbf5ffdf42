/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.annotation.PreAuth;
import com.snszyk.core.secure.constant.AuthConstant;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.TreeDTO;
import com.snszyk.zbusiness.basic.entity.BasicTree;
import com.snszyk.zbusiness.basic.service.IBasicTreeService;
import com.snszyk.zbusiness.basic.vo.BasicTreeVO;
import com.snszyk.zbusiness.basic.wrapper.BasicTreeWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 基础树表 控制器
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@RestController
@AllArgsConstructor
@RequestMapping("/basicTree")
@Api(value = "基础树表", tags = "基础树表接口")
public class BasicTreeController extends SzykController {

	private final IBasicTreeService basicTreeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入basicTree", hidden = true)
	public R<BasicTreeVO> detail(BasicTree basicTree) {
		BasicTree detail = basicTreeService.getOne(Condition.getQueryWrapper(basicTree));
		return R.data(BasicTreeWrapper.build().entityVO(detail));
	}

	/**
	 * 列表
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "nodeName", value = "节点名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入basicTree", hidden = true)
	public R<List<BasicTreeVO>> list(@ApiIgnore @RequestParam Map<String, Object> basicTree, SzykUser szykUser) {
		QueryWrapper<BasicTree> queryWrapper = Condition.getQueryWrapper(basicTree, BasicTree.class);
		List<BasicTree> list = basicTreeService.list((!szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(BasicTree::getTenantId, szykUser.getTenantId()) : queryWrapper);
		return R.data(BasicTreeWrapper.build().listNodeVO(list));
	}

	/**
	 * 懒加载列表
	 */
	@GetMapping("/lazy-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "nodeName", value = "节点名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "nodeCategory", value = "节点类型", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "clientType", value = "访问端类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "tenantId", value = "租户ID", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入basicTree")
	public R<List<BasicTreeVO>> lazyList(@ApiIgnore @RequestParam Map<String, Object> basicTree, Long parentId) {
		List<BasicTreeVO> list = basicTreeService.lazyList(parentId, basicTree);
		return R.data(BasicTreeWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * 获取基础树形结构
	 *
	 * @return
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "树形结构", notes = "树形结构", hidden = true)
	public R<List<BasicTreeVO>> tree(String tenantId, SzykUser szykUser) {
		List<BasicTreeVO> tree = basicTreeService.tree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()));
		return R.data(tree);
	}

	/**
	 * 懒加载获取基础树形结构
	 */
	@GetMapping("/lazy-tree")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "懒加载树形结构", notes = "树形结构")
	public R<List<BasicTreeVO>> lazyTree(String tenantId, Long parentId, SzykUser szykUser) {
		List<BasicTreeVO> tree = basicTreeService.lazyTree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()), parentId);
		return R.data(tree);
	}

	/**
	 * 基本搜索树
	 */
	@GetMapping("/searchTree")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "nodeName", value = "节点名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "nodeCategory", value = "节点类型（搜地点：0，搜设备：1）", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "基本搜索树", notes = "传入basicTree")
	public R<List<BasicTreeVO>> searchTree(@ApiIgnore @RequestParam Map<String, Object> basicTree, SzykUser szykUser) {
		List<BasicTree> list = basicTreeService.getNodeParent(szykUser.getTenantId(), basicTree);
		return R.data(BasicTreeWrapper.build().listNodeVO(list));
	}

	/**
	 * 新增或修改 基础树表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "新增或修改", notes = "传入basicTree", hidden = true)
	public R submit(@Valid @RequestBody BasicTree basicTree) {
		return R.status(basicTreeService.saveOrUpdate(basicTree));
	}


	/**
	 * 删除 基础树表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "逻辑删除", notes = "传入ids", hidden = true)
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(basicTreeService.removeByIds(Func.toLongList(ids)));
	}

	/**
	 * 地点下拉树
	 */
	@PreAuth(AuthConstant.PERMIT_ALL)
	@GetMapping("/selectDeviceTree")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "下拉树", notes = "传入parentId")
	public R<List<BasicTreeVO>> selectDeviceTree(Long parentId) {
		List<BasicTreeVO> tree = basicTreeService.selectDeviceTree(parentId);
		return R.data(tree);
	}

	/**
	 * 驾驶舱设备树
	 */
	@GetMapping("/cockpitDeviceTree")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "驾驶舱设备树", notes = "驾驶舱设备树")
	public R<List<BasicTreeVO>> cockpitDeviceTree(@ApiParam(value = "是否返回顶级节点") @RequestParam(required = false) Integer hasTop, SzykUser szykUser) {
		List<BasicTreeVO> tree = basicTreeService.cockpitDeviceTree(hasTop, szykUser);
		return R.data(tree);
	}

	/**
	 * 业务筛选树
	 */
	@GetMapping("/bizFilterTree")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "applyEquipment", value = "应用设备类型", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "applyPower", value = "应用功率范围", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "nodeName", value = "节点名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "nodeCategory", value = "节点类型（搜地点：0，搜设备：1）", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "业务筛选树", notes = "传入basicTree")
	public R<List<BasicTreeVO>> bizFilterTree(@ApiIgnore @RequestParam Map<String, Object> basicTree, SzykUser szykUser) {
		List<BasicTree> list = basicTreeService.bizFilterList(szykUser.getTenantId(), basicTree);
		return R.data(BasicTreeWrapper.build().listNodeVO(list));
	}


	@GetMapping("/areaList")
	R<List<TreeDTO>> areaList() {
		return R.data(this.basicTreeService.areaList());
	}
}
