package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 厂区设备整体统计
 * @ClassName: PlantDeviceStatistics
 * @author: wangmh
 * @create: 2022-12-21 08:36
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlantDeviceStatisticsDTO {

	@ApiModelProperty(value = "设备总数")
	private Integer deviceTotal;

	@ApiModelProperty(value = "测点数")
	private Integer devicePoint;

	@ApiModelProperty(value = "检修设备")
	private Integer repairDevice;

	@ApiModelProperty(value = "报警设备")
	private Integer alarmDevice;

	@ApiModelProperty(value = "待处理报警条数")
	private Integer pendingAlarmDevice;
}
