package com.snszyk.zbusiness.basic.rabbit.bussiness;

import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.config.SidasBizConfig;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

/**
 * 3、保存传感器数据后，根据波形振动有效值更新设备运行时长
 * 将此设备的上一条运行状态数据保存在Redis中，
 * 1）equipment:running:state:id - （0-停机，1-运行）
 * 2）equipment:running:originTime:id - 最新一条数据的时间
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class EquipmentConditionBusiness extends AbstractBusiness{

	@Resource
	private IEquipmentService equipmentService;
	@Resource
	private SzykRedis szykRedis;
	@Resource
	private SidasBizConfig sidasBizConfig;

	@Override
	public String getCommand() {
		return Command.VIBRATE_COMMAND;
	}

	@Override
	public void business(MessageBean message) {
		//是否需要处理运行时长
		if (!sidasBizConfig.sidasBizProperties().isEnableRunningByVibration()
			|| Func.isEmpty(message.getValue())) {
			log.debug("不需要进行运行时长处理------------退出");
			return;
		}
		// 只针对加速度数据进行判断和计算设备运行时长
		if (!SampledDataTypeEnum.ACCELERATION.getCode().equals(message.getType())) {
			log.debug("运行时长数据需要用到加速度有效值，当前数据类型匹配");
			return;
		}
		String sensorCode = message.getId();
		Equipment equipment = equipmentService.getByMonitorId(message.getMonitorId());
		if (equipment == null) {
			log.error("设备不存在！！！monitorId = {}", message.getMonitorId());
			return;
		}
		if(Func.isEmpty(equipment.getOriginRuntime())){
			equipment.setOriginRuntime(new Date(message.getOriginTime()));
		}

		// 新来的数据采样时间 < 上次的采集时间，则忽略
		if(message.getOriginTime() < equipment.getOriginRuntime().getTime()){
			return;
		}

		// 更新设备运行状态
		// 获取停机线 2024.05.06
		BigDecimal haltLine = BigDecimal.valueOf(sidasBizConfig.sidasBizProperties().getRunningStateMin());


		String haltLineKey = String.format(ISensorInstanceService.HALT_LINE,sensorCode, StringPool.COLON);
		if (Func.isNotEmpty((Object) szykRedis.get(haltLineKey))) {
			haltLine = BigDecimal.valueOf(Func.toDouble(szykRedis.get(haltLineKey)));
		}

		boolean isRun = new BigDecimal(message.getValue()).compareTo(haltLine) >= 0;
		log.info("根据振动波形有效值判断设备是否运行 - ，振动波形有效值 = {} ， 当前停机线：{}  当前运行状态：{}"
			, message.getValue(), haltLine, isRun);
		boolean preRun = equipment.getIsRunning() == 1;
		log.debug("当前运行状态：{}  上一次运行状态：{}", isRun, preRun);


		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(message.getOriginTime());
		Date date = equipment.getOriginRuntime();
		//本次运行时长
		Long timeLong = calendar.getTimeInMillis() - date.getTime();

		//状态发生变化,更新运行及停止时长
		if(preRun != isRun){
			equipment.setOriginRuntime(calendar.getTime());
			if(isRun){
				equipment.setShutdownTime(equipment.getShutdownTime()+timeLong);
				equipment.setIsRunning(1);
			}else {
				equipment.setRunningTime(equipment.getRunningTime()+timeLong);
				equipment.setIsRunning(0);
			}
			equipment.setCurrentRuntime(0L);
		}
		if(isRun){
			equipment.setCurrentRuntime(timeLong);
		}
		// 更新设备运行状态 & 运行、停机时长
		boolean update = equipmentService.updateById(equipment);
		if (update) {
			log.info("更新设备运行状态、时长成功！");
		} else {
			log.warn("更新设备运行状态、时长失败！");
		}

	}


}
