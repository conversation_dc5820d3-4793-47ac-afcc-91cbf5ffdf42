<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snszyk.zbusiness.basic.mapper.MonitorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="monitorResultMap" type="com.snszyk.zbusiness.basic.entity.Monitor">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="measure_direction" property="measureDirection"/>
        <result column="path" property="path"/>
        <result column="path_name" property="pathName"/>
        <result column="equipment_type" property="equipmentType"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="monitorDTOResultMap" type="com.snszyk.zbusiness.basic.dto.MonitorDTO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="path" property="path"/>
        <result column="path_name" property="pathName"/>
        <result column="image" property="image"/>
        <result column="measure_direction" property="measureDirection"/>
        <result column="equipment_type" property="equipmentType"/>
    </resultMap>

    <resultMap id="monitorThresholdDTOResultMap" type="com.snszyk.zbusiness.basic.dto.MonitorThresholdDTO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="equipment_id" property="equipmentId"/>
        <result column="name" property="name"/>
        <result column="quota_code" property="quotaCode"/>
        <result column="first_threshold" property="firstThreshold"/>
        <result column="second_threshold" property="secondThreshold"/>
        <result column="third_threshold" property="thirdThreshold"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
    </resultMap>

    <update id="createSensorDataTable">
        CREATE TABLE IF NOT EXISTS ${tableName} (
            `id` bigint NOT NULL COMMENT '主键',
            `wave_id` bigint DEFAULT NULL COMMENT '波形id',
            `sensor_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '传感器实例编码',
            `sample_data_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '采样数据类型',
            `axis_direction` int DEFAULT NULL COMMENT '轴方向',
            `measure_direction` int DEFAULT NULL COMMENT '测量方向',
            `sampling_freq` decimal(10,3) DEFAULT NULL COMMENT '采样频率',
            `sampling_points` int DEFAULT NULL COMMENT '采样点数',
            `value` decimal(16,10) DEFAULT NULL COMMENT '采集值',
            `time_domain_waveform` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '时域波形',
            `rms_value` decimal(16,10) DEFAULT NULL COMMENT '有效值',
            `peak_value` decimal(16,10) DEFAULT NULL COMMENT '峰值',
            `peak_peak_value` decimal(16,10) DEFAULT NULL COMMENT '峰峰值',
            `clearance_value` decimal(16,10) DEFAULT NULL COMMENT '裕度',
            `skewness_value` decimal(16,10) DEFAULT NULL COMMENT '歪度',
            `kurtosis_value` decimal(16,10) DEFAULT NULL COMMENT '峭度',
            `energy_value` decimal(16,10) DEFAULT NULL COMMENT '能量值',
            `origin_time` datetime DEFAULT NULL COMMENT '采集时间',
            `invalid` int DEFAULT '0' COMMENT '是否异常数据：0-正常；1-异常',
            `invalid_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '异常原因',
            `is_marked` int DEFAULT '0' COMMENT '是否已标记：0-否，1-是。',
            `alarm_level` int DEFAULT '0' COMMENT '报警等级',
            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
            PRIMARY KEY (`id`) USING BTREE,
            KEY `idx_wave_id` (`wave_id`) USING BTREE,
            KEY `idx_origin_time` (`origin_time`) USING BTREE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='传感器数据表(部位id)';
    </update>

    <select id="page" resultMap="monitorDTOResultMap">
        SELECT monitor.*, tree.path, tree.path_name FROM eolm_monitor monitor left join sidas_basic_tree tree on tree.id = monitor.id
        WHERE monitor.is_deleted = 0
        <if test="monitor.parentId!=null">
            and tree.path like concat('%',#{monitor.parentId},'%')
        </if>
        <if test="monitor.equipmentId!=null">
            and monitor.equipment_id = #{monitor.equipmentId}
        </if>
        <if test="monitor.keywords!=null and monitor.keywords != ''">
            and (monitor.code like concat('%',#{monitor.keywords},'%') or monitor.`name` like
            concat('%',#{monitor.keywords},'%'))
        </if>
        ORDER BY monitor.create_time DESC
    </select>

    <select id="monitorPage" resultMap="monitorDTOResultMap">
        select * from eolm_monitor
        where is_deleted = 0
          and id in (select DISTINCT monitor_id from basic_sensor_instance where is_deleted = 0 and monitor_id is not null )
        <if test="monitor.parentId!=null">
            and path like concat('%',#{monitor.parentId},'%')
        </if>
        <if test="monitor.equipmentId!=null">
            and equipment_id = #{monitor.equipmentId}
        </if>
        <if test="monitor.keywords!=null and monitor.keywords != ''">
            and (code like concat('%',#{monitor.keywords},'%') or `name` like
            concat('%',#{monitor.keywords},'%'))
        </if>
        ORDER BY create_time DESC
    </select>

    <delete id="removeAll">
        delete from eolm_monitor where equipment_id=#{equipmentId}
    </delete>

    <delete id="removeByIds">
        delete from eolm_monitor where id in
        <foreach collection="list" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
    </delete>

    <select id="queryBySensorCode" resultMap="monitorThresholdDTOResultMap">
        SELECT
            m.id,
            m.tenant_id,
            m.`name`,
            m.equipment_id,
            m.path,
            m.path_name,
            m.create_user,
            m.create_dept,
            m.update_user,
            t.quota_code,
            t.first_threshold,
            t.second_threshold,
            t.third_threshold
        FROM
            `eolm_monitor` m
        LEFT JOIN `eolm_common_quota_threshold` t ON t.monitor_id = m.id
        LEFT JOIN `basic_sensor_instance` si ON si.monitor_id = m.id
        WHERE
            m.is_deleted = 0 and si.is_deleted = 0
        AND si.code = #{sensorCode}
    </select>

    <select id="detectMonitors" resultMap="monitorDTOResultMap">
        SELECT m.id, m.equipment_id, s.code
        FROM `eolm_monitor` m
        LEFT JOIN `basic_sensor_instance` s ON s.monitor_id = m.id
        WHERE m.is_deleted = 0 and s.is_deleted = 0 AND s.code IS NOT NULL
        <if test="path!=null and path != ''">
            and m.`path` like concat('%',#{path},'%')
        </if>
    </select>

    <select id="getMaxSort" resultType="java.lang.Integer">
        SELECT max(sort)
        FROM eolm_monitor
    </select>

    <select id="modelMonitorPage" resultMap="monitorDTOResultMap">
        SELECT monitor.* FROM eolm_monitor monitor
        LEFT JOIN eolm_equipment equipment ON equipment.id = monitor.equipment_id
        <if test="monitor.applyStatus==1">
            RIGHT JOIN eolm_monitor_model model ON model.monitor_id = monitor.id
        </if>
        WHERE monitor.is_deleted = 0 and monitor.equipment_type = #{monitor.equipmentType}
        <if test="monitor.applyStatus==1">
            and model.model_id = #{monitor.modelId}
        </if>
        <if test="monitor.parentId!=null">
            and monitor.path like concat('%',#{monitor.parentId},'%')
        </if>
        <if test="monitor.keywords!=null and monitor.keywords != ''">
            and (monitor.code like concat('%',#{monitor.keywords},'%') or monitor.`name` like
            concat('%',#{monitor.keywords},'%'))
        </if>
        <if test="monitor.applyStatus==0">
            AND monitor.id NOT IN ( SELECT monitor_id FROM eolm_monitor_model WHERE model_id=#{monitor.modelId})
        </if>
        <if test="monitor.applyPower==1">
            AND equipment.power > 0 AND equipment.power <![CDATA[ <= ]]> 15
        </if>
        <if test="monitor.applyPower==2">
            AND equipment.power > 15 AND equipment.power <![CDATA[ <= ]]> 75
        </if>
        <if test="monitor.applyPower==3">
            AND equipment.power > 75
        </if>
        ORDER BY monitor.create_time DESC
    </select>

    <select id="waveList" resultType="com.snszyk.zbusiness.basic.dto.WaveDTO">
        SELECT w.*, #{monitorId} as parent_id, s.sampling_freq*1000 as sampling_freq, s.sampling_points,
               t.initial_freq, s.sampling_freq*1000/2.56 as cutoff_freq, s.vibration_type,
               3 AS node_category, 0 AS "has_children"
        FROM sidas_wave w
        LEFT JOIN basic_sensor_instance_param s ON w.sensor_instance_param_id = s.id
        LEFT JOIN basic_sensor_instance i ON s.instance_id = i.id
        LEFT JOIN basic_sensor_type t ON i.type_id = t.id
        WHERE w.monitor_id = #{monitorId}
        <if test="tenantId!=null and tenantId!=''">
            and i.tenant_id = #{tenantId}
        </if>
    </select>

</mapper>
