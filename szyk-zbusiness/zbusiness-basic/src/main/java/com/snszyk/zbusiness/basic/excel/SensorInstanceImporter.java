package com.snszyk.zbusiness.basic.excel;

import com.snszyk.core.excel.support.ExcelImporter;
import com.snszyk.zbusiness.basic.dto.SensorInstanceBindExcelDTO;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import lombok.RequiredArgsConstructor;

import java.util.List;


/**
 * 传感器实例导入类
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SensorInstanceImporter implements ExcelImporter<SensorInstanceBindExcelDTO> {

	private final ISensorInstanceService service;

	/**
	 * 是否覆盖 - 暂时忽略
 	 */
	private final Boolean isCovered;

	@Override
	public void save(List<SensorInstanceBindExcelDTO> data) {
		service.importSensorInstance(data, isCovered);
	}
}
