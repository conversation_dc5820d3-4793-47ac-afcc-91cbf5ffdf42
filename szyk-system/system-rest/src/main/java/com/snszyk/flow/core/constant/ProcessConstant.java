/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.flow.core.constant;

/**
 * 流程常量.
 *
 * <AUTHOR>
 */
public interface ProcessConstant {

	/**
	 * 请假流程标识
	 */
	String LEAVE_KEY = "Leave";

	/**
	 * 报销流程标识
	 */
	String EXPENSE_KEY = "Expense";

	/**
	 * 同意标识
	 */
	String PASS_KEY = "pass";

	/**
	 * 同意代号
	 */
	String PASS_ALIAS = "ok";

	/**
	 * 同意默认批复
	 */
	String PASS_COMMENT = "同意";

	/**
	 * 驳回默认批复
	 */
	String NOT_PASS_COMMENT = "驳回";

	/**
	 * 创建人变量名
	 */
	String TASK_VARIABLE_CREATE_USER = "createUser";

}
