package com.snszyk.zbusiness.basic.rabbit.exec;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.config.SzykAttachConfig;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.common.utils.CollectSensorDataInfluxdb;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.dto.SensorInstanceDTO;
import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.enums.MeasureDirectionEnum;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.rabbit.handler.CommandExec;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.ISensorInstanceService;
import com.snszyk.zbusiness.basic.service.IWaveService;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * 处理采样数据抽象方法
 *
 * <AUTHOR>
 * @date 2024/04/30 15:57
 **/
@Slf4j
public abstract class AbstractSensorDataExecutor implements CommandExec {

	@Resource
	private ISensorInstanceService sensorInstanceService;
	@Resource
	private IWaveService waveService;
	@Resource
	private InfluxdbTools influxdbTools;

	@Resource
	private CollectSensorDataInfluxdb collectSensorDataInfluxdb;
	@Resource
	private SzykAttachConfig szykAttachConfig;

	@Override
	public synchronized boolean save(MessageBean message) {
		try {
			return saveData(message);
		} catch (Exception e) {
			log.error("MQ接收数据，保存数据异常:{}", e.getMessage());
			collectSensorDataInfluxdb.insertMessage((JSONObject) JSONObject.toJSON(message),
				Calendar.getInstance().getTimeInMillis());
			return false;
		}

	}

	private boolean saveData(MessageBean message) {
		log.info("【开始】VibrateSensorDataExecutor - save() - message = {}, wave.length = {}", message,
			Func.isNotEmpty(message.getWave()) ? message.getWave().length() : 0);
		// 1、根据id、type、axis（需要先将axis转为真实的测量方向）查找waveId、monitorId等
		SensorInstanceDTO sensorInstance = sensorInstanceService.detailByCode(message.getId());
		log.info("当前传感器信息：{}", sensorInstance);
		if (sensorInstance == null) {
			log.warn("暂未查到传感器实例，sensorCode = {}", message.getId());
			return false;
		}
		sensorInstance
			.setCategoryName(DictBizCache.getValue(DictBizEnum.SENSOR_CATEGORY, sensorInstance.getCategory()));
		message.setSensorInstanceId(sensorInstance.getId());
		// 获取本条数据的测量方向
		Integer measureDirection = getMeasureDirection(sensorInstance.getAxisCount(),
			sensorInstance.getInstallDirection(), message.getAxis(), message.getType());
		Wave wave = waveService.getOne(Wrappers.<Wave>lambdaQuery()
			.eq(Wave::getSensorCode, message.getId())
			.eq(Wave::getSampleDataType, message.getType())
			.eq(Wave::getUnbind, 0)
			.eq(Func.isNotEmpty(measureDirection), Wave::getMeasureDirection, measureDirection));
		if (wave == null) {
			log.warn("暂未查询到波形配置，sensorCode = {}, sampleDataType = {}, measureDirection = {}",
				message.getId(), message.getType(), measureDirection);
			return false;
		}
		message.setSensorInstanceParamId(wave.getSensorInstanceParamId());
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(message.getOriginTime());
		String fileUrl = String.format("%s/%s/%s/%s/%s/",
			EolmConstant.SaveFileUrl.DIAGNOSIS_WAVE,
			calendar.get(Calendar.YEAR),
			calendar.get(Calendar.MONTH),
			calendar.get(Calendar.DAY_OF_MONTH),
			wave.getId());
		// 3、存储数据
		SensorData sensorData = new SensorData()
			.setMonitorId(wave.getMonitorId())
			.setWaveId(wave.getId())
			.setHasWaveData(0)
			.setSensorInstance(JSONObject.toJSONString(sensorInstance))
			.setSensorCode(message.getId())
			.setInvalid(0)
			.setIsMarked(0)
			.setAlarmLevel(0)
			.setSampleDataType(message.getType())
			.setAxisDirection(message.getAxis())
			.setMeasureDirection(measureDirection)
			.setSamplingFreq(
				message.getSamplingFreq() != null ? BigDecimal.valueOf(message.getSamplingFreq()) : null)
			.setSamplingPoints(message.getSamplingPoints())
			.setRmsValue(message.getEffective() != null ? BigDecimal.valueOf(message.getEffective()) : null)
			.setPeakValue(message.getPeak() != null ? BigDecimal.valueOf(message.getPeak()) : null)
			.setPeakPeakValue(message.getPeakPeak() != null ? BigDecimal.valueOf(message.getPeakPeak()) : null)
			.setClearanceValue(message.getClearance() != null ? BigDecimal.valueOf(message.getClearance()) : null)
			.setSkewnessValue(message.getSkewness() != null ? BigDecimal.valueOf(message.getSkewness()) : null)
			.setKurtosisValue(message.getKurtosis() != null ? BigDecimal.valueOf(message.getKurtosis()) : null)
			.setOriginTime(new Date(message.getOriginTime()));

		if (message.getValue() != null) {
			try {
				// 设置有效值
				sensorData.setValue(new BigDecimal(message.getValue()));
			} catch (Exception e) {
				log.error("数据异常，value = {}", message.getValue());
				log.error("信息={}", JSONObject.toJSONString(message));
				e.printStackTrace();
			}
		}

		if (Func.isNotEmpty(message.getWave())) {
			sensorData.setWaveformUrl(String.format("%s%s", fileUrl, message.getOriginTime()));
			fileUrl = String.format("%s%s", szykAttachConfig.szykAttachProperties().getPath(), fileUrl);
			sensorData.setHasWaveData(1);
			sensorData = createSensorData(message, sensorData, wave);
			// 保存时域波形文件
			writeFile(fileUrl, sensorData, message.getWave());
		} else {
			sensorData = createSensorData(message, sensorData, wave);
		}
		if (sensorData.getSampleDataType() == null) {
			log.info("若没有数据类型，设置数据类型为{}", message.getCommand());
			sensorData.setSampleDataType(message.getCommand());
		}
		log.debug("当前采集时间:{}", sensorData.getOriginTime());
		JSONObject data = (JSONObject) JSONObject.toJSON(sensorData);
		log.debug("执行插入数据");
		influxdbTools.insert(wave.getMonitorId().toString(), wave.getId().toString(), data,
			sensorData.getOriginTime().getTime());

		if (Func.isNotEmpty(message.getWave())) {
			log.info("开始执行插入波形数据----------");
			String table = String.format("%s_%s", wave.getId().toString(), "1");
			influxdbTools.insert(wave.getMonitorId().toString(), table, data, sensorData.getOriginTime().getTime());
		}

		// 4、更新message中的monitorId、sensorDataId
		message.setMonitorId(wave.getMonitorId());
		message.setSensorDataId(sensorData.getId());
		message.setHaltLine(wave.getHaltLine());
		SensorDataVO sensorDataVO = BeanUtil.copy(sensorData, SensorDataVO.class);
		sensorDataVO.setMonitorId(wave.getMonitorId());
		// 如果是应力波传感器的加速度、速度、位移，则有number
		sensorDataVO.setNumber(wave.getNumber());
		message.setSensorDataVO(sensorDataVO);
		data.put("originTime", sensorData.getOriginTime().getTime());
		data.put("category", sensorInstance.getCategory());
		data.put("categoryName", sensorInstance.getCategoryName());
		collectSensorDataInfluxdb.insert(data, Calendar.getInstance().getTimeInMillis());
		return true;
	}

	/**
	 * 保存时域波形文件
	 *
	 * @param path
	 * @param data
	 * @param wave
	 * @return void
	 */
	private void writeFile(String path, SensorData data, String wave) {
		File file = new File(path);
		if (!file.exists()) {
			boolean flag = file.mkdirs();
			log.info("创建文件{}", flag);
		}
		file = new File(String.format("%s/%s", path, data.getOriginTime().getTime()));
		try (FileWriter writer = new FileWriter(file)) {
			writer.write(wave);
			writer.flush();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 创建操作对象
	 *
	 * @param message
	 * @param sensorData
	 * @param wave
	 * @return
	 */
	protected abstract SensorData createSensorData(MessageBean message, SensorData sensorData, Wave wave);

	/**
	 * 获取测量方向
	 *
	 * @param axisCount
	 * @param installDirection
	 * @param axialDirection
	 * @param sampleDataType
	 * @return
	 */
	private Integer getMeasureDirection(Integer axisCount, Integer installDirection, Integer axialDirection,
										String sampleDataType) {
		if (axisCount == 0) {
			// 纯非振动传感器（温度、电压、电量传感器等）测量方向为null
			return null;
		} else if (axisCount == 1) {
			// 单轴传感器的安装方向即是实际测量方向
			return installDirection;
		} else if (axisCount == 3) {
			// 三轴温度无测量方向
			if (SampledDataTypeEnum.EQUIPMENT_TEMPERATURE.getCode().equals(sampleDataType)) {
				return null;
			}
			// 三轴传感器根据安装方向和轴方向获取实际测量方向 - 加速度、速度、位移
			switch (Objects.requireNonNull(MeasureDirectionEnum.getByCode(installDirection))) {
				case HORIZONTAL:
					// 3轴传感器水平安装：x -> 垂直； y -> 轴向；z -> 水平
					if (MeasureDirectionEnum.HORIZONTAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.VERTICAL.getCode();
					} else if (MeasureDirectionEnum.VERTICAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.AXIAL.getCode();
					} else {
						return MeasureDirectionEnum.HORIZONTAL.getCode();
					}
				case VERTICAL:
					// 3轴传感器垂直安装：x -> 水平； y -> 轴向；z -> 垂直
					if (MeasureDirectionEnum.HORIZONTAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.HORIZONTAL.getCode();
					} else if (MeasureDirectionEnum.VERTICAL.getCode().equals(axialDirection)) {
						return MeasureDirectionEnum.AXIAL.getCode();
					} else {
						return MeasureDirectionEnum.VERTICAL.getCode();
					}
				case AXIAL:
					// 3轴传感器轴向安装：x -> 水平； y -> 垂直；z -> 轴向
					return axialDirection;
				default:
					log.error("传感器安装方向不正确");
					return axialDirection;
				// throw new ServiceException("传感器实例安装方向不正确！");
			}
		} else {
			log.warn("非法的传感器实例，axisCount = {}", axisCount);
			return null;
		}
	}

}
