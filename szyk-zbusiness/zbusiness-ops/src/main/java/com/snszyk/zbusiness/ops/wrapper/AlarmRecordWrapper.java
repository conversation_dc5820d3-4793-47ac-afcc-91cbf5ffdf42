/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.ops.entity.AlarmRecord;
import com.snszyk.zbusiness.ops.vo.AlarmRecordVO;

import java.util.Objects;

/**
 * 报警记录表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
public class AlarmRecordWrapper extends BaseEntityWrapper<AlarmRecord, AlarmRecordVO> {

	public static AlarmRecordWrapper build() {
		return new AlarmRecordWrapper();
 	}

	@Override
	public AlarmRecordVO entityVO(AlarmRecord alarmRecord) {
		AlarmRecordVO alarmRecordVO = Objects.requireNonNull(BeanUtil.copy(alarmRecord, AlarmRecordVO.class));

		//User createUser = UserCache.getUser(alarm.getCreateUser());
		//User updateUser = UserCache.getUser(alarm.getUpdateUser());
		//alarmVO.setCreateUserName(createUser.getName());
		//alarmVO.setUpdateUserName(updateUser.getName());

		return alarmRecordVO;
	}

}
