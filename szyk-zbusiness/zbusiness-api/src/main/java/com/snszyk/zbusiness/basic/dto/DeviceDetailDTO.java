package com.snszyk.zbusiness.basic.dto;

import com.snszyk.resource.entity.Attach;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 设备详情
 * @ClassName: DeviceDetail
 * @author: wangmh
 * @create: 2022-12-20 16:13
 **/
@Data
public class DeviceDetailDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "设备id")
	private Long id;

	@ApiModelProperty(value = "设备编码")
	private String code;

	@ApiModelProperty(value = "设备名称")
	private String name;

	@ApiModelProperty(value = "设备型号")
	private String model;

	@ApiModelProperty(value = "设备图片")
	private Attach attach;

	@ApiModelProperty(value = "设备状态0:正常 1：一级报警 ....")
	private Integer alarmLevel;

	@ApiModelProperty(value = "运行状态：0-停机；1-运行中。")
	private Integer isRunning;

	@ApiModelProperty(value = "运行时长(ms)")
	private BigDecimal runningTime;

	@ApiModelProperty(value = "运行率")
	private BigDecimal runningPercentage;

	@ApiModelProperty(value = "测点")
	private List<DeviceMonitorDTO> monitorList;

}
