package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.SensorModelData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 传感器机理模型数据DTO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SensorModelDataDTO extends SensorModelData {

	private static final long serialVersionUID = 1L;

	/**
	 * 指标名称
	 */
	@ApiModelProperty(value = "指标名称")
	private String modelCodeName;

	/**
	 * 测量值（带单位）
	 */
	@ApiModelProperty(value = "测量值（带单位）")
	private BigDecimal value;

	/**
	 * 测量值单位
	 */
	@ApiModelProperty(value = "测量值单位")
	private String unit;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;

	/**
	 * 截止频率（Hz）
	 */
	@ApiModelProperty(value = "截止频率（Hz）")
	private BigDecimal cutoffFreq;

	/**
	 * 测量时间
	 */
	@ApiModelProperty(value = "测量时间")
	private BigDecimal samplingTime;

	/**
	 * 采样频率（KHz）
	 */
	@ApiModelProperty(value = "采样频率（KHz）")
	private BigDecimal samplingFreq;

	/**
	 * 采样点数
	 */
	@ApiModelProperty(value = "采样点数")
	private Integer samplingPoints;

	/**
	 * 报警门限类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "报警门限类型")
	private Integer alarmType;

	/**
	 * 报警门限类型名称
	 */
	@ApiModelProperty(value = "报警门限类型名称")
	private String alarmTypeName;

	/**
	 * 报警门限值
	 */
	@ApiModelProperty(value = "报警门限值")
	private List<BigDecimal> alarmThreshold;

	public SensorModelDataDTO(){
		super();
	}

	public SensorModelDataDTO(String modelCode){
		super();
		this.setModelCode(modelCode);
	}

}
