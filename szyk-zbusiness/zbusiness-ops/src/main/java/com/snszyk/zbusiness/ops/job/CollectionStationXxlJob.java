//package com.snszyk.zbusiness.ops.job;
//
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.snszyk.common.constant.EolmConstant;
//import com.snszyk.core.redis.cache.SzykRedis;
//import com.snszyk.core.tool.api.R;
//import com.snszyk.core.tool.utils.CollectionUtil;
//import com.snszyk.core.tool.utils.Func;
//import com.snszyk.core.tool.utils.StringPool;
//import com.snszyk.message.enums.MessageBizTypeEnum;
//import com.snszyk.message.enums.MessageTypeEnum;
//import com.snszyk.message.enums.ReceiverTypeEnum;
//import com.snszyk.message.enums.YesNoEnum;
//import com.snszyk.message.vo.DingTalkMessageVo;
//import com.snszyk.message.vo.MessageVo;
//import com.snszyk.message.vo.ReceiverInfoVo;
//import com.snszyk.system.entity.Tenant;
//import com.snszyk.zbusiness.basic.dto.CollectionStationChannelDTO;
//import com.snszyk.zbusiness.basic.entity.CollectionStation;
//import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
//import com.snszyk.zbusiness.basic.service.ICollectionStationChannelService;
//import com.snszyk.zbusiness.basic.service.ICollectionStationService;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * 采集站定时任务
// * <AUTHOR>
// */
//@Slf4j
//@Component
//@AllArgsConstructor
//public class CollectionStationXxlJob {
//
//	private static final Logger logger = LoggerFactory.getLogger(CollectionStationXxlJob.class);
//
//	private final ICollectionStationService stationService;
//	private final ICollectionStationChannelService channelService;
//	private final ISysClient sysClient;
//	private final IMessageClient messageClient;
//	private final IUserSearchClient userSearchClient;
//	private final SzykRedis szykRedis;
//	private final RabbitTemplate rabbitTemplate;
//
//	/**
//	 * 采集站 & 通道在线状态监控定时任务
//	 * @param param 参数
//	 * @return
//	 */
//	@XxlJob("collectionStationOnlineJobHandler")
//	public ReturnT<String> collectionStationOnlineJobHandler(String param) {
//		logger.info("采集站在线状态定时任务 - 【开始】");
//		//查询所有采集站id
//		List<CollectionStation> stationList = stationService.list(new QueryWrapper<CollectionStation>().lambda()
//			.eq(CollectionStation::getIsDeleted, 0));
//		//更新采集站 & 通道 在线状态
//		if (CollectionUtil.isNotEmpty(stationList)) {
//			stationList.forEach(station -> {
//				//查询所有的通道id - 采集站通道在线状态逻辑更新
//				List<CollectionStationChannelDTO> channelDTOList = channelService.selectChannelList(station.getId());
//				if (CollectionUtil.isNotEmpty(channelDTOList)) {
//					int onlineChannelCount = 0;
//					//更新通道的在线状态
//					for (CollectionStationChannelDTO dto : channelDTOList) {
//						if (Func.isNotEmpty(dto.getSensorCode()) && Func.isNotEmpty(dto.getOnlineSensorInstanceParamId())) {
//							dto.setOnline(szykRedis.get(dto.getSensorCode() + StringPool.COLON + dto.getOnlineSensorInstanceParamId()
//								+ StringPool.COLON + SampledDataTypeEnum.SENSOR_ONLINE.getCode()) != null ? 1 : 0);
//							channelService.updateById(dto);
//							//统计在线通道数
//							if (dto.getOnline() == 1) {
//								onlineChannelCount++;
//							}
//						}
//					}
//					int oldStatus = station.getOnline();
//					int newStatus = onlineChannelCount > 0 ? 1 : 0;
//					if (oldStatus == 1 && newStatus == 0) {
//						//发送采集站离线待办消息
//						MessageVo messageVo = new MessageVo();
//						messageVo.setAppKey("SiDAs");
//						messageVo.setTitle(MessageBizTypeEnum.COLLECTION_STATION_OFFLINE.getMessage() + "通知");
//						//content改为json字符串
//						messageVo.setContent(JSONObject.toJSONString(station));
//						messageVo.setType(MessageTypeEnum.WORK_TODO.getCode());
//						messageVo.setBizType(MessageBizTypeEnum.COLLECTION_STATION_OFFLINE.getCode());
//						messageVo.setBizId(String.valueOf(station.getId()));
//						messageVo.setSender("SiDAs");
//						messageVo.setIsImmediate(YesNoEnum.YES.getCode());
//						messageVo.setReceiverType(ReceiverTypeEnum.USER.getCode());
//						ReceiverInfoVo receiverInfoVo = new ReceiverInfoVo();
//						R<List<User>> allUserResult = userSearchClient.listAllUser(station.getTenantId());
//						if (allUserResult.isSuccess() && CollectionUtil.isNotEmpty(allUserResult.getData())) {
//							List<ReceiverInfoVo.UserVo> userVoList = allUserResult.getData().stream().map(user -> {
//								ReceiverInfoVo.UserVo userVo = new ReceiverInfoVo.UserVo();
//								userVo.setId(user.getId());
//								userVo.setRealName(user.getRealName());
//								return userVo;
//							}).collect(Collectors.toList());
//							receiverInfoVo.setUserList(userVoList);
//						} else {
//							logger.warn("发送采集站离线待办消息失败：获取用户列表失败！code = {}, msg = {}", allUserResult.getCode(), allUserResult.getMsg());
//						}
//						messageVo.setReceiverInfoVo(receiverInfoVo);
//						messageClient.pushMessage(messageVo);
//						// 发送钉钉消息
//						R<Tenant> tenantInfo = sysClient.getTenant(station.getTenantId());
//						log.info(MessageBizTypeEnum.COLLECTION_STATION_OFFLINE.getMessage() + "-发送钉钉消息：==================={}", station.getCode());
//						DingTalkMessageVo dingTalkMessage = new DingTalkMessageVo(tenantInfo.getData().getTenantId(),
//							tenantInfo.getData().getTenantName(), station.getCode(),
//							MessageBizTypeEnum.COLLECTION_STATION_OFFLINE.getCode(), JSONUtil.toJsonStr(station));
//						rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_DINGTALK_MESSAGE,
//							EolmConstant.Rabbit.ROUTING_DINGTALK_MESSAGE, dingTalkMessage);
//					}
//					//更新采集站在线状态
//					station.setOnline(newStatus);
//					stationService.updateById(station);
//				}
//			});
//		}
//		logger.info("采集站在线状态定时任务 - 【结束】");
//		return ReturnT.SUCCESS;
//	}
//
//	public static void main(String[] args) {
//		CollectionStation station = new CollectionStation();
//		station.setId(1762384020703727617L);
//		station.setTenantId("276851");
//		station.setCode("CJ202402270005");
//		station.setName("3楼采集站");
//		station.setOnline(0);
//		station.setIpAddress("*******");
//		station.setChannelCount(10);
//		DingTalkMessageVo messageVo = new DingTalkMessageVo("276851", "XXX洗煤厂",
//			"CJ202402270005", MessageBizTypeEnum.COLLECTION_STATION_OFFLINE.getCode(), JSONUtil.toJsonStr(station));
//		System.out.println(JSONUtil.toJsonStr(messageVo));
//	}
//
//}
