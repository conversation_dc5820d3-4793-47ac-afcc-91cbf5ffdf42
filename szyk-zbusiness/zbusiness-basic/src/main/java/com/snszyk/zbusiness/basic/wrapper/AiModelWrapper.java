/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.basic.entity.AiModel;
import com.snszyk.zbusiness.basic.vo.AiModelVO;

import java.util.Objects;

/**
 * AI模型表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
public class AiModelWrapper extends BaseEntityWrapper<AiModel, AiModelVO> {

	public static AiModelWrapper build() {
		return new AiModelWrapper();
 	}

	@Override
	public AiModelVO entityVO(AiModel aiModel) {
		AiModelVO aiModelVO = Objects.requireNonNull(BeanUtil.copy(aiModel, AiModelVO.class));

		//User createUser = UserCache.getUser(aiModel.getCreateUser());
		//User updateUser = UserCache.getUser(aiModel.getUpdateUser());
		//aiModelVO.setCreateUserName(createUser.getName());
		//aiModelVO.setUpdateUserName(updateUser.getName());

		return aiModelVO;
	}

}
