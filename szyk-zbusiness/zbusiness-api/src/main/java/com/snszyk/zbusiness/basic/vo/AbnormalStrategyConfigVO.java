package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备异常策略配置
 *
 * <AUTHOR>
 * @date 2024-05-21 20:07
 */
@Data
@ApiModel(value = "AbnormalStrategyConfigVO", description = "AbnormalStrategyConfigVO")
public class AbnormalStrategyConfigVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 连续策略配置
	 */
	@ApiModelProperty(value = "连续策略配置")
	private ContinuousStrategyVO continuousStrategy;
	/**
	 * 非连续策略配置
	 */
	@ApiModelProperty(value = "非连续策略配置")
	private DiscontinuousStrategyVO discontinuousStrategy;
	/**
	 * 趋势预警配置
	 */
	@ApiModelProperty(value = "趋势预警配置")
	private TrendStrategyVO trendStrategy;

}
