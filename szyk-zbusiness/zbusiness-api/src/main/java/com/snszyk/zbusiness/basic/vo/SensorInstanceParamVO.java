/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.SensorInstanceParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 传感器实例参数表 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SensorInstanceParamVO对象", description = "传感器实例参数表")
public class SensorInstanceParamVO extends SensorInstanceParam {

	private static final long serialVersionUID = 1L;

	/**
	 * 测量方向
	 */
	@ApiModelProperty(value = "测量方向")
	private Integer measureDirection;

	/**
	 * 传感器实力参数子集
	 */
	@ApiModelProperty(value = "传感器实例参数子集")
	private List<SensorInstanceParamVO> children;

	/**
	 * 波形id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "波形id")
	private Long waveId;

	public SensorInstanceParamVO() {

	}

	public SensorInstanceParamVO(String feature, String paramName) {
		this.setParamName(paramName);
		this.setFeature(feature);
	}

}
