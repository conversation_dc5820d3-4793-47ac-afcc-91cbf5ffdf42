/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.AiModelDTO;
import com.snszyk.zbusiness.basic.dto.EquipmentAiDTO;
import com.snszyk.zbusiness.basic.entity.AiModel;
import com.snszyk.zbusiness.basic.service.IAiModelService;
import com.snszyk.zbusiness.basic.service.IEquipmentAiService;
import com.snszyk.zbusiness.basic.vo.AiModelVO;
import com.snszyk.zbusiness.basic.vo.EquipmentAiVO;
import com.snszyk.zbusiness.basic.wrapper.AiModelWrapper;
import com.snszyk.system.vo.DelResultVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;

/**
 * AI模型表 控制器
 *
 * <AUTHOR>
 * @since 2023-07-06
 */
@RestController
@AllArgsConstructor
@RequestMapping("/aiModel")
@Api(value = "AI模型表", tags = "AI模型表接口")
public class AiModelController extends SzykController {

	private final IAiModelService aiModelService;
	private final IEquipmentAiService equipmentAiService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入aiModel")
	public R<AiModelVO> detail(AiModel aiModel) {
		AiModel detail = aiModelService.getOne(Condition.getQueryWrapper(aiModel));
		return R.data(AiModelWrapper.build().entityVO(detail));
	}

	/**
	 * 列表 AI模型表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入aiModel")
	public R<IPage<AiModelVO>> list(AiModelVO aiModel, Query query) {
		IPage<AiModel> pages = aiModelService.page(Condition.getPage(query), Condition.getQueryWrapper(aiModel));
		return R.data(AiModelWrapper.build().pageVO(pages));
	}

	/**
	 * 自定义分页 AI模型表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "name", value = "模型名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "启停用", paramType = "query", dataType = "Integer"),
		@ApiImplicitParam(name = "applyStatus", value = "应用状态", paramType = "query", dataType = "Integer")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入aiModel")
	public R<IPage<AiModelDTO>> page(@ApiIgnore AiModelVO aiModel, Query query) {
		IPage<AiModelDTO> pages = aiModelService.page(Condition.getPage(query), aiModel);
		return R.data(pages);
	}

	/**
	 * 新增 AI模型表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入aiModel")
	public R save(@Valid @RequestBody AiModel aiModel) {
		return R.status(aiModelService.save(aiModel));
	}

	/**
	 * 修改 AI模型表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入aiModel")
	public R update(@Valid @RequestBody AiModel aiModel) {
		return R.status(aiModelService.updateById(aiModel));
	}


	/**
	 * 新增或修改 AI模型表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入aiModel")
	public R submit(@Valid @RequestBody AiModelVO aiModel) {
		return R.status(aiModelService.submit(aiModel));
	}

	/**
	 * 删除 AI模型表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(aiModelService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 校验并删除 AI模型表
	 */
	@PostMapping("/check-remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "校验并删除", notes = "传入ids")
	public R<DelResultVO> checkAndRemove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		DelResultVO delResultVO = aiModelService.checkAndRemoveAiModel(Func.toLongList(ids));
		R<DelResultVO> result = new R<>();
		result.setCode(ResultCode.SUCCESS.getCode());
		result.setData(delResultVO);
		result.setSuccess(delResultVO.getFailureNumber() == 0);
		return result;
	}

	/**
	 * 启停用 AI模型表
	 */
	@PostMapping("/updateStatus")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "启停用", notes = "传入id")
	public R updateStatus(@ApiParam(value = "主键", required = true) @RequestBody AiModelVO aiModel) {
		return R.status(aiModelService.update(Wrappers.<AiModel>update().lambda()
			.eq(AiModel::getId, aiModel.getId()).set(AiModel::getStatus, aiModel.getStatus())));
	}

	/**
	 * 门户-AI配置分页 AI模型表
	 */
	@GetMapping("/equipmentAiPage")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "parentId", value = "树结构id", required = true, paramType = "query", dataType = "Long")
	})
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "门户-AI配置分页", notes = "传入equipmentAi")
	public R<IPage<EquipmentAiDTO>> equipmentAiPage(@ApiIgnore EquipmentAiVO equipmentAi, Query query) {
		IPage<EquipmentAiDTO> pages = equipmentAiService.equipmentAiPage(Condition.getPage(query), equipmentAi);
		return R.data(pages);
	}

	/**
	 * 模型应用
	 * @return
	 */
	@PostMapping("/apply")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "模型应用", notes = "传入aiModel")
	public R apply(@ApiParam(value = "主键", required = true) @RequestBody AiModelVO aiModel) {
		return R.status(aiModelService.apply(aiModel.getId(), aiModel.getEquipmentId()));
	}

}
