package com.snszyk.message.vo;

import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.message.enums.IntervalUnitEnum;
import com.snszyk.message.enums.MessageBizTypeEnum;
import com.snszyk.message.enums.SendStrategyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.Objects;

/**
 * 消息设置视图对象
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "消息设置视图对象", description = "消息设置视图对象")
public class MessageSettingSaveOrUpdateVO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 租户id
	 */
	@NotBlank
	@ApiModelProperty(value = "租户id", hidden = true)
	private String tenantId;

	/**
	 * 业务类型
	 */
	@NotBlank(message = "业务类型不能为空")
	@ApiModelProperty(value = "业务类型：DAILY_ABNORMAL-每日异常；EQUIPMENT_ABNORMAL-设备异常；REPORT_MANAGEMENT-报告管理；SENSOR_ABNORMAL-传感器异常", required = true)
	private String bizType;

	/**
	 * 发送策略
	 */
	@NotNull(message = "发送策略不能为空")
	@ApiModelProperty(value = "发送策略", required = true)
	private SendStrategyEnum sendStrategyEnum;

	/**
	 * 固定发送时间
	 */
	@ApiModelProperty(value = "固定发送时间，只支持整点或半点（如：09:00、09:30），HH:mm:ss格式")
	private LocalTime fixedSendTime;

	/**
	 * 检查间隔值
	 */
	@ApiModelProperty(value = "检查间隔值")
	private Integer checkInterval;
	/**
	 * 接收人信息
	 */
	@NotNull
	@ApiModelProperty(value = "接收人信息", required = true)
	private ReceiverInfoVo receiverInfoVo;
	/**
	 * 是否启用
	 */
	@ApiModelProperty(value = "是否启用")
	private Integer enabled;
	/**
	 * 发送渠道
	 */
	@NotBlank(message = "发送渠道不能为空")
	@ApiModelProperty(value = "发送渠道", required = true)
	private String channel;

	/**
	 * 接收人类型
	 */
	@ApiModelProperty(value = "接收人类型", hidden = true)
	private String receiverType;

	/**
	 * 间隔单位
	 */
	@ApiModelProperty(value = "间隔单位", hidden = true)
	private IntervalUnitEnum intervalUnitEnum;


	/**
	 * 校验固定发送时间
	 *
	 * @return
	 */
	@AssertTrue(message = "固定发送时间必须为整点或半点（如：09:00、09:30）")
	public boolean isValidFixedSendTime() {
		if (Objects.equals(SendStrategyEnum.FIXED_TIME, sendStrategyEnum)) {
			if (ObjectUtil.isEmpty(fixedSendTime)) {
				return false;
			}
			// 验证只能是整点或半点
			int minute = fixedSendTime.getMinute();
			return minute == 0 || minute == 30;
		}
		return true;
	}

	/**
	 * 校验检查间隔值
	 *
	 * @return
	 */
	@AssertTrue(message = "间隔发送策略时，检查间隔值不能为空且必须大于0")
	public boolean isValidCheckInterval() {
		if (Objects.equals(SendStrategyEnum.INTERVAL, sendStrategyEnum)) {
			return ObjectUtil.isNotEmpty(checkInterval) && checkInterval > 0;
		}
		return true;
	}

	/**
	 * 校验业务类型
	 *
	 * @return
	 */
	@AssertTrue(message = "不支持的业务类型")
	public boolean isValidBizType() {
		if (ObjectUtil.isEmpty(bizType)) {
			return false;
		}
		// 验证业务类型是否在枚举中
		for (MessageBizTypeEnum bizTypeEnum : MessageBizTypeEnum.values()) {
			if (bizTypeEnum.getCode().equals(bizType)) {
				return true;
			}
		}
		return false;
	}

	public void setCheckInterval(Integer checkInterval) {
		this.checkInterval = checkInterval;
		// 当检查间隔部位空时，默认设置间隔单位为小时
		if (ObjectUtil.isNotEmpty(checkInterval)) {
			this.intervalUnitEnum = IntervalUnitEnum.HOUR;
		}
	}
}
