package com.snszyk.zbusiness.basic.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备频发次数统计
 */
@Data
public class EquipmentFrequency implements Serializable {

	@ApiModelProperty(value = "设备名称")
	private String name;

	@ApiModelProperty(value = "总数")
	private String ct;

	@ApiModelProperty(value = "月份")
	private String mon;

	private List<String> mons = new ArrayList<>();
	private List<Integer> vals = new ArrayList<>();

	public void convert() {

		Map<String,Integer> data = new HashMap<>();
		String[] months = mon.split(",");
		String[] cts = ct.split(",");
		for(int i = 0; i<months.length; i++){
			data.put(months[i],Integer.parseInt(cts[i]));
		}
		LocalDate now = LocalDate.now();
		now = now.minusMonths(11);
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
		for(int i = 0; i<12; i++){
			String month = formatter.format(now);
			if(data.get(month) == null){
				mons.add(month);
				vals.add(0);
			} else {
				mons.add(month);
				vals.add(data.get(month));
			}
			now = now.plusMonths(1);
		}
	}

}
