/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 报警指标枚举类
 *
 * <AUTHOR>
 * @date 2024/01/12 15:56
 **/
@Getter
@AllArgsConstructor
public enum AlarmIndexEnum {

	/**
	 * 有效值
	 */
	EFFECTIVE_VALUE("MM000", "有效值"),

	/**
	 * 峰值
	 */
	PEAK_VALUE("MMN01", "峰值"),

	/**
	 * 峰峰值
	 */
	PEAK_PEAK_VALUE("MMN02", "峰峰值"),

	/**
	 * 裕度
	 */
	CLEARANCE_FACTOR("MMN03", "裕度"),

	/**
	 * 歪度
	 */
	SKEWNESS_VALUE("MMN04", "歪度"),

	/**
	 * 峭度
	 */
	KURTOSIS_VALUE("MMN05", "峭度"),

	/**
	 * 设备温度
	 */
	EQUIPMENT_TEMPERATURE("DTEMP", "温度值"),

	/**
	 * 暂不支持 - 防止switch空指针！
	 */
	UNSUPPORTED("9999", "暂不支持"),
	;

	final String code;
	final String name;

	public static AlarmIndexEnum getByCode(String code) {
		for (AlarmIndexEnum value : AlarmIndexEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return UNSUPPORTED;
	}

}
