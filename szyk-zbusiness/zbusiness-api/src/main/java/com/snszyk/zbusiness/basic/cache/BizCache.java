/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.cache;

import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.tool.utils.SpringUtil;
import com.snszyk.zbusiness.basic.entity.BasicTree;
import com.snszyk.zbusiness.basic.service.IBasicTreeService;

import static com.snszyk.core.cache.constant.CacheConstant.BIZ_CACHE;

/**
 * 业务表缓存工具类
 *
 * <AUTHOR>
 */
public class BizCache {
	public static final int DEVICE_CATEGORY = 0;
	public static final int EQUIPMENT_CATEGORY = 1;
	public static final int MONITOR_CATEGORY = 2;

	public static final String BASIC_TREE_ID = "tree:id:";

	private static IBasicTreeService basicTreeService;

	private static IBasicTreeService getBasicClient() {
		if (basicTreeService == null) {
			basicTreeService = SpringUtil.getBean(IBasicTreeService.class);
		}
		return basicTreeService;
	}

	/**
	 * 获取行政区划实体
	 *
	 * @param id 区划编号
	 * @return Param
	 */
	public static BasicTree getBasicNode(Long id) {
		return CacheUtil.get(BIZ_CACHE, BASIC_TREE_ID, id, () -> getBasicClient().getById(id));
	}

}
