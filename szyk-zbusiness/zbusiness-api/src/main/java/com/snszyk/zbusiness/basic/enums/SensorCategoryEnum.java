package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 传感器类型枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SensorCategoryEnum {

	/**
	 * 温振一体
	 */
	TEMP_VIBRATE(1, "温振一体"),

	/**
	 * 应力波
	 */
	STRESS_WAVE(2, "应力波"),

	/**
	 * 电流
	 */
	ELECTRIC(3, "电流"),

	/**
	 * 转速
	 */
	RPM(4, "转速");

	final Integer code;
	final String name;

	public static SensorCategoryEnum getByCode(Integer code){
		for (SensorCategoryEnum value : SensorCategoryEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
