package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备异常-非连续策略配置
 *
 * <AUTHOR>
 * @date 2024-05-21 20:07
 */
@Data
@ApiModel(value = "DiscontinuousStrategyVO对象", description = "DiscontinuousStrategyVO对象")
public class DiscontinuousStrategyVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 报警门限
	 */
	@ApiModelProperty(value = "报警门限")
	private AbnormalParamVO alarmThreshold;
	/**
	 * 机理模型
	 */
	@ApiModelProperty(value = "机理模型")
	private AbnormalParamVO mechanismModel;
	/**
	 * AI模型
	 */
	@ApiModelProperty(value = "AI模型")
	private AbnormalParamVO aiModel;
	/**
	 * 权重和
	 */
	@ApiModelProperty(value = "权重和")
	private Integer weightSum;

}
