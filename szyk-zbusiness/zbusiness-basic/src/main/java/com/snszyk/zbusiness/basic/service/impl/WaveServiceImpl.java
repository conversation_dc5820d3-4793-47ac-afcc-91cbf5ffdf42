package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.dto.WaveDTO;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.mapper.MonitorMapper;
import com.snszyk.zbusiness.basic.mapper.WaveMapper;
import com.snszyk.zbusiness.basic.service.IWaveService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 传感器波形表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class WaveServiceImpl extends ServiceImpl<WaveMapper, Wave> implements IWaveService {

	private final MonitorMapper monitorMapper;

	@Override
	public int removeAlarmThresholdByWave(List<Long> waveIdList) {
		return baseMapper.removeAlarmThresholdByWave(waveIdList);
	}

	@Override
	public int unbindWaveByIds(List<Long> waveIdList) {
		return baseMapper.unbindWaveByIds(waveIdList);
	}

	@Override
	public Wave getBy(Long sensorInstanceParamId, Integer unbind) {
		return this.lambdaQuery()
			.eq(Wave::getSensorInstanceParamId, sensorInstanceParamId)
			.eq(Wave::getUnbind, unbind)
			.one();
	}

	@Override
	public WaveDTO getBy(Long id) {
		WaveDTO waveDTO = null;
		Wave wave = super.getById(id);
		if (Func.isNotEmpty(wave)) {
			waveDTO = Objects.requireNonNull(BeanUtil.copy(wave, WaveDTO.class));
			Monitor monitor = monitorMapper.selectById(wave.getMonitorId());
			waveDTO.setTenantId(monitor.getTenantId()).setEquipmentId(monitor.getEquipmentId())
				.setPath(monitor.getPath() + StringPool.COMMA + id)
				.setPathName(monitor.getPathName() + StringPool.COMMA + wave.getWaveName());
		}
		return waveDTO;
	}
}
