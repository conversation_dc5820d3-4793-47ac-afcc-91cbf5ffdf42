package com.snszyk.zbusiness.basic.rabbit.handler;


import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 命令调度(配置类，自动加载，形成Map<String,BusinessBean>对象)
 * <AUTHOR>
 * @version 1.0
 */
@Component
@Slf4j
public class CommandDispatch {

	/**
	 *
	 * @param commandExec 保存业务
	 * @param commandBusiness 其他业务
	 */
	@Bean
	public Map<String,BusinessBean> dispatch(List<CommandExec> commandExec, List<CommandBusiness> commandBusiness){
		log.info("开始封装命令信息-{}-{}",commandExec,commandBusiness);
		Map<String,BusinessBean> map = new HashMap<>();
		//@Autowire
		////系统启动自动执行，形成Map<String,BusinessBean>对象
		if(commandExec != null){
			commandExec.forEach(e->map.put(e.getCommand(),new BusinessBean(e,new ArrayList<>())));
		}
		if(commandBusiness == null){
			return map;
		}
		commandBusiness.forEach(e->{
			BusinessBean bean = map.get(e.getCommand());
			if(bean == null){
				bean = new BusinessBean(null,new ArrayList<>());
				map.put(e.getCommand(),bean);
			}
			bean.getHandlers().add(e);
		});
		return map;
	}

}
