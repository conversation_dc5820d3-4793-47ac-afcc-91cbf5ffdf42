/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 设备部位表实体类
 *
 * <AUTHOR>
 * @since 2022-10-13
 */
@Data
@Accessors(chain = true)
@TableName("eolm_monitor")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Monitor对象", description = "设备部位表")
public class Monitor extends TenantEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 父主键
	*/
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "父主键")
	private Long equipmentId;
	/**
	* 编码
	*/
	@ApiModelProperty(value = "编码")
	private String code;
	/**
	* 名称
	*/
	@ApiModelProperty(value = "名称")
	private String name;
	/**
	 * 照片
	 */
	@TableField(updateStrategy= FieldStrategy.IGNORED)
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "图片")
	private Long image;
	/**
	 * 测量方向（字典：sensor_measure_direction）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测量方向（字典：sensor_measure_direction）")
	private Integer measureDirection;
	/**
	 * 全路径
	 */
	@ApiModelProperty(value = "全路径")
	private String path;
	/**
	 * 全路径名称
	 */
	@ApiModelProperty(value = "全路径名称")
	private String pathName;
	/**
	 * 设备归属类型（字典：equipment_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备归属类型（字典：equipment_type）")
	private Integer equipmentType;
	/**
	* 排序
	*/
	@ApiModelProperty(value = "排序")
	private Integer sort;

}
