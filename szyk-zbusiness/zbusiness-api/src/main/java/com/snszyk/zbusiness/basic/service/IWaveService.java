package com.snszyk.zbusiness.basic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.snszyk.common.cache.MybatisRedisCache;
import com.snszyk.zbusiness.basic.dto.WaveDTO;
import com.snszyk.zbusiness.basic.entity.Wave;
import org.apache.ibatis.annotations.CacheNamespace;

import java.util.List;

/**
 * 传感器波形表 服务接口
 *
 * <AUTHOR>
 */
@CacheNamespace(implementation = MybatisRedisCache.class)
public interface IWaveService extends IService<Wave> {

	/**
	 * 根据waveId删除门限
	 *
	 * @param waveIdList 波形id
	 * @return
	 */
	int removeAlarmThresholdByWave(List<Long> waveIdList);

	/**
	 * 根据waveId解绑波形
	 *
	 * @param waveIdList 波形id
	 * @return
	 */
	int unbindWaveByIds(List<Long> waveIdList);

	Wave getBy(Long sensorInstanceParamId, Integer unbind);


	WaveDTO getBy(Long id);
}
