/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 报警统计图数据
 *
 * <AUTHOR>
 * @since 2023-07-10
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "AlarmStatisticsChartDTO对象", description = "AlarmStatisticsChartDTO对象")
public class AlarmStatisticsChartDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "总报警数")
	private Integer alarmSum;

	@ApiModelProperty(value = "已成缺陷")
	private Integer isFaultSum;

	@ApiModelProperty(value = "一级报警")
	private Integer levelOneSum;

	@ApiModelProperty(value = "二级报警")
	private Integer levelTwoSum;

	@ApiModelProperty(value = "三级报警")
	private Integer levelThreeSum;

	@ApiModelProperty(value = "四级报警")
	private Integer levelFourSum;

	@ApiModelProperty(value = "未处理报警")
	private Integer stateZeroSum;

	@ApiModelProperty(value = "已成缺陷报警")
	private Integer stateOneSum;

	@ApiModelProperty(value = "已关闭报警")
	private Integer stateTwoSum;

}
