/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.zbusiness.basic.entity.MonitorModel;
import com.snszyk.zbusiness.basic.vo.MechanismModelParamVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 部位机理模型表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MonitorModelDTO extends MonitorModel {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备路径
	 */
	@ApiModelProperty(value = "设备路径")
	private String pathName;

	/**
	 * 部位名称
	 */
	@ApiModelProperty(value = "部位名称")
	private String monitorName;

	/**
	 * 应用设备类型（字典：device_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "应用设备类型（字典：device_type）")
	private Integer applyEquipment;

	/**
	 * 应用功率范围（字典：power_range）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "应用功率范围（字典：power_range）")
	private Integer applyPower;

	/**
	 * 机理类型（字典：model_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "机理类型（字典：model_type）")
	private Integer type;

	/**
	 * 应用数据类型（字典：sampled_data_type）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "应用数据类型（字典：sampled_data_type）")
	private Integer applyData;

	/**
	 * 应用设备类型
	 */
	@ApiModelProperty(value = "应用设备类型名称")
	private String applyEquipmentName;

	/**
	 * 应用功率范围名称
	 */
	@ApiModelProperty(value = "应用功率范围名称")
	private String applyPowerName;

	/**
	 * 应用数据类型名称
	 */
	@ApiModelProperty(value = "应用数据类型名称")
	private String applyDataName;

	/**
	 * 机理类型名称
	 */
	@ApiModelProperty(value = "机理类型名称")
	private String typeName;

	/**
	 * 机理模型参数集合
	 */
	@ApiModelProperty(value = "机理模型参数集合")
	private List<MechanismModelParamVO> modelParamList;

	/**
	 * 是否有子集
	 */
	@ApiModelProperty(value = "是否有子集")
	private Integer hasChildren;

	/**
	 * 子集
	 */
	@ApiModelProperty(value = "子集")
	private List<MonitorModelDTO> children;

	/**
	 * 机理模型说明
	 */
	@ApiModelProperty(value = "机理模型说明")
	private String remark;

	/**
	 * 机理模型状态
	 */
	@ApiModelProperty(value = "机理模型状态")
	private Integer modelStatus;

}
