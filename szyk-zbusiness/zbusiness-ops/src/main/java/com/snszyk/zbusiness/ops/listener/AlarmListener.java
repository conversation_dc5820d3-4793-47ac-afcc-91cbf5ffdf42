package com.snszyk.zbusiness.ops.listener;

import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import com.snszyk.zbusiness.ops.service.logic.AlarmLogicService;
import com.snszyk.zbusiness.ops.vo.AiAlarmResultVO;
import com.snszyk.zbusiness.ops.vo.ModelAlarmResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 报警监听Listener
 *
 * <AUTHOR>
 * @date 2023/02/10 15:56
 **/
@Slf4j
@Configuration
public class AlarmListener {

	private static final String[] KEY_ALARM_HANDLER = {"threshold", "mechanism", "ai"};
	@Resource
	private AlarmLogicService alarmLogicService;

	@RabbitHandler
//	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_SENSOR_MODEL_DATA_ALARM)
	public void handleData(SensorDataVO sensorData) {
		log.info("=====》报警模块开始处理传感器采样数据：========{}", sensorData);
		alarmLogicService.handleSensorData(sensorData);
		log.info("=====》报警模块传感器采样数据处理结束：========{}", sensorData);
	}

	@RabbitHandler
//	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_AI_ALARM)
	public void handleData(AiAlarmResultVO data) {
		log.info("=====》开始处理AI报警数据：========{}", data);
		alarmLogicService.handleAiAlarmData(data);
		log.info("=====》完成处理AI报警数据：========{}", data);
	}

	@RabbitHandler
//	@RabbitListener(queues = EolmConstant.Rabbit.QUEUE_MECHANISM_MODEL_ALARM)
	@Transactional(rollbackFor = Exception.class)
	public void handleData(ModelAlarmResultVO data) {
		log.info("=====》开始处理机理模型报警数据：========{}{}", data, DateUtil.now().getTime());
		try {
			Thread.sleep(5000*2);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		log.info("=====》开始处理机理模型报警数据：========{}{}", data, DateUtil.now().getTime());
		if(Func.equals(Func.toInt(StringPool.ZERO), data.getAlarmLevel())){
			alarmLogicService.setRedisData(KEY_ALARM_HANDLER[1], data.getWaveId(),
				DateUtil.parse(data.getOriginTime(), DateUtil.PATTERN_DATETIME), 1);
			alarmLogicService.sensorDataHandler(null, data.getWaveId() + StringPool.COLON +
				DateUtil.parse(data.getOriginTime(), DateUtil.PATTERN_DATETIME).getTime() + StringPool.COLON + KEY_ALARM_HANDLER[1]);
			return;
		} else {
			alarmLogicService.setRedisData(KEY_ALARM_HANDLER[1], data.getWaveId(),
				DateUtil.parse(data.getOriginTime(), DateUtil.PATTERN_DATETIME), 2);
		}
		alarmLogicService.handleModelAlarmData(data);
		log.info("=====》完成处理机理模型报警数据：========{}", data);
	}

}
