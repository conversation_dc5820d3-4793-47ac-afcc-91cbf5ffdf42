package com.snszyk.zbusiness.basic.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 导入传感器实例结果数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ImportSensorInstanceVO对象", description = "ImportSensorInstanceVO对象")
public class ImportSensorInstanceVO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "导入成功数量")
	private Integer successNumber;

	@ApiModelProperty(value = "第一条导入失败的序号")
	private Integer firstFailNumber;

	@ApiModelProperty(value = "导入失败的测点路径")
	private String failureMonitorPath;

	@ApiModelProperty(value = "导入失败信息")
	private String failureMessage;

}
