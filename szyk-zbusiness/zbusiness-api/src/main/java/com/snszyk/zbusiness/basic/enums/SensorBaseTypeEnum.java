/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 传感器轴数类型枚举类
 *
 * <AUTHOR>
 * @date 2023/08/16 11:36
 **/
@Getter
@AllArgsConstructor
public enum SensorBaseTypeEnum {

	/**
	 * 单轴
	 */
	SINGLE(0, "单轴"),

	/**
	 * 多轴
	 */
	MULTIPLE(1, "多轴");

	final Integer code;
	final String name;

	public static SensorBaseTypeEnum getByCode(Integer code){
		for (SensorBaseTypeEnum value : SensorBaseTypeEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
