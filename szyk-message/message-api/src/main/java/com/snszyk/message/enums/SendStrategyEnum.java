package com.snszyk.message.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 发送策略枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SendStrategyEnum {

    /**
     * 立即发送
     */
    IMMEDIATE("IMMEDIATE", "立即发送"),

    /**
     * 固定时间发送
     */
    FIXED_TIME("FIXED_TIME", "固定时间发送"),

    /**
     * 间隔发送
     */
    INTERVAL("INTERVAL", "间隔发送");

    private final String code;
    private final String name;

    /**
     * 根据编码获取枚举
     */
    public static SendStrategyEnum getByCode(String code) {
        for (SendStrategyEnum strategy : values()) {
            if (strategy.getCode().equals(code)) {
                return strategy;
            }
        }
        return null;
    }
}