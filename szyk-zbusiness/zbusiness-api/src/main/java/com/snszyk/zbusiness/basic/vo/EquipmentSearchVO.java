package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.support.Query;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备运行监控查询条件
 *
 * <AUTHOR>
 * @date 2023/05/16 09:28
 **/
@Data
@AllArgsConstructor
public class EquipmentSearchVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "地点ID")
	private Long deviceId;

	@ApiModelProperty(value = "设备名称")
	private String name;

	@ApiModelProperty(value = "报警等级")
	private Integer alarmLevel;

	@ApiModelProperty(value = "设备分类，多个用','逗号分隔")
	private String category;

	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "生产工艺")
	private Integer produceTech;

	@ApiModelProperty(value = "查询")
	private Query query;

}
