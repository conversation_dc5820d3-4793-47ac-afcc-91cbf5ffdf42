/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.vo;

import com.snszyk.system.entity.Post;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 岗位表视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PostAttilaVO对象", description = "岗位表")
public class PostAttilaVO extends Post {
	private static final long serialVersionUID = 1L;

	/**
	 * 岗位分类名
	 */
	private String categoryName;

}
