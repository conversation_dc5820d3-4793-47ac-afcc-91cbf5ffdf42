/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.resource.entity.Attach;
import com.snszyk.zbusiness.basic.entity.Equipment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备信息表实体类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EquipmentDTO对象", description = "设备信息表")
public class EquipmentDTO extends Equipment {

	/**
	 * 设备分类
	 */
	@ApiModelProperty(value = "设备分类、")
	private String categoryName;

	/**
	 * 设备等级
	 */
	@ApiModelProperty(value = "设备等级")
	private String gradeName;

	/**
	 * 波形名称
	 */
	@ApiModelProperty(value = "波形名称")
	private String waveName;

	/**
	 * 测点列表
	 */
	@ApiModelProperty(value = "测点列表")
	private List<MonitorDTO> monitorList;

	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> imageList;

	/**
	 * 报警等级
	 */
	@ApiModelProperty(value = "报警等级")
	private String alarmLevel;

	/**
	 * 报警等级名称
	 */
	@ApiModelProperty(value = "报警等级名称")
	private String alarmLevelName;

	/**
	 * 生产工艺名称
	 */
	@ApiModelProperty(value = "生产工艺名称")
	private String produceTechName;

	/**
	 * 3D场景名称
	 */
	@ApiModelProperty(value = "3D场景名称")
	private String sceneName;

	/**
	 * 设备类型
	 */
	@ApiModelProperty(value = "设备类型")
	private String types;

	/**
	 * 设备类型名称
	 */
	@ApiModelProperty(value = "设备类型名称")
	private String typesName;

	@ApiModelProperty(value = "异常等级")
	private Integer abnormalLevel;
	@ApiModelProperty(value = "异常等级")
	private String abnormalLevelName;
	/**
	 * 异常原因
	 */
	@ApiModelProperty(value = "异常原因")
	private String abnormalReason;
	/**
	 * 诊断结论
	 */
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;
	/**
	 * 检维修建议
	 */
	@ApiModelProperty(value = "检维修建议")
	private String suggestion;
	/**
	 * 首次异常时间
	 */
	@ApiModelProperty(value = "首次异常时间")
	private LocalDateTime firstTime;
	/**
	 * 最新异常时间
	 */
	@ApiModelProperty(value = "最新异常时间")
	private LocalDateTime lastTime;

}
