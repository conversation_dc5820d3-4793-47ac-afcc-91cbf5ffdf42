/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.zbusiness.basic.dto.MonitorModelDTO;
import com.snszyk.zbusiness.basic.entity.MonitorModel;
import com.snszyk.zbusiness.basic.vo.MonitorModelVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部位机理模型表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
public interface MonitorModelMapper extends BaseMapper<MonitorModel> {

	/**
	 * 机理模型分页
	 *
	 * @param page
	 * @param monitorModel
	 * @return
	 */
	List<MonitorModelDTO> page(IPage page, @Param("monitorModel") MonitorModelVO monitorModel);

	/**
	 * 部位机理模型分页
	 *
	 * @param page
	 * @param monitorModel
	 * @return
	 */
	List<MonitorModelDTO> monitorModelPage(IPage page, @Param("monitorModel") MonitorModelVO monitorModel);

	/**
	 * 获取部位应用的模型子集
	 *
	 * @param monitorId
	 * @param modelId
	 * @return
	 */
	List<MonitorModelDTO> getChildrenList(@Param("monitorId")Long monitorId, @Param("modelId") Long modelId);

}
