/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 3D模型配置表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "DeviceModelConfigVO对象", description = "3D模型配置表")
public class DeviceModelConfigVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 采样数据类型
	 */
	@ApiModelProperty(value = "采样数据类型")
	private String sampleDataType;

	/**
	 * 传感器参数id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器参数id")
	private Long sensorParamId;

	/**
	 * 传感器参数id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器参数id")
	private String sensorParamName;

	/**
	 * 指标列表JSON
	 */
	@ApiModelProperty(value = "指标列表JSON")
	private String quota;

	/**
	 * 指标列表
	 */
	@ApiModelProperty(value = "指标列表")
	private List<String> quotaList;

}
