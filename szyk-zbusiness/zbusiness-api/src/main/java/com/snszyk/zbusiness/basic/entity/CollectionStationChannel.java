/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("eolm_collection_station_channel")
@ApiModel(value = "CollectionStation对象", description = "CollectionStation对象")
public class CollectionStationChannel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 采集站id
	 */
	@ApiModelProperty(value = "采集站id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long stationId;

	/**
	 * 名称
	 */
	@ApiModelProperty(value = "名称")
	private String name;

	/**
	 * 是否在线：0-离线；1-在线
	 */
	@ApiModelProperty(value = "是否在线：0-离线；1-在线")
	private Integer online;

	/**
	 * 是否在线
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "是否在线")
	private String onlineName;

	/**
	 * 绑定的传感器编码
	 */
	@ApiModelProperty(value = "绑定的传感器编码")
	private String sensorCode;

}
