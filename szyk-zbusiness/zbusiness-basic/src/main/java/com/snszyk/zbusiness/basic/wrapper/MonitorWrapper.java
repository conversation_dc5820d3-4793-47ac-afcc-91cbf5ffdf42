/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.zbusiness.basic.entity.Monitor;
import com.snszyk.zbusiness.basic.vo.MonitorVO;

import java.util.Objects;

/**
 * 测点信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class MonitorWrapper extends BaseEntityWrapper<Monitor, MonitorVO> {

	public static MonitorWrapper build() {
		return new MonitorWrapper();
	}

	@Override
	public MonitorVO entityVO(Monitor monitor) {
		MonitorVO monitorVO = Objects.requireNonNull(BeanUtil.copy(monitor, MonitorVO.class));
		return monitorVO;
	}

}
