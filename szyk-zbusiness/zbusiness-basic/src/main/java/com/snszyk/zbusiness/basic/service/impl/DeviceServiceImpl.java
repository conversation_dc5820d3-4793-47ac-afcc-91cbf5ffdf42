/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.base.BaseServiceImpl;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.vo.DelDetailVO;
import com.snszyk.system.vo.DelResultVO;
import com.snszyk.zbusiness.basic.dto.DeviceCoordinateSubDTO;
import com.snszyk.zbusiness.basic.dto.DeviceDTO;
import com.snszyk.zbusiness.basic.entity.BasicTree;
import com.snszyk.zbusiness.basic.entity.Device;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.enums.DeviceCategoryEnum;
import com.snszyk.zbusiness.basic.mapper.DeviceMapper;
import com.snszyk.zbusiness.basic.service.IBasicTreeService;
import com.snszyk.zbusiness.basic.service.IDeviceService;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 设备树表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Service
@AllArgsConstructor
public class DeviceServiceImpl extends BaseServiceImpl<DeviceMapper, Device> implements IDeviceService {

	private final IEquipmentService equipmentService;
	private final IBasicTreeService basicTreeService;

	@Override
	public List<Device> getDeviceChild(Long deviceId) {
		List<Device> deviceList = new ArrayList<>();
		List<BasicTree> list = basicTreeService.list(Wrappers.<BasicTree>query().lambda()
			.eq(BasicTree::getNodeCategory, DeviceCategoryEnum.DEVICE.getCode()).like(BasicTree::getAncestors, deviceId));
		if(Func.isNotEmpty(list)){
			deviceList = baseMapper.selectList(Wrappers.<Device>query().lambda()
				.in(Device::getId, list.stream().map(BasicTree::getId).collect(Collectors.toList())));
		}
		return deviceList;
	}

	@Override
	public boolean removeDevice(List<Long> ids) {
		List<Device> list = baseMapper.selectList(Wrappers.<Device>query().lambda().in(Device::getId, ids));
		if (Func.isEmpty(list)) {
			throw new ServiceException("当前节点已删除，请刷新后重试!");
		}
		Integer cnt = baseMapper.selectCount(Wrappers.<Device>query().lambda().in(Device::getParentId, ids));
		if (cnt > 0) {
			throw new ServiceException("请先删除子节点!");
		}
		int count = equipmentService.count(Wrappers.<Equipment>query().lambda().in(Equipment::getDeviceId, ids));
		if (count > 0){
			throw new ServiceException("请先删除设备！");
		}
		// 删除基础树节点
		basicTreeService.removeByIds(ids);
		return removeByIds(ids);
	}

	@Override
	public boolean isNameExist(Device device) {
		return lambdaQuery().eq(Device::getName, device.getName())
			.eq(Device::getParentId, device.getParentId())
			.ne(device.getId() != null, Device::getId, device.getId())
			.count() > 0;
	}

	@Override
	public boolean isCodeDuplicate(Device device) {
		return lambdaQuery().eq(Device::getCode, device.getCode())
			.ne(device.getId() != null, Device::getId, device.getId())
			.count() > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public DelResultVO checkAndRemoveDevice(List<Long> ids) {
		DelResultVO resultVO = new DelResultVO();
		ids.forEach(id -> {
			//查询机构信息
			Device device = this.getById(id);
			//如果机构不存在，直接失败返回
			if (device == null) {
				resultVO.getDetailVOList().add(new DelDetailVO(id.toString(),Boolean.FALSE, "点位不存在"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
			//如果存在子节点，不允许删除
			Integer childDeviceCnt = baseMapper.selectCount(Wrappers.<Device>query().lambda().in(Device::getParentId, Arrays.asList(id)));
			if (childDeviceCnt > 0) {
				resultVO.getDetailVOList().add(new DelDetailVO(device.getName(),Boolean.FALSE, "存在子节点，不允许删除!"));
				resultVO.setFailureNumber(resultVO.getFailureNumber() + 1);
				return;
			}
		});
		return resultVO;
	}

	@Override
	public List<DeviceCoordinateSubDTO> deviceView(Long id) {
		return baseMapper.deviceView(id);
	}

	@Override
	public DeviceDTO getByCode(String code) {
		Device device = this.getOne(Wrappers.<Device>query().lambda().eq(Device::getCode, code));
		if(device == null){
			return null;
		}
		DeviceDTO dto = Objects.requireNonNull(BeanUtil.copy(device, DeviceDTO.class));
		BasicTree basicTree = basicTreeService.getById(device.getId());
		dto.setPath(basicTree.getPath()).setPathName(basicTree.getPathName());
		return dto;
	}

	@Override
	public List<DeviceDTO> getByLevel(Integer level) {
		return baseMapper.getByLevel(level);
	}

	@Override
	public List<DeviceDTO> getByParentId(Long parentId) {
		return baseMapper.getByParentId(parentId);
	}

	@Override
	public Integer getMaxSort() {
		Integer maxSort = baseMapper.selectMaxSort();
		return maxSort == null ? 0 : maxSort;
	}

	@Override
	public List<Device> listSecondLevelDevice(String tenantId) {
		return baseMapper.listSecondLevelDevice(tenantId);
	}
}
