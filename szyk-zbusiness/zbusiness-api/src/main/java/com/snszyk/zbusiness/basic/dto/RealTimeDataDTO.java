/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 3D模型配置表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
public class RealTimeDataDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 传感器参数id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "传感器参数id")
	private Long sensorParamId;

	/**
	 * 传感器参数名称
	 */
	@ApiModelProperty(value = "传感器参数名称")
	private String sensorParamName;

	/**
	 * 实时数据列表
	 */
	@ApiModelProperty(value = "实时数据列表")
	private List<SensorModelDataDTO> dataList;

}
