package com.snszyk.zbusiness.basic.rabbit.handler;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MessageBean implements Command {

	/**
	 * 操作命令
	 * 加速度操作、速度操作、位移操作、温度操作
	 */
	private String command;

	/**
	 * 传感器ID - 传感器唯一标识码（传感器编码）
	 */
	private String id;

	/**
	 * 采集站id（网关id）
	 */
	private String stationId;

	/**
	 * 采样频率
	 */
	private Integer samplingFreq;

	/**
	 * 采样时间
	 */
	private Integer samplingTime;

	/**
	 * 采样点数
	 */
	private Integer samplingPoints;

	/**
	 * 轴向，0：Z轴，1：X轴，2：Y轴 - 仅三轴传感器的加速度、速度、位移数据有此数据！
	 */
	private Integer axis;

	/**
	 * 采样数据类型，如温度、加速度、速度、位移、传感器在线状态、传感器剩余电量等6种数据类型
	 */
	private String type;

	/**
	 * 值（如温度值、电量值、压力值、加速度值、速度值、特征图谱值等）
	 */
	private String value;

	/**
	 * 采样时间
	 */
	private Long originTime;

	/**
	 * 波形数据
	 */
	@ToString.Exclude
	private String wave;

	/**
	 * 峰值
	 */
	private Float peak;

	/**
	 * 峰峰值
	 */
	private Float peakPeak;

	/**
	 * 裕度
	 */
	private Float clearance;

	/**
	 * 歪度
	 */
	private Float skewness;

	/**
	 * 峭度
	 */
	private Float kurtosis;

	/**
	 * 有效值
	 */
	private Float effective;

	/**
	 * 保存传感器数据后的sidas_sensor_data_${monitor_id} 和 原始数据id。
	 */
	private Long monitorId;

	/**
	 * 传感器实例id
	 */
	private Long sensorInstanceId;

	/**
	 * 传感器实例参数id
	 */
	private Long sensorInstanceParamId;

	/**
	 * 传感器数据id
	 */
	private Long sensorDataId;

	/**
	 * 传感器数据
	 */
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@ToString.Exclude
	private SensorDataVO sensorDataVO;

	/**
	 * 停机线
	 */
	private BigDecimal haltLine;

}
