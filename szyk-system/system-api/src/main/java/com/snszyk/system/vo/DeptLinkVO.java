package com.snszyk.system.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "DeptLinkVO对象", description = "关联组织列表")
public class DeptLinkVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "组织ID")
    private String orgId;

    @ApiModelProperty(value = "组织名称")
    private String orgName;

    public DeptLinkVO() {
    }

    public DeptLinkVO(String orgId, String orgName) {
        this.orgId = orgId;
        this.orgName = orgName;
    }
}
