/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.tool.api.R;
import com.snszyk.zbusiness.basic.service.IWaveMarkService;
import com.snszyk.zbusiness.basic.vo.ParamBearingVO;
import com.snszyk.zbusiness.basic.vo.ParamGearVO;
import com.snszyk.zbusiness.basic.vo.ParamRpmVO;
import com.snszyk.zbusiness.basic.vo.WaveMarkVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 波形标注表 控制器
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/waveMark")
@Api(value = "波形标注表", tags = "波形标注表接口")
public class WaveMarkController extends SzykController {

	private final IWaveMarkService waveMarkService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入waveMark")
	public R<WaveMarkVO> detail(WaveMarkVO waveMark) {
		WaveMarkVO detail = waveMarkService.detail(waveMark);
		return R.data(detail);
	}

	/**
	 * 新增或修改 波形标注表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "新增或修改", notes = "传入waveMark")
	public R submit(@Valid @RequestBody WaveMarkVO waveMark) {
		return R.status(waveMarkService.submit(waveMark));
	}

	/**
	 * 选择轴承型号
	 */
	@GetMapping("/bearingSelect")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "选择轴承型号", notes = "传入waveId")
	public R<List<ParamBearingVO>> bearingSelect(@ApiParam(value = "波形id", required = true) @RequestParam Long waveId) {
		return R.data(waveMarkService.bearingSelect(waveId));
	}

	/**
	 * 选择转速
	 */
	@GetMapping("/rpmSelect")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "选择转速", notes = "传入waveId")
	public R<List<ParamRpmVO>> rpmSelect(@ApiParam(value = "波形id", required = true) @RequestParam Long waveId,
										 @ApiParam(value = "采样时间", required = true) @RequestParam String originTime) {
		return R.data(waveMarkService.rpmSelect(waveId, originTime));
	}

	/**
	 * 选择齿数
	 */
	@GetMapping("/gearTeethSelect")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "选择齿数", notes = "传入waveId")
	public R<List<ParamGearVO>> gearTeethSelect(@ApiParam(value = "波形id", required = true) @RequestParam Long waveId) {
		return R.data(waveMarkService.gearTeethSelect(waveId));
	}

}
