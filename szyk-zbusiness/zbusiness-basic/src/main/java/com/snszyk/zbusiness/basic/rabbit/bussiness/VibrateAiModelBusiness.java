package com.snszyk.zbusiness.basic.rabbit.bussiness;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.config.SzykAttachConfig;
import com.snszyk.common.constant.EolmConstant;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.AiModelDTO;
import com.snszyk.zbusiness.basic.dto.SensorDataDTO;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import com.snszyk.zbusiness.basic.service.IAiModelService;
import com.snszyk.zbusiness.basic.service.IEquipmentService;
import com.snszyk.zbusiness.basic.service.IWaveService;
import com.snszyk.zbusiness.basic.vo.AiDataVO;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI模型业务
 * <AUTHOR>
 */
@Slf4j
@Component
public class VibrateAiModelBusiness extends AbstractBusiness {

	@Resource
	private IAiModelService aiModelService;
	@Resource
	private IEquipmentService equipmentService;
	@Resource
	private IWaveService waveService;
	@Resource
	private InfluxdbTools influxdbTools;
	@Resource
	private RabbitTemplate rabbitTemplate;
	@Resource
	private SzykAttachConfig szykAttachConfig;

	/**
	 * 上传文件路径
	 */
	private String rootPath;

	@PostConstruct
	public void initField(){
		this.rootPath = szykAttachConfig.szykAttachProperties().getPath();
	}

	@Override
	public String getCommand() {
		return Command.VIBRATE_COMMAND;
	}

	@Override
	public void business(MessageBean message) {
		super.business(message);
		log.info("AI模型业务处理开始");
		// 传感器编码
		String sensorCode = message.getId();
		// 获取该传感器绑定的设备ID、模型名称列表
		List<AiModelDTO> aiModelList = aiModelService.queryAiModelParams(sensorCode);
		if (aiModelList == null || aiModelList.isEmpty()) {
			log.error("AI模型为空或AI模型获取失败！");
			return;
		}
		// 停机数据过滤
		if(new BigDecimal(message.getValue()).compareTo(message.getHaltLine()) > 0){
			Long equipmentId = aiModelList.get(0).getEquipmentId();
			List<String> aiModelNames = aiModelList.stream().map(dto -> dto.getName()).collect(Collectors.toList());
			Wave wave = waveService.getOne(Wrappers.<Wave>query().lambda()
				.eq(Wave::getSensorInstanceParamId, message.getSensorInstanceParamId()).eq(Wave::getUnbind, 0));
			String originTime = DateUtil.formatDateTime(new Date(message.getOriginTime()));
			SensorDataVO sensorData = new SensorDataVO(null, wave.getId(), null);
			JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
			List<JSONObject> list = influxdbTools.queryData(message.getOriginTime() - 1000L, message.getOriginTime() + 1000L, jsonObject);
			List<SensorDataDTO> sensorDataList = new ArrayList<>();
			list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
			// 组装协议数据
			AiDataVO aiData = new AiDataVO(equipmentId, wave.getId(), sensorCode, aiModelNames, originTime);
			aiData.setEquipmentName(equipmentService.getById(equipmentId).getName());
			if(Func.isNotEmpty(sensorDataList)){
				aiData.setFilePath(rootPath + sensorDataList.get(0).getWaveformUrl());
			}
			log.info("AI模型组装数据======================：{}", aiData);
			// 将数据发送到MQ
			rabbitTemplate.convertAndSend(EolmConstant.Rabbit.EXCHANGE_AI_WAVE, null, aiData);
		}
	}

}
