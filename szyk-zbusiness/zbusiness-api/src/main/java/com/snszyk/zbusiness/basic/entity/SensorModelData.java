package com.snszyk.zbusiness.basic.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 传感器机理模型数据（eolm_sensor_model_data表已废弃）
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SensorModelData对象", description = "传感器机理模型数据")
public class SensorModelData implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("主键id")
	private Long id;

	/**
	 * 波形配置id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("波形id")
	private Long waveId;

	/**
	 * 测点ID
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "测点ID")
	private Long monitorId;

	/**
	 * 采样数据类型（字典：sampled_data_type）
	 */
	@ApiModelProperty(value = "采样数据类型（字典：sampled_data_type）")
	private String sampleDataType;

	/**
	 * 机理模型编码
	 */
	@ApiModelProperty(value = "机理模型编码")
	private String modelCode;

	/**
	 * 结果值
	 */
	@ApiModelProperty("结果值")
	private BigDecimal result;

	/**
	 * 报警等级
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty("报警等级")
	private Integer alarmLevel;

	/**
	 * 采集时间
	 */
	@ApiModelProperty("采集时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date originTime;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

}
