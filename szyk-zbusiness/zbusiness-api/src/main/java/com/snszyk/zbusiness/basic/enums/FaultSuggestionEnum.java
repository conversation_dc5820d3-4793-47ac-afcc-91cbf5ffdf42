/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 故障建议枚举类
 *
 * <AUTHOR>
 **/
@Getter
@AllArgsConstructor
public enum FaultSuggestionEnum {

	/**
	 * 不平衡
	 */
	IMBALANCE(1, "不平衡", "进行平衡校正，使用专业的平衡设备来检测设备的不平衡量，如添加或去除平衡配重、调整零部件位置等，以达到设备的动平衡状态"),

	/**
	 * 不对中
	 */
	MISALIGNMENT(2, "不对中", "使用对中测量仪检查对中程度，检查设备对中情况，日常点检注意观察设备状态"),

	/**
	 * 结构松动
	 */
	STRUCTURE_LOOSENESS(3, "结构松动", "巩固基础、矫正结构、紧固松动螺栓，日常点检注意观察设备状态"),

	/**
	 * 轴承故障
	 */
	BEARING_FAULT(4, "轴承故障", "改善润滑情况，日常点检注意观察设备状态"),

	/**
	 * 齿轮故障
	 */
	GEAR_FAULT(5, "齿轮故障", "检查润滑系统的运行状况，确保润滑油的质量，注意齿轮轴线的位置和方向，确保齿轮与齿轮副的配合精度"),

	/**
	 * 叶轮故障
	 */
	IMPELLER_FAULT(6, "叶轮故障", "检查叶轮叶片的完整性和形状，是否存在疲劳破裂、变形或断裂，清洗叶轮：如果叶轮表面有污垢、固体颗粒或腐蚀物，可以使用适当的清洗方法将其清除"),

	/**
	 * 摩擦故障
	 */
	FRICTION_FAULT(7, "摩擦故障", "改善润滑情况，日常点检注意观察设备状态"),

	/**
	 * 润滑不良
	 */
	LUBRICATE_FAULT(8, "润滑不良", "检查润滑脂和润滑油，检查油路是否存在堵塞或泄漏现象，检查轴承温度情况，检查轴承振动和声音有无异响"),

	/**
	 * 不支持
	 */
	UNSUPPORTED(9999, "不支持的故障", ""),
	;

	final Integer code;
	final String fault;
	final String suggestion;

	public static FaultSuggestionEnum getByCode(Integer code) {
		for (FaultSuggestionEnum value : FaultSuggestionEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return UNSUPPORTED;
	}

}
