/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 算法枚举类
 *
 * <AUTHOR>
 * @date 2023/02/14 11:37
 **/
@Getter
@AllArgsConstructor
public enum AlgorithmEnum {

	/**
	 * 振动速度时域
	 */
	VIBRATION_VELOCITY_TIME(6, "振动速度时域"),

	/**
	 * 振动速度频域
	 */
	VIBRATION_VELOCITY_FREQUENCY(7, "振动速度频域"),

	/**
	 * 振动加速度时域
	 */
	VIBRATION_ACCELERATION_TIME(8, "振动加速度时域"),

	/**
	 * 振动加速度频域
	 */
	VIBRATION_ACCELERATION_FREQUENCY(9, "振动加速度频域"),

	/**
	 * 振动加速度包络
	 */
	VIBRATION_ACCELERATION_ENVELOPE_FREQUENCY(10, "振动加速度包络"),

	/**
	 * 相位
	 */
	PHASE(11, "相位"),

	/**
	 * 位移
	 */
	SHIFT(12, "位移"),

	/**
	 * 温度
	 */
	TEMPERATURE(13, "温度"),

	/**
	 * 电量
	 */
	QUANTITY_OF_ELECTRICITY(14, "电量"),

	/**
	 * 暂不支持 - 防止switch空指针！
	 */
	UNSUPPORTED(9999, "暂不支持"),

	;

	final Integer code;
	final String name;

	public static AlgorithmEnum getByCode(Integer code) {
		for (AlgorithmEnum value : AlgorithmEnum.values()) {
			if (code.equals(value.getCode())) {
				return value;
			}
		}

		return UNSUPPORTED;
	}

}
