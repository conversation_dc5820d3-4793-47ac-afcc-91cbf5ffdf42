package com.snszyk.zbusiness.ai.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
@ApiModel("消息")
public class MessageVo {

	/**
	 * 用户标识
	 */
	@ApiModelProperty("用户标识")
	private String user;
	/**
	 * 消息内容
	 */
	@ApiModelProperty("消息内容")
	private String content;
	/**
	 * 会话标识
	 */
	@ApiModelProperty("会话标识")
	private String conversationId;
	/**
	 * 请求类型
	 */
	@ApiModelProperty("请求类型")
	private String type;
	/**
	 * 请求参数
	 */
	@ApiModelProperty("请求参数")
	private Map<String, Object> inputs = new HashMap<>();
	/**
	 * 部位id
	 */
	@ApiModelProperty("部位id")
	private Long monitorId;
	/**
	 * 设备id
	 */
	@ApiModelProperty("设备id")
	private Long equipmentId;

	private String desc;

}
