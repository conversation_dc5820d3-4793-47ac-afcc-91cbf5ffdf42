/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.builder.sms;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.SneakyThrows;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.sms.SmsTemplate;
import com.snszyk.core.sms.AliSmsTemplate;
import com.snszyk.core.sms.props.SmsProperties;
import com.snszyk.resource.entity.Sms;

/**
 * 阿里云短信构建类
 *
 * <AUTHOR>
 */
public class AliSmsBuilder {

	@SneakyThrows
	public static SmsTemplate template(Sms sms, SzykRedis szykRedis) {
		SmsProperties smsProperties = new SmsProperties();
		smsProperties.setTemplateId(sms.getTemplateId());
		smsProperties.setAccessKey(sms.getAccessKey());
		smsProperties.setSecretKey(sms.getSecretKey());
		smsProperties.setRegionId(sms.getRegionId());
		smsProperties.setSignName(sms.getSignName());
		IClientProfile profile = DefaultProfile.getProfile(smsProperties.getRegionId(), smsProperties.getAccessKey(), smsProperties.getSecretKey());
		IAcsClient acsClient = new DefaultAcsClient(profile);
		return new AliSmsTemplate(smsProperties, acsClient, szykRedis);
	}

}
