package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 采集站在线状态统计对象
 * <AUTHOR>
 */
@Data
@ApiModel("采集站在线状态统计对象")
public class CollectionStationOnlineStatDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 总数
	 */
	@ApiModelProperty("总数")
	private Integer totalCount;

	/**
	 * 在线数
	 */
	@ApiModelProperty("在线数")
	private Integer onlineCount;

	/**
	 * 离线数
	 */
	@ApiModelProperty("离线数")
	private Integer offlineCount;

	/**
	 * 通道数
	 */
	@ApiModelProperty("通道数")
	private Integer channelCount;

}
