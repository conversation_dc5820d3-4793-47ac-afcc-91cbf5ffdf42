package com.snszyk.zbusiness.basic.dto;

import com.snszyk.resource.entity.Attach;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class EquipmentRunningMonitorDTO {

	@ApiModelProperty(value = "设备ID")
	private Long id;
	@ApiModelProperty(value = "设备名称")
	private String name;
	@ApiModelProperty(value = "设备编码")
	private String code;
	@ApiModelProperty(value = "开始使用时间")
	private Date startDateOfUse;
	@ApiModelProperty(value = "使用寿命")
	private Integer lifeExpectancy;
	@ApiModelProperty(value = "路径名")
	private String pathName;
	@ApiModelProperty(value = "诊断结论")
	private String conclusion;
	@ApiModelProperty(value = "告警等级")
	private Integer alarmLevel;
	@ApiModelProperty(value = "部位总数")
	private Integer ct;
	@ApiModelProperty(value = "异常等级")
	private Integer abnormalLevel;
	private String abnormalLevelName;

	@ApiModelProperty(value = "重点关注")
	public String getImportant(){
		return this.abnormalLevel>1?"是":"否";
	}

	@ApiModelProperty(value = "健康指数")
	private HealthIndex healthIndex;

	private Long image;

	private Attach attach;

	public String getPathName(){
		this.pathName = this.pathName.replace(",","/");
		return this.pathName;
	}



}
