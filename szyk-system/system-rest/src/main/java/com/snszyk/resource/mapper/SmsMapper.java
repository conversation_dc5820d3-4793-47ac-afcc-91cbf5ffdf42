/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.resource.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.snszyk.resource.entity.Sms;
import com.snszyk.resource.vo.SmsVO;

import java.util.List;

/**
 * 短信配置表 Mapper 接口
 *
 * <AUTHOR>
 */
public interface SmsMapper extends BaseMapper<Sms> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param sms
	 * @return
	 */
	List<SmsVO> selectSmsPage(IPage page, SmsVO sms);

}
