package com.snszyk.zbusiness.basic.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.Objects;

/**
 * 设备运行状态转换器
 * <AUTHOR>
 */
public class RunningStatusConverter implements Converter<Integer> {

	private static final String HALT = "停机";
	private static final String RUNNING = "运行中";

	@Override
	public Class supportJavaTypeKey() {
		return Integer.class;
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return CellDataTypeEnum.STRING;
	}

	@Override
	public Integer convertToJavaData(ReadCellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		String cellValue = cellData.getStringValue();
		if (Objects.equals(HALT, cellValue)) {
			return 0;
		} else if (Objects.equals(RUNNING, cellValue)) {
			return 1;
		}
		return null;
	}

	@Override
	public WriteCellData convertToExcelData(Integer value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		if (value == 0) {
			return new WriteCellData(HALT);
		} else if (value == 1) {
			return new WriteCellData(RUNNING);
		}

		return null;
	}

}
