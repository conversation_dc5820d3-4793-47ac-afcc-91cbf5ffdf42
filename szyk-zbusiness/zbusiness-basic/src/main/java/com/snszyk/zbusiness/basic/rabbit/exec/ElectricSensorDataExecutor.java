package com.snszyk.zbusiness.basic.rabbit.exec;

import com.snszyk.zbusiness.basic.entity.SensorData;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.rabbit.handler.Command;
import com.snszyk.zbusiness.basic.rabbit.handler.MessageBean;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 电流类型传感器数据保存
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class ElectricSensorDataExecutor extends AbstractSensorDataExecutor {

	@Override
	public String getCommand() {
		// 针对电流数据类型
		return Command.ELECTRIC_COMMAND;
	}

	@Override
	protected SensorData createSensorData(MessageBean message, SensorData sensorData, Wave wave) {
		return sensorData;
	}
}
