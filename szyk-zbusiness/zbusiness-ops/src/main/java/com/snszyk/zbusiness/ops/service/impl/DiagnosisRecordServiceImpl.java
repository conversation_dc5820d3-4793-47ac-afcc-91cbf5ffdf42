/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.UserCache;
import com.snszyk.system.entity.User;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;
import com.snszyk.zbusiness.ops.entity.DiagnosisAlarmRecord;
import com.snszyk.zbusiness.ops.entity.DiagnosisRecord;
import com.snszyk.zbusiness.ops.enums.DiagnosisTypeEnum;
import com.snszyk.zbusiness.ops.enums.IntelligentDiagnosisTypeEnum;
import com.snszyk.zbusiness.ops.mapper.DiagnosisRecordMapper;
import com.snszyk.zbusiness.ops.service.IAlarmRecordService;
import com.snszyk.zbusiness.ops.service.IDiagnosisAlarmRecordService;
import com.snszyk.zbusiness.ops.service.IDiagnosisRecordService;
import com.snszyk.zbusiness.ops.vo.AlarmRecordVO;
import com.snszyk.zbusiness.ops.vo.DiagnosisRecordVO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 诊断记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Service
@AllArgsConstructor
public class DiagnosisRecordServiceImpl extends ServiceImpl<DiagnosisRecordMapper, DiagnosisRecord> implements IDiagnosisRecordService {

	private final IDiagnosisAlarmRecordService diagnosisAlarmRecordService;
	private final IAlarmRecordService alarmRecordService;

	@Override
	public List<DiagnosisRecordVO> submit(DiagnosisRecordVO vo) {
		DiagnosisRecord diagnosisRecord = Objects.requireNonNull(BeanUtil.copy(vo, DiagnosisRecord.class));
		if(DiagnosisTypeEnum.EXPERT == DiagnosisTypeEnum.getByCode(vo.getDiagnosisType())){
			diagnosisRecord.setDiagnoseUser(AuthUtil.getUserId());
		}
		diagnosisRecord.setDiagnoseTime(DateUtil.now());
		this.saveOrUpdate(diagnosisRecord);
		// 保存诊断与报警记录关联信息
		if(Func.isEmpty(vo.getId())){
			List<AlarmRecordVO> alarmRecordList;
			if(DiagnosisTypeEnum.INTELLIGENCE == DiagnosisTypeEnum.getByCode(vo.getDiagnosisType())){
				alarmRecordList = alarmRecordService.getAlarmRecordList(vo.getAlarmId(), vo.getAlarmType());
			} else {
				alarmRecordList = alarmRecordService.getAlarmRecordList(vo.getAlarmId(), DiagnosisTypeEnum.EXPERT.getCode());
			}
			if(Func.isNotEmpty(alarmRecordList)){
				List<DiagnosisAlarmRecord> list = alarmRecordList.stream().map(alarmRecord -> {
					DiagnosisAlarmRecord diagnosisAlarmRecord = new DiagnosisAlarmRecord();
					diagnosisAlarmRecord.setDiagnosisId(diagnosisRecord.getId()).setAlarmRecordId(alarmRecord.getId())
						.setCreateTime(diagnosisRecord.getDiagnoseTime());
					return diagnosisAlarmRecord;
				}).collect(Collectors.toList());
				diagnosisAlarmRecordService.saveBatch(list);
			}
		}
		List<DiagnosisRecord> list = list(Wrappers.<DiagnosisRecord>query().lambda().eq(DiagnosisRecord::getAlarmId, vo.getAlarmId()));
		list.add(0, diagnosisRecord);
		return BeanUtil.copy(list, DiagnosisRecordVO.class);
	}

	@Override
	public List<DiagnosisRecordVO> getList(DiagnosisRecordVO record) {
		if (Func.isNotEmpty(record.getStartDate())) {
			record.setStartDate(record.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(record.getEndDate())) {
			record.setEndDate(record.getEndDate() + " 23:59:59");
		}
		List<DiagnosisRecord> list = baseMapper.getList(record);
		if(Func.isNotEmpty(list)){
			return list.stream().map(diagnosisRecord -> {
				DiagnosisRecordVO vo = Objects.requireNonNull(BeanUtil.copy(diagnosisRecord, DiagnosisRecordVO.class));
				vo.setDiagnosisTypeName(DictBizCache.getValue(DictBizEnum.DIAGNOSIS_TYPE, diagnosisRecord.getDiagnosisType()));
				User diagnoseUser = UserCache.getUser(diagnosisRecord.getDiagnoseUser());
				if(Func.isNotEmpty(diagnoseUser)){
					vo.setDiagnoseUserName(diagnoseUser.getName());
				} else {
					if(Func.toLong(IntelligentDiagnosisTypeEnum.MECHANISM_MODEL) == diagnosisRecord.getDiagnoseUser()){
						vo.setDiagnoseUserName(IntelligentDiagnosisTypeEnum.MECHANISM_MODEL.getName());
					}
					if(Func.toLong(IntelligentDiagnosisTypeEnum.AI_MODEL) == diagnosisRecord.getDiagnoseUser()){
						vo.setDiagnoseUserName(IntelligentDiagnosisTypeEnum.AI_MODEL.getName());
					}
				}
				return vo;
			}).collect(Collectors.toList());
		}
		return Collections.emptyList();
	}

	@Override
	public boolean handleEquipmentData(EquipmentVO vo) {
		return this.update(Wrappers.<DiagnosisRecord>update().lambda()
			.set(DiagnosisRecord::getEquipmentName, vo.getName()).eq(DiagnosisRecord::getEquipmentId, vo.getId()));
	}

	@Override
	public List<DiagnosisRecordVO> recordsByAlarmId(DiagnosisRecordVO diagnosisRecord) {
		List<DiagnosisRecordVO> resultList = new ArrayList<>();
		List<DiagnosisRecord> list = this.list(Wrappers.<DiagnosisRecord>query().lambda()
			.eq(DiagnosisRecord::getAlarmId, diagnosisRecord.getAlarmId()).eq(Func.isNotEmpty(diagnosisRecord.getDiagnosisType()), DiagnosisRecord::getDiagnosisType, diagnosisRecord.getDiagnosisType())
			.eq(Func.isNotEmpty(diagnosisRecord.getConclusion()), DiagnosisRecord::getConclusion, diagnosisRecord.getConclusion()).orderByDesc(DiagnosisRecord::getDiagnoseTime));
		if(Func.isNotEmpty(list)){
			resultList = list.stream().map(record -> {
				DiagnosisRecordVO vo = Objects.requireNonNull(BeanUtil.copy(record, DiagnosisRecordVO.class));
				if(DiagnosisTypeEnum.EXPERT == DiagnosisTypeEnum.getByCode(vo.getDiagnosisType())){
					vo.setDiagnoseUserName(UserCache.getUser(record.getDiagnoseUser()).getName());
				} else {
					vo.setDiagnoseUserName(IntelligentDiagnosisTypeEnum.getByCode(Func.toInt(record.getDiagnoseUser())).getName());
				}
				return vo;
			}).collect(Collectors.toList());
		}
		return resultList;
	}


}
