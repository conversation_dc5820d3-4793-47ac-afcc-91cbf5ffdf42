/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.controller;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.entity.Role;
import com.snszyk.system.entity.User;
import com.snszyk.system.service.IDeptService;
import com.snszyk.system.service.IPostService;
import com.snszyk.system.service.IRoleService;
import com.snszyk.system.service.IUserService;
import com.snszyk.system.vo.*;
import com.snszyk.system.wrapper.UserWrapper;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@RequestMapping("system-attila")
@AllArgsConstructor
public class AttilaController {

	private final IDeptService deptService;
	private final IPostService postService;
	private final IRoleService roleService;
	private final IUserService userService;


	/**
	 * 获取部门树形结构
	 */
	@GetMapping("/attila/tree")
	@ApiOperation(value = "获取部门树形结构", notes = "获取部门树形结构")
	public R<List<DeptAttilaVO>> attilaTree(String tenantId, SzykUser szykUser) {
		List<DeptAttilaVO> tree = deptService.attilaTree(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()));
		return R.data(tree);
	}

	/**
	 * 获取部门树形结构，及部门下人员
	 */
	@GetMapping("/attila/tree/user")
	@ApiOperation(value = "获取部门树形结构", notes = "获取部门树形结构")
	public R<List<DeptUserAttilaVO>> attilaTreeUser(String tenantId, SzykUser szykUser) {
		List<DeptUserAttilaVO> tree = deptService.attilaTreeUser(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()));
		return R.data(tree);
	}

	/**
	 * 获取当前企业的关联组织列表（上下一级）
	 *
	 * @param orgId
	 * @return 关联组织列表
	 */
	@GetMapping("/org/linkList")
	@ApiOperation(value = "获取当前企业的关联组织列表（上下一级）", notes = "获取当前企业的关联组织列表（上下一级）")
	public R<List<DeptLinkVO>> selectLinkList(@RequestParam(value = "orgId", required = false) Long orgId) {
		return R.data(deptService.selectLinkList(orgId));
	}


	/**
	 * 岗位列表
	 */
	@GetMapping("/attila/list")
	@ApiOperation(value = "岗位列表", notes = "岗位列表")
	public R<List<PostAttilaVO>> attilaList(String tenantId, SzykUser szykUser) {
		List<PostAttilaVO> list = postService.attilaList(Func.toStrWithEmpty(tenantId, szykUser.getTenantId()));
		return R.data(list);
	}

	/**
	 * 查询岗位和岗位下的人员
	 */
	@GetMapping("/user-tree")
	@ApiOperation(value = "当前公司下所有职务以及职务下的人员", notes = "无参数")
	public R<List<PostTreeVO>> selectUserTree(@RequestParam(value = "nodeId", required = false) String nodeId,
											  @RequestParam(value = "processId", required = false) String processId,
											  @RequestParam(value = "orgId", required = false) Long orgId,
											  String tenantId, SzykUser szykUser) {
		return R.data(postService.selectUserTree(orgId, Func.toStrWithEmpty(tenantId, szykUser.getTenantId())));
	}


	/**
	 * 角色数据
	 */
	@GetMapping("/role/list")
	@ApiOperation(value = "角色数据", notes = "无参数")
	public R<List<Role>> roleList() {
		List<Role> list = roleService.list(Wrappers.<Role>lambdaQuery().in(Role::getIsDeleted, 0));
		if (CollectionUtils.isEmpty(list)) {
			return R.data(new ArrayList<>());
		}
		return R.data(list);
	}

	/**
	 * 用户详情
	 */
	@GetMapping("/detail")
	@ApiOperation(value = "查看详情", notes = "传入id")
	public R<UserVO> detail(User user) {
		if (Func.equals(user.getId().toString(), "11111111")) {
			throw new ServiceException("自动通过的虚拟人员不支持查看详细信息");
		}

		User detail = userService.getOne(Condition.getQueryWrapper(user));
		return R.data(UserWrapper.build().entityVO(detail));
	}

}
