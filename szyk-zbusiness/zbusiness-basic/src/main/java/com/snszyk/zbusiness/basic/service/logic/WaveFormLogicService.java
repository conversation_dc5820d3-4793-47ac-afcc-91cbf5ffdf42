/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.service.logic;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.snszyk.common.utils.InfluxdbTools;
import com.snszyk.core.log.exception.ServiceException;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.utils.DateUtil;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.StringPool;
import com.snszyk.zbusiness.basic.config.SidasBizConfig;
import com.snszyk.zbusiness.basic.dto.SampleTimeDTO;
import com.snszyk.zbusiness.basic.dto.SensorDataDTO;
import com.snszyk.zbusiness.basic.dto.WaterfallPlotDTO;
import com.snszyk.zbusiness.basic.dto.WaveFormDTO;
import com.snszyk.zbusiness.basic.entity.Wave;
import com.snszyk.zbusiness.basic.enums.SampledDataTypeEnum;
import com.snszyk.zbusiness.basic.feign.PythonServerFeign;
import com.snszyk.zbusiness.basic.service.ISensorDataService;
import com.snszyk.zbusiness.basic.service.IWaveService;
import com.snszyk.zbusiness.basic.vo.CancelWaveMarkVO;
import com.snszyk.zbusiness.basic.vo.SensorDataVO;
import com.snszyk.zbusiness.basic.vo.WaveFormVO;
import com.snszyk.zbusiness.basic.vo.WaveVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 波形表 逻辑服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Slf4j
@Service
public class WaveFormLogicService {

	@Resource
	private IWaveService waveService;
	@Resource
	private ISensorDataService sensorDataService;
	@Resource
	private PythonServerFeign pythonServerFeign;
	@Resource
	private SzykRedis szykRedis;
	@Resource
	private InfluxdbTools influxdbTools;
	@Resource
	private SidasBizConfig sidasBizConfig;
	private final static DateTimeFormatter format = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

	private Long dateConvert(String date){
		return LocalDateTime.parse(date, format).toInstant(ZoneOffset.UTC).toEpochMilli();
	}
	/**
	 * 特征值波形
	 *
	 * <AUTHOR>
	 * @date 2023/02/16 16:56
	 * @param waveForm
	 */
	public WaveFormDTO characterWaveForm(WaveFormVO waveForm) {
		WaveFormDTO dto = new WaveFormDTO();
		if (Func.isNotEmpty(waveForm.getStartDate())) {
			waveForm.setStartDate(waveForm.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(waveForm.getEndDate())) {
			waveForm.setEndDate(waveForm.getEndDate() + " 23:59:59");
		}

        SensorDataVO sensorData = new SensorDataVO()
			.setMonitorId(waveForm.getMonitorId())
			.setShowWave(waveForm.getShowWave())
			.setStartTime(waveForm.getStartDate())
			.setEndTime(waveForm.getEndDate());
		sensorData.setWaveId(waveForm.getWaveId());
		if(waveForm.getShowWave() != null){
			sensorData.setHasWaveData(waveForm.getShowWave());
		}
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);

		List<JSONObject> list = influxdbTools.queryData(dateConvert(waveForm.getStartDate()),
			dateConvert(waveForm.getEndDate()), jsonObject);
		if(list.size() <= 0){
			return dto;
		}
		List<SensorDataDTO> datas = new ArrayList<>();
		list.forEach(item->datas.add(item.toJavaObject(SensorDataDTO.class)));
		dto.setDataList(datas);
		return dto;
	}

	public JSONObject mainWaveForm(WaveFormVO waveForm) {
		JSONObject jsonObject = initParam(waveForm);
		jsonObject.put("invalid","0");
		List<JSONObject> list = influxdbTools.queryDataTable(dateConvert(waveForm.getStartDate()),
			dateConvert(waveForm.getEndDate()), jsonObject,null,
			(sql)->{
				sql.getQuerySQL().append(" |>sort(columns: [\"_time\"], desc: true) ").append(" |>limit(n: 1000)");
				sql.addSort("_time", false).configLimit(1000);
			}, (item)->{
				JSONObject object = new JSONObject();
				object.put("waveId", waveForm.getWaveId());
				object.put("sampleDataType", item.getValueByKey("sampleDataType"));
				object.put("monitorId", item.getValueByKey("monitorId"));
				object.put("originTime", item.getTime().toEpochMilli());
				object.put("value", item.getValueByKey("value"));
				object.put("waveformUrl", item.getValueByKey("waveformUrl"));
				object.put("samplingPoints", item.getValueByKey("samplingPoints"));
				object.put("samplingFreq", item.getValueByKey("samplingFreq"));
				object.put("peakValue", item.getValueByKey("peakValue"));
				object.put("peakPeakValue", item.getValueByKey("peakPeakValue"));
				object.put("clearanceValue", item.getValueByKey("clearanceValue"));
				object.put("kurtosisValue", item.getValueByKey("kurtosisValue"));
				object.put("skewnessValue", item.getValueByKey("skewnessValue"));
				object.put("isMarked", item.getValueByKey("isMarked"));
				object.put("alarmLevel", item.getValueByKey("alarmLevel"));
				return object;
			});
		JSONObject result = new JSONObject();
		if(Func.isEmpty(list)){
			return result;
		}
		result.put("dataList", list);
		return result;
	}

	private JSONObject initParam(WaveFormVO waveForm){
		if (Func.isNotEmpty(waveForm.getStartDate())) {
			waveForm.setStartDate(waveForm.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(waveForm.getEndDate())) {
			waveForm.setEndDate(waveForm.getEndDate() + " 23:59:59");
		}

		SensorDataVO sensorData = new SensorDataVO()
			.setMonitorId(waveForm.getMonitorId())
			.setShowWave(waveForm.getShowWave())
			.setStartTime(waveForm.getStartDate())
			.setEndTime(waveForm.getEndDate());
		sensorData.setWaveId(waveForm.getWaveId());
		if(waveForm.getShowWave() != null){
			sensorData.setHasWaveData(waveForm.getShowWave());
		}
		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(sensorData);
		return jsonObject;
	}
	/**
	 * 特征图数据
	 */
	public List<JSONObject> waveForm(WaveFormVO waveForm){
		JSONObject jsonObject = initParam(waveForm);

		List<JSONObject> list = influxdbTools.queryDataTable(dateConvert(waveForm.getStartDate()),
			dateConvert(waveForm.getEndDate()), jsonObject,null,(sql)->sql.addSort("_time", false), (item)->{
				JSONObject obj = new JSONObject();
				obj.put("x", item.getTime().toEpochMilli());
				obj.put("y", item.getValueByKey("value"));
				obj.put("url", item.getValueByKey("waveformUrl"));
				if(item.getValueByKey("alarmLevel") != null){
					JSONObject alarm = new JSONObject();
					alarm.put("symbol","diamond");
					alarm.put("radius","6");
					alarm.put("symbol","lineWidth");
					obj.put("marker",alarm);
				}
				return obj;
			});
		return list;
	}

	/**
	 * 根据波形id查询采样时间
	 *
	 */
	public IPage<SampleTimeDTO> sampleTimeByWave(IPage<SampleTimeDTO> page,SensorDataVO vo) {
		return sensorDataService.sampleTimePageByWave(page, vo);
	}

	/**
	 * 瀑布图频域波形
	 *
	 * <AUTHOR>
	 * @date 2023/04/17 15:01
	 * @param originTimes 多个传感器数据的时间，用英文','分隔
	 * @param waveId 波形id
	 */
	public WaterfallPlotDTO waterfallPlot(String originTimes, String waveId, String monitorId) {
		WaterfallPlotDTO dto = new WaterfallPlotDTO();

		// 瀑布图
		List<String> originTimeList = Func.toStrList(originTimes);
		JSONArray originTimeArray = new JSONArray();
		originTimeArray.addAll(originTimeList);
		List<List<BigDecimal>> waterfall = pythonServerFeign.waterfall(originTimeArray.toJSONString(), waveId, monitorId);
		if (Func.isNotEmpty(waterfall)) {
			dto.setDataList(waterfall);
		} else {
			log.warn("获取瀑布图数据失败 - waterfall()，请求参数：origin_time = {}, waveId = {}, monitorId = {}",
				originTimeArray.toJSONString(), waveId, monitorId);
		}

		// 时间
		List<Date> dateList = originTimeList.stream()
			.map(time -> DateUtil.parse(time, DateUtil.PATTERN_DATETIME))
			.collect(Collectors.toList());
		dto.setDateList(dateList);

		return dto;
	}


	/**
	 * 根据测点或波形（二选一）获取振动数据类型下拉
	 *
	 * <AUTHOR>
	 * @date 2023/07/25 16:56
	 * @param monitorId 测点id
	 * @param waveId 波形id
	 * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
	 */
	public List<Map<String, String>> getVibrationDataType(Long monitorId, Long waveId) {
		List<Map<String, String>> list = new ArrayList<>();

		// 点击到测点查看特征图谱
		if (Func.isNotEmpty(monitorId)) {
			List<Wave> waveList = waveService.list(Wrappers.<Wave>lambdaQuery().eq(Wave::getMonitorId, monitorId));
			if (Func.isNotEmpty(waveList)) {
				List<String> sampleDataTypeList = waveList.stream().map(Wave::getSampleDataType).distinct().collect(Collectors.toList());
				if (Func.isNotEmpty(sampleDataTypeList)) {
					sampleDataTypeList.forEach(sampleDataType -> {
						Map<String, String> map = new HashMap<>(16);
						if (SampledDataTypeEnum.VELOCITY == SampledDataTypeEnum.getByCode(sampleDataType)) {
							map.put("label", SampledDataTypeEnum.VELOCITY.getName());
							map.put("value", SampledDataTypeEnum.VELOCITY.getCode());
							list.add(map);
						}
						if (SampledDataTypeEnum.ACCELERATION == SampledDataTypeEnum.getByCode(sampleDataType)) {
							map = new HashMap<>(16);
							map.put("label", SampledDataTypeEnum.ACCELERATION.getName());
							map.put("value", SampledDataTypeEnum.ACCELERATION.getCode());
							list.add(map);
						}
						if (SampledDataTypeEnum.DISPLACEMENT == SampledDataTypeEnum.getByCode(sampleDataType)) {
							map = new HashMap<>(16);
							map.put("label", SampledDataTypeEnum.DISPLACEMENT.getName());
							map.put("value", SampledDataTypeEnum.DISPLACEMENT.getCode());
							list.add(map);
						}
					});
				}
			}
		}

		// 点击到波形查看特征图谱
		if (Func.isNotEmpty(waveId)) {
			Wave wave = waveService.getById(waveId);
			Map<String, String> map = new HashMap<>(16);
			if (SampledDataTypeEnum.VELOCITY == SampledDataTypeEnum.getByCode(wave.getSampleDataType())) {
				map.put("label", SampledDataTypeEnum.VELOCITY.getName());
				map.put("value", SampledDataTypeEnum.VELOCITY.getCode());
			}
			if (SampledDataTypeEnum.ACCELERATION == SampledDataTypeEnum.getByCode(wave.getSampleDataType())) {
				map.put("label", SampledDataTypeEnum.ACCELERATION.getName());
				map.put("value", SampledDataTypeEnum.ACCELERATION.getCode());
			}
			if (SampledDataTypeEnum.DISPLACEMENT == SampledDataTypeEnum.getByCode(wave.getSampleDataType())) {
				map.put("label", SampledDataTypeEnum.DISPLACEMENT.getName());
				map.put("value", SampledDataTypeEnum.DISPLACEMENT.getCode());
			}
			list.add(map);
		}
		return list;
	}

	/**
	 * 特征图谱：查询波形某种振动数据类型（加速度、速度、位移）的6种通用指标数据
	 *
	 * <AUTHOR>
	 * @date 2023/07/18 13:56
	 * @param waveForm
	 */
	public WaveFormDTO specificChromatogram(WaveFormVO waveForm) {
		WaveFormDTO dto = new WaveFormDTO();
		if (Func.isNotEmpty(waveForm.getStartDate())) {
			waveForm.setStartDate(waveForm.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(waveForm.getEndDate())) {
			waveForm.setEndDate(waveForm.getEndDate() + " 23:59:59");
		}

		// 查询特征值列表
		List<SensorDataDTO> sensorDataList = sensorDataService.specificChromatogram(waveForm);
		dto.setCharacterDataList(sensorDataList);
		return dto;
	}

	/**
	 * 时域和频谱
	 * @param vo 请求参数
	 * @return
	 */
	public WaveFormDTO timeAndFreqWave(SensorDataVO vo) {
		WaveFormDTO dto = new WaveFormDTO();
		// 频谱波形
		List<BigDecimal> freqDomainWave = pythonServerFeign.freqDomainWave(DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
			vo.getWaveId().toString(), vo.getMonitorId().toString());
		if (Func.isNotEmpty(freqDomainWave)) {
			dto.setFreqDomainWaveForm(freqDomainWave);
		} else {
			log.warn("获取频域波形失败 - freqDomainWave()，请求参数：origin_time = {}, waveId = {}, monitorId = {}",
				DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
				vo.getWaveId().toString(), vo.getMonitorId().toString());
		}

		return dto;
	}

	/**
	 * 频谱和包络图谱
	 * @param vo 请求参数
	 * @return
	 */
	public WaveFormDTO freqAndEnvelope(SensorDataVO vo) {
		WaveFormDTO dto = new WaveFormDTO();
		// 调用Python服务获取频谱和包络
		JSONObject freqAndEnvelope = pythonServerFeign.freqAndEnvelope(DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
			vo.getWaveId().toString(), vo.getMonitorId().toString());
		if (freqAndEnvelope != null) {
			JSONArray freq = freqAndEnvelope.getJSONArray("freq");
			if (Func.isNotEmpty(freq)) {
				dto.setFreqDomainWaveForm(Arrays.stream(freq.toArray()).map(Object::toString).map(BigDecimal::new).collect(Collectors.toList()));
			}
			JSONArray envelope = freqAndEnvelope.getJSONArray("envelope");
			if (Func.isNotEmpty(envelope)) {
				dto.setEnvelopeWaveForm(Arrays.stream(envelope.toArray()).map(Object::toString).map(BigDecimal::new).collect(Collectors.toList()));
			}
		} else {
			log.warn("获取频谱、包络图谱失败 - freqAndEnvelope()，请求参数：origin_time = {}, waveId = {}, monitorId = {}",
				DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
				vo.getWaveId().toString(), vo.getMonitorId().toString());
		}
		return dto;
	}

	/**
	 * Bode图谱
	 * @param vo 请求参数
	 * @return
	 */
	public WaveFormDTO bodePlot(SensorDataVO vo) {
		WaveFormDTO dto = new WaveFormDTO();
		JSONObject bodeResult = pythonServerFeign.bode(DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
			vo.getWaveId().toString(), vo.getMonitorId().toString());
		if (bodeResult != null) {
			JSONArray bode = bodeResult.getJSONArray("bode");
			if (Func.isNotEmpty(bode)) {
				dto.setFreqDomainWaveForm(Arrays.stream(bode.toArray()).map(Object::toString).map(BigDecimal::new).collect(Collectors.toList()));
			}
			JSONArray angle = bodeResult.getJSONArray("angle");
			if (Func.isNotEmpty(angle)) {
				dto.setFreqPhaseWaveForm(Arrays.stream(angle.toArray()).map(Object::toString).map(BigDecimal::new).collect(Collectors.toList()));
			}
			JSONArray freq = bodeResult.getJSONArray("freq");
			if (Func.isNotEmpty(freq)) {
				dto.setBodeFreqX(Arrays.stream(freq.toArray()).map(Object::toString).map(BigDecimal::new).collect(Collectors.toList()));
			}
		} else {
			log.warn("获取Bode图谱失败 - bode()，请求参数：origin_time = {}, waveId = {}, monitorId = {}",
				DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
				vo.getWaveId().toString(), vo.getMonitorId().toString());
		}
		return dto;
	}

	/**
	 * 倒谱图谱
	 * @param vo 请求参数
	 * @return
	 */
	public WaveFormDTO cepstrumPlot(SensorDataVO vo) {
		WaveFormDTO result = new WaveFormDTO();
		List<BigDecimal> wave = pythonServerFeign.wave(DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
			vo.getWaveId().toString(), PythonServerFeign.WAVE_TYPE_CEPSTRUM, vo.getMonitorId().toString());
		if (Func.isNotEmpty(wave)) {
			result.setCepstrumWaveForm(wave);
		} else {
			log.warn("获取倒谱图谱失败 - wave()，请求参数：origin_time = {}, waveId = {}, waveType = {}, monitorId = {}",
				DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
				vo.getWaveId().toString(), PythonServerFeign.WAVE_TYPE_CEPSTRUM, vo.getMonitorId().toString());
		}
		return result;
	}

	/**
	 * 相位图谱
	 * @param vo 请求参数
	 * @return
	 */
	public WaveFormDTO phasePlot(SensorDataVO vo) {
		WaveFormDTO result = new WaveFormDTO();
		List<BigDecimal> wave = pythonServerFeign.wave(DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
			vo.getWaveId().toString(), PythonServerFeign.WAVE_TYPE_FREQ_PHASE, vo.getMonitorId().toString());
		if (Func.isNotEmpty(wave)) {
			result.setFreqPhaseWaveForm(wave);
		} else {
			log.warn("获取相位图谱失败 - wave()，请求参数：origin_time = {}, waveId = {}, waveType = {}, monitorId = {}",
				DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
				vo.getWaveId().toString(), PythonServerFeign.WAVE_TYPE_FREQ_PHASE, vo.getMonitorId().toString());
		}
		return result;
	}

	/**
	 * 速度图谱
	 * @param vo 请求参数
	 * @return
	 */
	public WaveFormDTO velocityPlot(SensorDataVO vo) {
		WaveFormDTO result = new WaveFormDTO();
		List<BigDecimal> wave = pythonServerFeign.wave(DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
			vo.getWaveId().toString(), PythonServerFeign.WAVE_TYPE_VELOCITY, vo.getMonitorId().toString());
		if (Func.isNotEmpty(wave)) {
			result.setVelocityWaveForm(wave);
		} else {
			log.warn("获取速度图谱失败 - wave()，请求参数：origin_time = {}, waveId = {}, waveType = {}, monitorId = {}",
				DateUtil.format(vo.getOriginTime(), DateUtil.PATTERN_DATETIME),
				vo.getWaveId().toString(), PythonServerFeign.WAVE_TYPE_VELOCITY, vo.getMonitorId().toString());
		}
		return result;
	}

	/**
	 * 趋势图（增值、增幅变化波形）
	 * @param startDate 开始时间
	 * @param endDate 结束时间
	 * @param waveId 波形id
	 * @param monitorId 测点id
	 * @return
	 */
	public JSONObject trendPlot(String startDate, String endDate, String waveId, String monitorId) {
		JSONObject jsonObject = pythonServerFeign.increase(startDate, endDate, waveId, monitorId);
		if (jsonObject != null) {
			return jsonObject;
		} else {
			log.warn("获取趋势图（增值、增幅变化波形）谱失败 - increase()，请求参数：begin_time = {}, end_time = {}, " +
				"wave_config_id = {}, monitorId = {}", startDate, endDate, waveId, monitorId);
			return null;
		}
	}

	/**
	 * 有效值趋势图标注
	 *
	 * @param waveId
	 * @param originTime
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/1/30 14:36
	 */
	public boolean waveMark(Long waveId, String originTime, Integer showWave) {
		SensorDataVO query = new SensorDataVO();
		query.setWaveId(waveId);
		JSONObject queryObject = (JSONObject) JSONObject.toJSON(query);
		Date date = DateUtil.parse(originTime, DateUtil.PATTERN_DATETIME);
		Long dateTime = date.getTime();
		List<JSONObject> list = influxdbTools.queryData(dateTime, dateTime + 1000L, queryObject);
		list.forEach(item->{
			SensorDataDTO dto = item.toJavaObject(SensorDataDTO.class);
			dto.setIsMarked(1);
			influxdbTools.update(Func.toStr(dto.getMonitorId()), Func.toStr(waveId), (JSONObject) JSONObject.toJSON(dto), dto.getOriginTime().getTime(),Func.isNotEmpty(dto.getWaveformUrl()));
		});

		return Boolean.TRUE;
	}

	/**
	 * 取消有效值趋势图标注
	 *
	 * @param waveId
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/1/30 14:36
	 */
//	public boolean cancelWaveMark(Long waveId, String startTime, String endTime) {
//		Wave wave = waveService.getById(waveId);
//		SensorDataVO query = new SensorDataVO();
//		query.setWaveId(waveId).setSampleDataType(wave.getSampleDataType());
//		JSONObject queryObject = (JSONObject) JSONObject.toJSON(query);
//		startTime = startTime + " 00:00:00";
//		endTime = endTime + " 23:59:59";
//		Date startDate = DateUtil.parse(startTime, DateUtil.PATTERN_DATETIME);
//		Date endDate = DateUtil.parse(endTime, DateUtil.PATTERN_DATETIME);
//		List<JSONObject> list = influxdbTools.queryData(startDate.getTime(), endDate.getTime(), queryObject);
//		List<SensorDataDTO> sensorDataList = new ArrayList<>();
//		list.forEach(item -> sensorDataList.add(item.toJavaObject(SensorDataDTO.class)));
//		if(Func.isNotEmpty(sensorDataList)){
//			sensorDataList.forEach(sensorData -> {
//				sensorData.setIsMarked(0);
//				JSONObject updateObject = (JSONObject) JSONObject.toJSON(sensorData);
//				influxdbTools.update(null, Func.toStr(waveId), updateObject, sensorData.getOriginTime().getTime());
//			});
//		}
//		return Boolean.TRUE;
//	}
	public boolean cancelWaveMark(CancelWaveMarkVO vo) {
		vo.getOriginTimes().forEach(action->{
			Date startDate = DateUtil.parse(action, DateUtil.PATTERN_DATETIME);
			vo.setOriginTime(startDate);
			List<JSONObject> list = influxdbTools.queryData(startDate.getTime(), startDate.getTime() + 1000L, (JSONObject) JSONObject.toJSON(vo));
			list.forEach(data -> {
				SensorDataDTO dto = data.toJavaObject(SensorDataDTO.class);
				dto.setIsMarked(0);
				influxdbTools.insert(Func.toStr(dto.getMonitorId()), Func.toStr(dto.getWaveId()), (JSONObject) JSONObject.toJSON(dto), dto.getOriginTime().getTime());
				if(Func.isNotEmpty(dto.getWaveformUrl())){
					influxdbTools.insert(Func.toStr(dto.getMonitorId()), String.format("%s_1",vo.getWaveId()), (JSONObject) JSONObject.toJSON(dto), dto.getOriginTime().getTime());
				}
			});
		});
		return  true;
	}

	/**
	 * 获取停机线
	 *
	 * @param waveId
	 * @return
	 */
	public BigDecimal getShutdownLine(Long waveId) {
		Wave wave = waveService.getById(waveId);
		if(wave == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		if(Func.isNotEmpty(wave.getHaltLine())){
			return wave.getHaltLine();
		}
		return BigDecimal.valueOf(sidasBizConfig.sidasBizProperties().getRunningStateMin());
	}

	/**
	 * 获取能量图
	 * @return
	 */
	public List<JSONObject> queryEnergyList(WaveFormVO waveForm) {
		if (Func.isNotEmpty(waveForm.getStartDate())) {
			waveForm.setStartDate(waveForm.getStartDate() + " 00:00:00");
		}
		if (Func.isNotEmpty(waveForm.getEndDate())) {
			waveForm.setEndDate(waveForm.getEndDate() + " 23:59:59");
		}

		JSONObject jsonObject = (JSONObject) JSONObject.toJSON(waveForm);
		List<JSONObject> list = influxdbTools.queryDataTable(dateConvert(waveForm.getStartDate()),
			dateConvert(waveForm.getEndDate()), jsonObject,null,(sql)->sql.addSort("_time", false),(item)->{
				JSONObject object = new JSONObject();
				object.put("originTime", item.getTime().toEpochMilli());
				Assert.notNull(item.getValueByKey("energyValue"),"能量值为空");
				object.put("energyValue", item.getValueByKey("energyValue"));
				return object;
			});

		return list;

	}

	/**
	 * 获取波形停机线
	 *
	 * @param waveId
	 * @return BigDecimal
	 * <AUTHOR>
	 * @date 2024/4/30 11:51
	 */
	public BigDecimal getHaltLine(Long waveId){
		JSONObject jsonObject = pythonServerFeign.getStopLineByWaveId(Func.toStr(waveId));
		if(Func.isNotEmpty(jsonObject)){
			return BigDecimal.valueOf(jsonObject.getDoubleValue("stopLine"));
		}
		return null;
	}

	/**
	 * 设置波形停机线
	 *
	 * @param vo
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/4/30 11:51
	 */
	public boolean setHaltLine(WaveVO vo){
		Wave wave = waveService.getById(vo.getId());
		if(wave == null){
			throw new ServiceException(ResultCode.FAILURE);
		}
		wave.setHaltLine(vo.getHaltLine());
		// 停机线缓存Redis（只针对加速度波形）
		if(SampledDataTypeEnum.ACCELERATION == SampledDataTypeEnum.getByCode(wave.getSampleDataType())){
			String haltLineKey = wave.getSensorCode() + StringPool.COLON + "HALT-LINE";
			szykRedis.set(haltLineKey, wave.getHaltLine());
		}
		return waveService.updateById(wave);
	}

}
