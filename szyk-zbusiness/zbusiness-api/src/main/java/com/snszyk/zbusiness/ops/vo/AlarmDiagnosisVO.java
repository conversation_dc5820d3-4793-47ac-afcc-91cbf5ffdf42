/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 报警诊断结论视图实体类
 *
 * <AUTHOR>
 * @since 2022-12-13
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AlarmDiagnosisVO对象", description = "报警诊断结论")
public class AlarmDiagnosisVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "主键")
	private Long id;

	/**
	 * 指标
	 */
	@ApiModelProperty(value = "指标")
	private String alarmIndex;

	/**
	 * 诊断结论
	 */
	@ApiModelProperty(value = "诊断结论", required = true)
	private String conclusion;

	/**
	 * 检维修建议
	 */
	@ApiModelProperty(value = "检维修建议")
	private String suggestion;

	/**
	 * 诊断类型
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "诊断类型（字典：diagnosis_type）")
	private Integer diagnosisType;

	/**
	 * 诊断时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "诊断时间")
	private Date diagnoseTime;

	/**
	 * 诊断人
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "诊断人")
	private Long diagnoseUser;

	/**
	 * 诊断人姓名
	 */
	@ApiModelProperty(value = "诊断人姓名")
	private String diagnoseUserName;

}
