/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 非振动采样数据类型 枚举类
 *
 * <AUTHOR>
 * @date 2023/07/07 15:56
 **/
@Getter
@AllArgsConstructor
public enum NonVibrationDataEnum {

	/**
	 * 有效值
	 */
	EFFECTIVE_VALUE("MM000", "有效值"),

	/**
	 * 峰值
	 */
	PEAK_VALUE("MMN01", "峰值"),

	/**
	 * 峰峰值
	 */
	PEAK_PEAK_VALUE("MMN02", "峰峰值"),

	/**
	 * 裕度
	 */
	CLEARANCE_FACTOR("MMN03", "裕度"),

	/**
	 * 歪度
	 */
	SKEWNESS_VALUE("MMN04", "歪度"),

	/**
	 * 峭度
	 */
	KURTOSIS_VALUE("MMN05", "峭度");

	final String code;
	final String name;

	public static NonVibrationDataEnum getByCode(String code){
		for (NonVibrationDataEnum value : NonVibrationDataEnum.values()) {
			if (code.equals(value.getCode())){
				return value;
			}
		}
		return null;
	}

}
