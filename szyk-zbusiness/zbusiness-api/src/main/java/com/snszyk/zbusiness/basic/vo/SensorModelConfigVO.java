/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 3D模型配置表 视图实体类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Data
@ApiModel(value = "SensorModelConfigVO对象", description = "3D模型配置表")
public class SensorModelConfigVO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 设备id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "设备id")
	private Long equipmentId;

	/**
	 * 传感器测点列表
	 */
	@ApiModelProperty(value = "传感器测点列表")
	private List<SensorMonitorConfigVO> sensorMonitorConfigList;

	/**
	 * 分类
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "分类")
	private Integer category;

	public SensorModelConfigVO(){

	}

	/**
	 * 构造方法
	 *
	 * @param equipmentId
	 */
	public SensorModelConfigVO(Long equipmentId){
		this.equipmentId = equipmentId;
	}

}
