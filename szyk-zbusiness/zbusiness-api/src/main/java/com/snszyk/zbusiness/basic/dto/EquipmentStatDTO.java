package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class EquipmentStatDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "设备总数")
	private Integer totalCount;

	@ApiModelProperty(value = "运行设备数")
	private Integer runningCount;

	@ApiModelProperty(value = "运行百分比")
	private BigDecimal runningPercent;

	@ApiModelProperty(value = "停机设备数")
	private Integer shutdownCount;

	@ApiModelProperty(value = "停机百分比")
	private BigDecimal shutdownPercent;
	/**
	 * 正常数量
	 */
	@ApiModelProperty(value = "正常数量")
	private Integer normalCount;
	@ApiModelProperty(value = "正常百分比")
	private BigDecimal normalPercent;
	/**
	 * 异常数量
	 */
	@ApiModelProperty(value = "异常数量")
	private Integer abNormalCount;
	@ApiModelProperty(value = "异常百分比")
	private BigDecimal abNormalPercent;


	public EquipmentStatDTO init() {
		this.totalCount = 0;
		this.normalCount = 0;
		this.abNormalCount = 0;
		this.normalPercent = BigDecimal.ZERO;
		this.abNormalPercent = BigDecimal.ZERO;
		return this;
	}
}
