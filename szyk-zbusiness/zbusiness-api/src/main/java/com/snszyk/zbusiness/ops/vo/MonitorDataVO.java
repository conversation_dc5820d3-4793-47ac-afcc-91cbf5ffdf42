/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 监测数据
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@Data
@ApiModel(value = "MonitorDataVO对象", description = "MonitorDataVO")
public class MonitorDataVO implements Serializable {

	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "传感器id")
	private Long id;

	@ApiModelProperty(value = "采样时间")
	private Long time;

	@ApiModelProperty(value = "采样点数")
	private Integer number;

	@ApiModelProperty(value = "查询类型(0:all, 1:single)")
	private String type;

	public MonitorDataVO(Long id, Long time, Integer number) {
		this.id = id;
		this.time = time;
		this.number = number;
	}

}
