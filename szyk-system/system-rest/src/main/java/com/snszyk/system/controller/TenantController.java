/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.common.constant.CustomRoleConstant;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.cache.utils.CacheUtil;
import com.snszyk.core.launch.constant.AppConstant;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.annotation.PreAuth;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tenant.annotation.NonDS;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.api.ResultCode;
import com.snszyk.core.tool.constant.RoleConstant;
import com.snszyk.core.tool.constant.SzykConstant;
import com.snszyk.core.tool.support.Kv;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.core.tool.utils.ObjectUtil;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.system.cache.SysCache;
import com.snszyk.system.entity.Tenant;
import com.snszyk.system.entity.TenantPackage;
import com.snszyk.system.service.ITenantPackageService;
import com.snszyk.system.service.ITenantService;
import com.snszyk.system.vo.DelResultVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.snszyk.core.cache.constant.CacheConstant.SYS_CACHE;
import static com.snszyk.core.tenant.constant.TenantBaseConstant.TENANT_DATASOURCE_CACHE;
import static com.snszyk.core.tenant.constant.TenantBaseConstant.TENANT_DATASOURCE_EXIST_KEY;

/**
 * 控制器
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping(AppConstant.APPLICATION_SYSTEM_NAME + "/tenant")
@Api(value = "租户管理", tags = "租户管理")
public class TenantController extends SzykController {

	private final ITenantService tenantService;
	private final ITenantPackageService tenantPackageService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入tenant")
	@PreAuth(CustomRoleConstant.HAS_ROLE_PLATFORM_ADMIN)
	public R<Tenant> detail(Tenant tenant) {
		Tenant detail = tenantService.getOne(Condition.getQueryWrapper(tenant));
		if (ObjectUtil.isNotEmpty(detail) && ObjectUtil.isNotEmpty(detail.getIndustry())) {
			detail.setIndustryName(DictBizCache.getValue(DictBizEnum.INDUSTRY, detail.getIndustry()));
		}
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "tenantId", value = "参数名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "tenantName", value = "角色别名", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "contactNumber", value = "联系电话", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入tenant")
	@PreAuth(CustomRoleConstant.HAS_ROLE_PLATFORM_ADMIN)
	public R<IPage<Tenant>> list(@ApiIgnore @RequestParam Map<String, Object> tenant, Query query, SzykUser szykUser) {
		QueryWrapper<Tenant> queryWrapper = Condition.getQueryWrapper(tenant, Tenant.class);
		IPage<Tenant> pages = tenantService.page(Condition.getPage(query), (!szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(Tenant::getTenantId, szykUser.getTenantId()) : queryWrapper);
		if (ObjectUtil.isNotEmpty(pages) && ObjectUtil.isNotEmpty(pages.getRecords())) {
			pages.getRecords().forEach(record -> {
				Optional.ofNullable(record.getIndustry())
					.map(industry -> DictBizCache.getValue(DictBizEnum.INDUSTRY, industry))
					.ifPresent(record::setIndustryName);
			});
		}
		return R.data(pages);
	}

	/**
	 * 下拉数据源
	 */
	@GetMapping("/select")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "下拉数据源", notes = "传入tenant")
	@PreAuth(CustomRoleConstant.HAS_ROLE_PLATFORM_ADMIN)
	public R<List<Tenant>> select(Tenant tenant, SzykUser szykUser) {
		QueryWrapper<Tenant> queryWrapper = Condition.getQueryWrapper(tenant);
		List<Tenant> list = tenantService.list((!szykUser.getTenantId().equals(SzykConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(Tenant::getTenantId, szykUser.getTenantId()) : queryWrapper);
		return R.data(list);
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "分页", notes = "传入tenant")
	@PreAuth(CustomRoleConstant.HAS_ROLE_PLATFORM_ADMIN)
	public R<IPage<Tenant>> page(Tenant tenant, Query query) {
		IPage<Tenant> pages = tenantService.selectTenantPage(Condition.getPage(query), tenant);
		return R.data(pages);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "新增或修改", notes = "传入tenant")
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	public R submit(@Valid @RequestBody Tenant tenant) {
		return R.status(tenantService.submitTenant(tenant));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(tenantService.removeTenant(Func.toLongList(ids)));
	}

	/**
	 * 删除
	 */
	@PostMapping("/check-remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "校验并删除", notes = "传入ids")
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	public R<DelResultVO> checkAndRemove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		CacheUtil.clear(SYS_CACHE, Boolean.FALSE);

		DelResultVO delResultVO = tenantService.checkAndRemoveTenant(Func.toLongList(ids));
		R<DelResultVO> result = new R<>();
		result.setCode(ResultCode.SUCCESS.getCode());
		result.setData(delResultVO);
		result.setSuccess(delResultVO.getFailureNumber() == 0);
		return result;
	}


	/**
	 * 授权配置
	 */
	@PostMapping("/setting")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "授权配置", notes = "传入ids,accountNumber,expireTime")
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	public R setting(@ApiParam(value = "主键集合", required = true) @RequestParam String ids, @ApiParam(value = "账号额度") Integer accountNumber, @ApiParam(value = "过期时间") Date expireTime) {
		return R.status(tenantService.setting(accountNumber, expireTime, ids));
	}

	/**
	 * 数据源配置
	 */
	@PostMapping("datasource")
	@ApiOperationSupport(order = 8)
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	@ApiOperation(value = "数据源配置", notes = "传入datasource_id")
	public R datasource(@ApiParam(value = "租户ID", required = true) @RequestParam String tenantId, @ApiParam(value = "数据源ID", required = true) @RequestParam Long datasourceId) {
		CacheUtil.evict(TENANT_DATASOURCE_CACHE, TENANT_DATASOURCE_EXIST_KEY, tenantId, Boolean.FALSE);
		return R.status(tenantService.updateAndRefresh(tenantId, datasourceId));
	}

	/**
	 * 根据名称查询列表
	 *
	 * @param name 租户名称
	 */
	@GetMapping("/find-by-name")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "详情", notes = "传入tenant")
	@PreAuth(CustomRoleConstant.HAS_ROLE_PLATFORM_ADMIN)
	public R<List<Tenant>> findByName(String name) {
		List<Tenant> list = tenantService.list(Wrappers.<Tenant>query().lambda().like(Tenant::getTenantName, name));
		return R.data(list);
	}

	/**
	 * 根据域名查询信息
	 *
	 * @param domain 域名
	 */
	@GetMapping("/info")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "配置信息", notes = "传入domain")
	public R<Kv> info(String domain) {
		Tenant tenant = tenantService.getOne(Wrappers.<Tenant>query().lambda().eq(Tenant::getDomain, domain));
		Kv kv = Kv.create();
		if (tenant != null) {
			kv.set("tenantId", tenant.getTenantId())
				.set("domain", tenant.getDomain())
				.set("backgroundUrl", tenant.getBackgroundUrl());
		}
		return R.data(kv);
	}

	/**
	 * 根据租户ID查询产品包详情
	 *
	 * @param tenantId 租户ID
	 */
	@GetMapping("/package-detail")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "产品包详情", notes = "传入tenantId")
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	public R<TenantPackage> packageDetail(Long tenantId) {
		Tenant tenant = tenantService.getById(tenantId);
		return R.data(tenantPackageService.getById(tenant.getPackageId()));
	}

	/**
	 * 产品包配置
	 */
	@PostMapping("/package-setting")
	@ApiOperationSupport(order = 12)
	@PreAuth(RoleConstant.HAS_ROLE_ADMINISTRATOR)
	@ApiOperation(value = "产品包配置", notes = "传入packageId")
	public R packageSetting(@ApiParam(value = "租户ID", required = true) @RequestParam String tenantId, @ApiParam(value = "产品包ID") Long packageId) {
		return R.status(tenantService.update(Wrappers.<Tenant>update().lambda().set(Tenant::getPackageId, packageId).eq(Tenant::getTenantId, tenantId)));
	}

	/**
	 * 保存选中租户id
	 */
	@PostMapping("/biz-select")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "保存选中租户至缓存", notes = "传入tenantId")
	public R saveTenantSelect(@ApiParam(value = "用户选择查询的租户ID", required = true) @RequestParam String tenantId) {
		SysCache.saveTenantSelect(tenantId);
		return R.status(true);
	}

	/**
	 * 查询用户选中租户id
	 */
	@GetMapping("/biz-select")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "查询用户上次选中租户", notes = "传入tenantId")
	public R<Tenant> getTenantSelect() {
		return R.data(SysCache.getTenantSelect(AuthUtil.getTenantId()));
	}


}
