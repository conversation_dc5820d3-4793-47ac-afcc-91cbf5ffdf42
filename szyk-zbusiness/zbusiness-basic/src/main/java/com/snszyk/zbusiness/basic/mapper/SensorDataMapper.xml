<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.snszyk.zbusiness.basic.mapper.SensorDataMapper">

    <select id="queryCharacterData" resultType="com.snszyk.zbusiness.basic.dto.SensorDataDTO">
        SELECT id, wave_id, sensor_code, sample_data_type, axis_direction, measure_direction,
               sampling_freq, sampling_points, `value`, rms_value, peak_value, peak_peak_value,
               clearance_value, skewness_value, kurtosis_value, origin_time, invalid,
               invalid_reason, is_marked, alarm_level, create_time
        FROM sidas_sensor_data
        <where>
            wave_id = #{waveId} AND invalid = 0
            <if test="showWave==0">
                AND time_domain_waveform IS NOT NULL
            </if>
            <if test="startTime != null">
                AND origin_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND origin_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY origin_time
    </select>

    <select id="queryTimeDomainWave" resultType="java.lang.String">
        SELECT time_domain_waveform timeDomainWaveform
        FROM sidas_sensor_data
        WHERE invalid = 0 AND wave_id = #{waveId} AND origin_time = #{originTime}
    </select>

    <select id="page" resultType="com.snszyk.zbusiness.basic.dto.SensorDataDTO">
        SELECT ss.id, ss.wave_id, ss.sensor_code, ss.sample_data_type, ss.axis_direction, ss.measure_direction,
        ss.sampling_freq, ss.sampling_points, ss.`value`, ss.rms_value, ss.peak_value, ss.peak_peak_value,
        ss.clearance_value, ss.skewness_value, ss.kurtosis_value, ss.origin_time, ss.invalid,
        ss.invalid_reason, ss.is_marked, ss.alarm_level, ss.create_time,
        bt.category, if(ss.time_domain_waveform is null, 0, 1) as has_wave_data
        FROM sidas_sensor_data as ss
        left join basic_sensor_instance as bs on ss.sensor_code=bs.code AND bs.is_deleted=0
        left join basic_sensor_type as bt on bs.type_id=bt.id AND bt.is_deleted=0
        <where>
            ss.invalid = 0
            <if test="vo.invalid != null">
                AND ss.invalid = #{vo.invalid}
            </if>
            <if test="vo.startTime != null">
                AND ss.origin_time &gt;= #{vo.startTime}
            </if>
            <if test="vo.endTime != null">
                AND ss.origin_time &lt;= #{vo.endTime}
            </if>
            <if test="vo.sensorCode != null">
                AND ss.sensor_code = #{vo.sensorCode}
            </if>
            <if test="vo.category != null">
                AND bt.category = #{vo.category}
            </if>
            <if test="vo.sampleDataType != null">
                AND ss.sample_data_type = #{vo.sampleDataType}
            </if>
            <if test="vo.hasWaveData != null and vo.hasWaveData==1">
                and ss.time_domain_waveform is not null
            </if>
            <if test="vo.hasWaveData != null and vo.hasWaveData==0">
                and ss.time_domain_waveform is null
            </if>
        </where>
        ORDER BY ss.origin_time DESC
    </select>

    <select id="sampleTimeByWave" resultType="com.snszyk.zbusiness.basic.dto.SampleTimeDTO">
        SELECT
            id,
            origin_time as sample_time,
            sampling_freq as sampling_freq,
            sampling_points as sampling_points,
            `value` as sample_value,
            concat(sampling_freq/1000, 'K') as data_length
        FROM sidas_sensor_data
        WHERE wave_id = #{waveId} and invalid = 0
        ORDER BY origin_time DESC
    </select>

    <select id="queryCommonCharacters" resultType="com.snszyk.zbusiness.basic.dto.SensorDataDTO">
        SELECT `value`, origin_time, alarm_level
        FROM sidas_sensor_data
        WHERE invalid = 0
        <if test="waveId!=null">
            AND wave_id = #{waveId}
        </if>
        <if test="dataType!=null">
            AND sample_data_type = #{dataType}
        </if>
        <if test="startDate!=null">
            and origin_time <![CDATA[ >= ]]> #{startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="endDate!=null">
            and origin_time <![CDATA[ <= ]]> #{endDate, jdbcType=TIMESTAMP}
        </if>
        ORDER BY origin_time
    </select>

    <select id="specificChromatogram" resultType="com.snszyk.zbusiness.basic.dto.SensorDataDTO">
        SELECT id, wave_id, sensor_code, sample_data_type, axis_direction, measure_direction,
        sampling_freq, sampling_points, `value`, rms_value, peak_value, peak_peak_value,
        clearance_value, skewness_value, kurtosis_value, origin_time, invalid,
        invalid_reason, is_marked, alarm_level, create_time
        FROM sidas_sensor_data
        <where>
            wave_id = #{waveId} AND invalid = 0
            <if test="showWave==0">
                AND time_domain_waveform IS NOT NULL
            </if>
            <if test="startDate != null">
                AND origin_time &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND origin_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY origin_time
    </select>

    <select id="queryEnergyList" resultType="com.snszyk.zbusiness.basic.dto.SensorDataDTO">
        SELECT id, wave_id, sensor_code, origin_time, energy_value FROM sidas_sensor_data
        <where>
            wave_id = #{waveId} AND invalid = 0
            <if test="startTime != null">
                AND origin_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND origin_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="queryRealRev" resultType="com.snszyk.zbusiness.basic.dto.SensorDataDTO">
        SELECT id, wave_id, sensor_code, `value`, origin_time FROM sidas_sensor_data
        WHERE
            sample_data_type = 'RPM'
        ORDER BY
            ABS( STR_TO_DATE(#{originTime},'%Y-%m-%d %H:%i:%s') - origin_time) ASC
            LIMIT 1
    </select>

</mapper>
