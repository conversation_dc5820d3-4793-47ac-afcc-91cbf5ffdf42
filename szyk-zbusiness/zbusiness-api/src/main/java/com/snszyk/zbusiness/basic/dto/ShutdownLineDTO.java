package com.snszyk.zbusiness.basic.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 停机线
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ShutdownLineDTO对象", description = "停机线")
public class ShutdownLineDTO implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 是否启用根据振动波形有效值判断停机状态
	 */
	@ApiModelProperty(value = "是否启用根据振动波形有效值判断停机状态")
	private Boolean enableRunningByVibration;

	/**
	 * 停机状态最小值
	 */
	@ApiModelProperty(value = "停机状态最小值")
	private Double runningStateMin;

}
