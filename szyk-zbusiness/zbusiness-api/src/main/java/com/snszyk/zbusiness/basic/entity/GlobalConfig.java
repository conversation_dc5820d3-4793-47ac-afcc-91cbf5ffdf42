package com.snszyk.zbusiness.basic.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.snszyk.core.tenant.mp.TenantEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 全局配置表实体类
 *
 * <AUTHOR>
 * @since 2023-06-12
 */
@Data
@Accessors(chain = true)
@TableName("eolm_global_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "GlobalConfig对象", description = "全局配置表")
public class GlobalConfig extends TenantEntity {

	/**
	 * 配置类型
	 */
	@ApiModelProperty(value = "配置类型")
	private String category;

	/**
	 * 配置信息
	 */
	@ApiModelProperty(value = "配置信息")
	private String settings;

	public GlobalConfig(){

	}

	public GlobalConfig(String tenantId, String category, String settings){
		this.setTenantId(tenantId);
		this.category = category;
		this.settings = settings;
	}

}
