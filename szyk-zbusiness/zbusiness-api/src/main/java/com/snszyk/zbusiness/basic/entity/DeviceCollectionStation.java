/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.snszyk.core.mp.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 厂区采集站绑定实体
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("eolm_device_collection_station")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DeviceCollectionStation对象", description = "DeviceCollectionStation对象")
public class DeviceCollectionStation extends BaseEntity {

	/**
	 * 厂区id
	 */
	@NotNull(message = "厂区id不能为空！")
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "厂区id")
	private Long deviceId;

	/**
	 * 采集站id
	 */
	@NotNull(message = "采集站id不能为空！")
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "采集站id")
	private Long stationId;

	/**
	 * 采集站在厂区模型图的坐标
	 */
	@NotBlank(message = "采集站在厂区模型图的坐标不能为空！")
	@ApiModelProperty(value = "采集站在厂区模型图的坐标")
	private String coordinate;

}
