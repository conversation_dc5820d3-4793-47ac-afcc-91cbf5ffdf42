/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.redis.cache.SzykRedis;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.CollectionStationDTO;
import com.snszyk.zbusiness.basic.dto.CollectionStationOnlineStatDTO;
import com.snszyk.zbusiness.basic.dto.StationTotalDTO;
import com.snszyk.zbusiness.basic.entity.CollectionStation;
import com.snszyk.zbusiness.basic.service.ICollectionStationService;
import com.snszyk.zbusiness.basic.service.logic.CollectionStationLogicService;
import com.snszyk.zbusiness.basic.vo.CollectionStationVO;
import com.snszyk.zbusiness.basic.wrapper.CollectionStationWrapper;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;


/**
 * 采集站表 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/collection_station")
@Api(value = "采集站表", tags = "采集站表接口")
@Validated
public class CollectionStationController extends SzykController {

	private final ICollectionStationService collectionStationService;
	private final CollectionStationLogicService logicService;
	private final SzykRedis szykRedis;

	/**
	 * 详情
	 */
	@GetMapping("{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<CollectionStationDTO> detail(@PathVariable Long id) {
		return R.data(logicService.detail(id));
	}

	/**
	 * 自定义分页 采集站表
	 */
	@GetMapping("/page")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "online", value = "状态：0-离线；1-在线", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "tenantId", value = "租户id", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入vo")
	public R<IPage<CollectionStationDTO>> page(@ApiIgnore CollectionStationVO vo, Query query) {
		IPage<CollectionStationDTO> pages = logicService.selectStationPage(Condition.getPage(query), vo);
		return R.data(pages);
	}

	/**
	 * 新增 采集站表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增", notes = "传入vo")
	public R save(@Valid @RequestBody CollectionStationVO vo) {
		return R.status(logicService.save(vo));
	}

	/**
	 * 修改 采集站表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "修改", notes = "传入vo")
	public R update(@Valid @RequestBody CollectionStationVO vo) {
		return R.status(logicService.update(vo));
	}

	/**
	 * 删除 采集站表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(logicService.remove(Func.toLongList(ids)));
	}

	/**
	 * 门户端-采集站数据概览
	 */
	@GetMapping("/onlineStat")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "门户端-采集站数据概览")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "tenantId", value = "租户id", paramType = "query", dataType = "string")
	})
	public R<CollectionStationOnlineStatDTO> onlineStat(@NotBlank String tenantId) {
		return R.data(logicService.onlineStat(tenantId));
	}

	@GetMapping("/onlineTotal")
	@ApiOperation(value = "在线统计")
	public R<StationTotalDTO> stationTotal() {
		StationTotalDTO dto = new StationTotalDTO();
		this.collectionStationService.list().forEach(a -> dto.online(a.getOnline()));
		return R.data(dto);
	}


	/**
	 * 门户端-采集站工作状态概览 采集站表
	 */
	@GetMapping("/portalPage")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "门户端-采集站工作状态概览", notes = "传入collectionStation")
	public R<IPage<CollectionStationVO>> portalPage(@ApiIgnore CollectionStationVO collectionStation, Query query) {
		QueryWrapper<CollectionStation> queryWrapper = Condition.getQueryWrapper(collectionStation);
		queryWrapper.lambda().orderByDesc(CollectionStation::getUpdateTime);
		IPage<CollectionStation> pages = collectionStationService.page(Condition.getPage(query), queryWrapper);
		return R.data(CollectionStationWrapper.build().pageVO(pages));
	}

	/**
	 * 导出列表Excel 采集站表
	 */
	@GetMapping("/exportExcel")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "门户端-采集站工作状态概览一键导出", notes = "传入collectionStation")
	public void exportExcel(@ApiIgnore CollectionStationVO collectionStation, HttpServletResponse response) {
		collectionStationService.exportExcel(collectionStation, response);
		//return R.success("操作成功");
	}

}
