/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.wrapper;

import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.mp.support.BaseEntityWrapper;
import com.snszyk.core.tool.utils.BeanUtil;
import com.snszyk.system.cache.DictBizCache;
import com.snszyk.zbusiness.basic.entity.Equipment;
import com.snszyk.zbusiness.basic.vo.EquipmentVO;

import java.util.Objects;

/**
 * 设备信息表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class EquipmentWrapper extends BaseEntityWrapper<Equipment, EquipmentVO> {

	public static EquipmentWrapper build() {
		return new EquipmentWrapper();
	}

	@Override
	public EquipmentVO entityVO(Equipment equipment) {
		EquipmentVO equipmentVO = Objects.requireNonNull(BeanUtil.copy(equipment, EquipmentVO.class));
		String categoryName = DictBizCache.getValue(DictBizEnum.EQ_CATEGORY, equipment.getCategory());
		equipmentVO.setCategoryName(categoryName);
		return equipmentVO;
	}

}
