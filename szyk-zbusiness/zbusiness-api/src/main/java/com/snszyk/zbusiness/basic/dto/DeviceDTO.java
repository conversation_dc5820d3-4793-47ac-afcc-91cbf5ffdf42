/*
 *      Copyright (c) 2018-2088
 */
package com.snszyk.zbusiness.basic.dto;

import com.snszyk.resource.entity.Attach;
import com.snszyk.zbusiness.basic.entity.Device;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeviceDTO extends Device {
	private static final long serialVersionUID = 1L;

	/**
	 * 父级
	 */
	@ApiModelProperty(value = "父级")
	private Device parent;

	/**
	 * 设备总数
	 */
	@ApiModelProperty(value = "设备总数")
	private Integer eqSum;

	/**
	 * 附件列表
	 */
	@ApiModelProperty(value = "附件列表")
	private List<Attach> imageList;

	/**
	 * 厂区绑定的采集站列表
	 */
	@ApiModelProperty(value = "厂区绑定的采集站列表")
	private List<DeviceCollectionStationDTO> collectionStationList;

	/**
	 * 层级
	 */
	@ApiModelProperty(value = "层级")
	private Integer level;

}
