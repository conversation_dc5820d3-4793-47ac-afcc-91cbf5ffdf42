/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.secure.SzykUser;
import com.snszyk.core.secure.utils.AuthUtil;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.service.IGlobalConfigService;
import com.snszyk.zbusiness.basic.vo.GlobalConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 全局配置表 控制器
 *
 * <AUTHOR>
 * @since 2023-06-12
 */
@RestController
@AllArgsConstructor
@RequestMapping("/globalConfig")
@Api(value = "全局配置表", tags = "全局配置表接口")
public class GlobalConfigController extends SzykController {

	private final IGlobalConfigService globalConfigService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入")
	public R<GlobalConfigVO> detail(@RequestParam(name = "tenantId", required = false) String tenantId) {
		if(Func.isEmpty(tenantId)){
			tenantId= AuthUtil.getTenantId();
		}
		return R.data(globalConfigService.detail(tenantId));
	}

	/**
	 * 新增或修改 全局配置表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "新增或修改", notes = "传入globalConfig")
	public R submit(@RequestBody @Valid  GlobalConfigVO globalConfig) {
		return R.status(globalConfigService.submit(globalConfig));
	}

}
