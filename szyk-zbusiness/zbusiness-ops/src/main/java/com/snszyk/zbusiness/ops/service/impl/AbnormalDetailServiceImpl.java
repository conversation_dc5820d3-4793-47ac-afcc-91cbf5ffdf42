/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.zbusiness.ops.entity.AbnormalDetail;
import com.snszyk.zbusiness.ops.mapper.AbnormalDetailMapper;
import com.snszyk.zbusiness.ops.service.IAbnormalDetailService;
import com.snszyk.zbusiness.ops.vo.AbnormalDetailVO;
import org.springframework.stereotype.Service;

/**
 * 设备异常详情表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-22
 */
@Service
public class AbnormalDetailServiceImpl extends ServiceImpl<AbnormalDetailMapper, AbnormalDetail> implements IAbnormalDetailService {

	@Override
	public IPage<AbnormalDetailVO> page(IPage<AbnormalDetailVO> page, AbnormalDetailVO abnormalDetail) {
		return page.setRecords(baseMapper.page(page, abnormalDetail));
	}

}
