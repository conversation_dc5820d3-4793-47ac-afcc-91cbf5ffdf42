/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.basic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.snszyk.core.boot.ctrl.SzykController;
import com.snszyk.core.mp.support.Condition;
import com.snszyk.core.mp.support.Query;
import com.snszyk.core.tool.api.R;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.zbusiness.basic.dto.SensorTypeDTO;
import com.snszyk.zbusiness.basic.service.logic.SensorTypeLogicService;
import com.snszyk.zbusiness.basic.vo.SensorTypeVO;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;


/**
 * 传感器类型表 控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sensorType")
@Api(value = "传感器类型表", tags = "传感器类型接口")
public class SensorTypeController extends SzykController {

	private final SensorTypeLogicService logicService;

	/**
	 * 详情
	 */
	@GetMapping("{id}")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入id")
	public R<SensorTypeDTO> detail(@PathVariable Long id) {
		return R.data(logicService.detail(id));
	}

	/**
	 * 分页 传感器类型表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 2)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "nameOrModel", value = "传感器名称或型号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "supplier", value = "生产厂家", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "category", value = "传感器类型", paramType = "query", dataType = "int"),
		@ApiImplicitParam(name = "isWireless", value = "有线无线", paramType = "query", dataType = "int")

	})
	@ApiOperation(value = "分页", notes = "传入sensorTypeVO")
	public R<IPage<SensorTypeDTO>> page(@ApiIgnore SensorTypeVO vo, Query query) {
		IPage<SensorTypeDTO> pages = logicService.page(Condition.getPage(query), vo);
		return R.data(pages);
	}

	/**
	 * 新增或修改 传感器类型表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "新增或修改", notes = "传入sensorType")
	public R<Boolean> submit(@Valid @RequestBody SensorTypeVO sensorType) {
		return R.status(logicService.submit(sensorType));
	}

	/**
	 * 带校验的删除 传感器类型表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "带校验的删除", notes = "传入ids")
	public R<Boolean> remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(logicService.removeSensor(Func.toLongList(ids)));
	}

}
