/*
 *      Copyright (c) 2018-2028
 */
package com.snszyk.zbusiness.ops.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.snszyk.common.enums.DictBizEnum;
import com.snszyk.core.tool.utils.Func;
import com.snszyk.system.entity.DictBiz;
import com.snszyk.system.service.IDictBizService;
import com.snszyk.zbusiness.basic.enums.AlarmIndexEnum;
import com.snszyk.zbusiness.ops.entity.AlarmRecord;
import com.snszyk.zbusiness.ops.enums.AlarmBizTypeEnum;
import com.snszyk.zbusiness.ops.mapper.AlarmRecordMapper;
import com.snszyk.zbusiness.ops.service.IAlarmRecordService;
import com.snszyk.zbusiness.ops.vo.AlarmRecordVO;
import com.snszyk.zbusiness.ops.wrapper.AlarmRecordWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报警记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-15
 */
@AllArgsConstructor
@Service
public class AlarmRecordServiceImpl extends ServiceImpl<AlarmRecordMapper, AlarmRecord> implements IAlarmRecordService {

//	private final IDictBizClient dictBizClient;
	private final IDictBizService dictBizService;


	@Override
	public IPage<AlarmRecordVO> page(IPage<AlarmRecordVO> page, AlarmRecordVO alarmRecord) {
		List<AlarmRecordVO> list = baseMapper.page(page, alarmRecord);
		if (Func.isNotEmpty(list)) {
			list.forEach(alarmRecordVO -> {
				DictBiz dictBiz = dictBizService.getDictValue(DictBizEnum.ALARM_LEVEL.getName(), Func.toStr(alarmRecordVO.getAlarmLevel()));
				if (Func.isNotEmpty(dictBiz)) {
					alarmRecordVO.setAlarmLevelName(dictBiz.getDictValue());
				}
				if(AlarmBizTypeEnum.THRESHOLD == AlarmBizTypeEnum.getByCode(alarmRecordVO.getAlarmType())){
					alarmRecordVO.setAlarmValue(String.format("%.3f", alarmRecordVO.getVal()))
						.setAlarmIndexName(AlarmIndexEnum.getByCode(alarmRecordVO.getAlarmIndex()).getName());
				}
			});
		}
		return page.setRecords(list);
	}

	@Override
	public List<AlarmRecordVO> getAlarmRecordList(Long alarmId, Integer alarmType) {
		List<AlarmRecord> list = this.list(Wrappers.<AlarmRecord>query().lambda()
			.eq(AlarmRecord::getAlarmId, alarmId).eq(AlarmRecord::getAlarmType, alarmType)
			.orderByDesc(AlarmRecord::getAlarmTime));
		List<AlarmRecord> dataList = new ArrayList<>();
		Map<String, List<AlarmRecord>> recordMap = list.stream()
			.collect(Collectors.groupingBy(record -> record.getMonitorId() + record.getAlarmType() + record.getAlarmIndex()));
		if(Func.isNotEmpty(recordMap)){
			recordMap.forEach((key, value) -> {
				dataList.add(value.get(0));
			});
		}
		return AlarmRecordWrapper.build().listVO(dataList);
	}

}
